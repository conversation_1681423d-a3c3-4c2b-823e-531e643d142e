/**
 * Contexte pour la gestion des messages directs (pings)
 * Fournit des fonctions pour envoyer, recevoir et marquer les pings comme lus
 */

import React, { createContext, useState, useContext, ReactNode, useEffect, useRef } from 'react';
import { Ping } from '@/types';
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

import { useProfileContext } from "./ProfileContext";
import { handleError, ErrorCode, createAppError } from "@/services/errorService";
import { logCreate, logRead, logUpdate } from "@/utils/secure-logging";
import { sendDesktopNotification } from "@/utils/notifications";
import { createPing, getUserPings, markPingAsRead } from "@/api/supabase";
import { useSupabaseSubscription, onInsert, onUpdate } from '@/hooks/useSupabaseSubscription';

interface PingContextProps {
  pings: Ping[];
  loading: boolean;
  unreadCount: number;
  sendPing: (toUserId: string, message: string) => Promise<boolean>;
  getPings: () => Promise<void>;
  markPingRead: (pingId: string) => Promise<boolean>;
}

const PingContext = createContext<PingContextProps | undefined>(undefined);

/**
 * Provider pour le contexte des pings
 */
export const PingProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [pings, setPings] = useState<Ping[]>([]);
  const [loading, setLoading] = useState(false);
  const [unreadCount, setUnreadCount] = useState(0);

  // Variable pour stocker le dernier ID de ping vu
  const lastSeenPingIdRef = useRef<string | null>(null);

  const { currentProfile } = useProfileContext();

  /**
   * Récupère les pings pour l'utilisateur courant
   */
  const getPings = async () => {
    if (!currentProfile) {
      setPings([]);
      setUnreadCount(0);
      return;
    }

    try {
      setLoading(true);

      // Journaliser l'opération
      logRead('pings', null, currentProfile.id, 'Récupération des pings');

      // Récupérer les pings
      const userPings = await getUserPings(currentProfile.id);

      if (!userPings) {
        throw createAppError(
          'Erreur lors de la récupération des pings',
          ErrorCode.DB_QUERY_ERROR
        );
      }

      setPings(userPings);

      // Compter les pings non lus
      const unread = userPings.filter(p => !p.read).length;
      setUnreadCount(unread);

      // Vérifier s'il y a de nouveaux pings non lus
      if (unread > 0) {
        const latestPing = userPings.find(p => !p.read);

        // Si le dernier ping est plus récent que le dernier vu
        if (latestPing && latestPing.id !== lastSeenPingIdRef.current) {
          // Envoyer une notification
          sendDesktopNotification(
            `Nouveau message de ${latestPing.from_user?.name || 'Un utilisateur'}`,
            latestPing.message,
            '/lovable-uploads/wema-logo.png'
          );

          // Mettre à jour le dernier ping vu
          lastSeenPingIdRef.current = latestPing.id;
        }
      }

    } catch (error) {
      handleError(error, 'PingContext.getPings');
    } finally {
      setLoading(false);
    }
  };

  /**
   * Envoie un ping à un utilisateur
   * @param toUserId - ID du destinataire
   * @param message - Message à envoyer
   * @returns true si l'envoi a réussi, false sinon
   */
  const sendPing = async (toUserId: string, message: string): Promise<boolean> => {
    if (!currentProfile) return false;

    try {
      // Journaliser l'opération
      logCreate('pings', null, currentProfile.id, 'Envoi d\'un message direct');

      // Envoyer le message
      const success = await createPing(currentProfile.id, toUserId, message);

      if (!success) {
        throw createAppError(
          'Impossible d\'envoyer le message',
          ErrorCode.DB_QUERY_ERROR
        );
      }

      toast.success("Message envoyé");

      // Rafraîchir immédiatement les pings si l'expéditeur et le destinataire sont le même utilisateur
      if (toUserId === currentProfile.id) {
        await getPings();
      }

      return true;
    } catch (error) {
      handleError(error, 'PingContext.sendPing');
      return false;
    }
  };

  /**
   * Marque un ping comme lu
   * @param pingId - ID du ping
   * @returns true si la mise à jour a réussi, false sinon
   */
  const markPingRead = async (pingId: string): Promise<boolean> => {
    if (!currentProfile) return false;

    try {
      // Journaliser l'opération
      logUpdate('pings', pingId, currentProfile.id, 'Marquer un ping comme lu');

      // Marquer le ping comme lu
      const success = await markPingAsRead(pingId, currentProfile.id);

      if (!success) {
        throw createAppError(
          'Impossible de marquer le message comme lu',
          ErrorCode.DB_QUERY_ERROR
        );
      }

      // Mettre à jour l'état local
      setPings(prev =>
        prev.map(p => p.id === pingId ? { ...p, read: true } : p)
      );

      // Mettre à jour le compteur de pings non lus
      setUnreadCount(prev => Math.max(0, prev - 1));

      return true;
    } catch (error) {
      handleError(error, 'PingContext.markPingRead');
      return false;
    }
  };



  // Effet pour charger les pings initiaux
  useEffect(() => {
    if (!currentProfile) return;

    // Charger les pings initiaux
    getPings();

    // Configurer un intervalle pour rafraîchir périodiquement les pings
    const interval = setInterval(() => {
      getPings();
    }, 30000); // Toutes les 30 secondes

    // Nettoyer l'intervalle lors du démontage
    return () => {
      clearInterval(interval);
    };
  }, [currentProfile]);

  // Utiliser le hook de souscription pour les pings
  useSupabaseSubscription(
    'pings',
    [
      // S'abonner aux nouveaux pings destinés au profil courant
      onInsert('pings', (payload) => {
        if (currentProfile && payload.new && payload.new.to_user_id === currentProfile.id) {
          getPings();
        }
      }),

      // S'abonner aux mises à jour de pings concernant le profil courant
      onUpdate('pings', (payload) => {
        if (currentProfile && payload.new &&
            (payload.new.to_user_id === currentProfile.id || payload.new.from_user_id === currentProfile.id)) {
          getPings();
        }
      })
    ]
  );

  return (
    <PingContext.Provider
      value={{
        pings,
        loading,
        unreadCount,
        sendPing,
        getPings,
        markPingRead
      }}
    >
      {children}
    </PingContext.Provider>
  );
};

/**
 * Hook pour utiliser le contexte des pings
 * @returns Contexte des pings
 * @throws {Error} Si utilisé en dehors d'un PingProvider
 */
export const usePingContext = () => {
  const context = useContext(PingContext);
  if (context === undefined) {
    throw new Error('usePingContext must be used within a PingProvider');
  }
  return context;
};
