/**
 * Service pour la gestion des erreurs
 * Fournit des fonctions pour traiter et journaliser les erreurs de manière cohérente
 */

import { AppError } from "@/types";
import { toast } from "sonner";

// Codes d'erreur pour les différentes catégories
export enum ErrorCode {
  // Erreurs d'authentification
  AUTH_ERROR = "AUTH_ERROR",

  // Erreurs de base de données
  DB_CONNECTION_ERROR = "DB_CONNECTION_ERROR",
  DB_QUERY_ERROR = "DB_QUERY_ERROR",
  DB_CONSTRAINT_ERROR = "DB_CONSTRAINT_ERROR",

  // Erreurs de chiffrement
  ENCRYPTION_ERROR = "ENCRYPTION_ERROR",
  DECRYPTION_ERROR = "DECRYPTION_ERROR",

  // Erreurs de validation
  VALIDATION_ERROR = "VALIDATION_ERROR",

  // Erreurs réseau
  NETWORK_ERROR = "NETWORK_ERROR",

  // Erreurs inconnues
  UNKNOWN_ERROR = "UNKNOWN_ERROR"
}

/**
 * Crée une erreur d'application avec un code et des détails
 * @param message Message d'erreur
 * @param code Code d'erreur
 * @param details Détails supplémentaires
 * @returns Erreur d'application
 */
export function createAppError(
  message: string,
  code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
  details?: string
): AppError {
  const error = new Error(message) as AppError;
  error.code = code;
  error.details = details;
  return error;
}

/**
 * Journalise une erreur dans la console
 * @param error Erreur à journaliser
 * @param context Contexte de l'erreur
 */
export function logError(error: Error | AppError, context?: string): void {
  const appError = error as AppError;

  if (context) {
    console.error(`[${context}] ${error.message}`);
  } else {
    console.error(error.message);
  }

  if (appError.code) {
    console.error(`Code: ${appError.code}`);
  }

  if (appError.details) {
    console.error(`Détails: ${appError.details}`);
  }

  console.error(error.stack);
}

/**
 * Affiche une notification d'erreur à l'utilisateur
 * @param error Erreur à afficher
 * @param fallbackMessage Message à afficher si l'erreur n'a pas de message
 */
export function showErrorToast(
  error: Error | AppError | string,
  fallbackMessage: string = "Une erreur est survenue"
): void {
  let message: string;

  if (typeof error === 'string') {
    message = error;
  } else {
    message = error.message || fallbackMessage;
  }

  toast.error(message);
}

/**
 * Gère une erreur de manière complète (journalisation + notification)
 * @param error Erreur à gérer
 * @param context Contexte de l'erreur
 * @param showToast Afficher une notification à l'utilisateur
 * @param fallbackMessage Message à afficher si l'erreur n'a pas de message
 */
export function handleError(
  error: Error | AppError | string,
  context?: string,
  showToast: boolean = true,
  fallbackMessage?: string
): void {
  // Journaliser l'erreur
  if (typeof error !== 'string') {
    logError(error, context);
  } else {
    console.error(`[${context || 'ERROR'}] ${error}`);
  }

  // Afficher une notification si demandé
  if (showToast) {
    showErrorToast(error, fallbackMessage);
  }
}

/**
 * Analyse une erreur Supabase et la convertit en AppError
 * @param error Erreur Supabase
 * @returns AppError
 */
export function parseSupabaseError(error: any): AppError {
  let code = ErrorCode.UNKNOWN_ERROR;
  let message = "Une erreur est survenue lors de l'opération";

  if (error.code) {
    // Codes d'erreur PostgreSQL
    if (error.code.startsWith('23')) {
      code = ErrorCode.DB_CONSTRAINT_ERROR;
      message = "Erreur de contrainte dans la base de données";
    } else if (error.code.startsWith('28')) {
      code = ErrorCode.AUTH_ERROR;
      message = "Erreur d'authentification";
    } else if (error.code.startsWith('08')) {
      code = ErrorCode.DB_CONNECTION_ERROR;
      message = "Erreur de connexion à la base de données";
    }
  }

  if (error.message) {
    message = error.message;
  }

  return createAppError(message, code, error.details || error.hint);
}
