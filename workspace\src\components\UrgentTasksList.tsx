
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { StatusBadge } from './StatusBadge';
import { Client, Step } from '@/types';
import { AlertCircle, ArrowRight } from 'lucide-react';
import { Link } from 'react-router-dom';

interface UrgentTasksListProps {
  urgentTasks: { client: Client; step: Step }[];
}

export const UrgentTasksList: React.FC<UrgentTasksListProps> = ({ urgentTasks }) => {
  if (urgentTasks.length === 0) return null;

  return (
    <Card className="mb-8 border-pink-200 shadow-md">
      <CardHeader className="bg-pink-50 pb-2">
        <CardTitle className="text-xl font-bold text-pink-700 flex items-center gap-2">
          <div className="p-1 bg-pink-100 rounded-full">
            <AlertCircle className="h-5 w-5 text-pink-600" />
          </div>
          Tâches Urgentes
        </Card<PERSON><PERSON><PERSON>>
      </CardHeader>
      <CardContent className="py-4">
        <ul className="space-y-2">
          {urgentTasks.map((item) => (
            <li key={`${item.client.id}-${item.step.id}`} className="border-b border-gray-100 pb-2 last:border-0">
              <Link 
                to={`/client/${item.client.id}`}
                className="flex justify-between items-center hover:bg-gray-50 p-2 rounded-md group transition-colors"
              >
                <div className="flex flex-col">
                  <span className="font-medium">{item.client.name}</span>
                  <span className="text-sm text-gray-600">{item.step.name}</span>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-pink-500 transition-colors" />
              </Link>
            </li>
          ))}
        </ul>
      </CardContent>
    </Card>
  );
};
