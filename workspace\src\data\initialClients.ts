
import { v4 as uuidv4 } from 'uuid';
import { Client } from '../types';
import { defaultSteps } from './defaultSteps';

const generateDefaultSteps = () => {
  return defaultSteps.map(step => ({
    id: uuidv4(),
    name: step,
    status: 'manquant' as const,
    receivedDate: null,
    comment: ''
  }));
};

export const initialClients: Client[] = [
  {
    id: uuidv4(),
    name: '<PERSON>',
    steps: [
      {
        id: uuidv4(),
        name: "Document d'entrée en relation",
        status: 'valide',
        receivedDate: '2025-04-10',
        comment: 'Conforme'
      },
      {
        id: uuidv4(),
        name: "Document de Connaissance Client",
        status: 'manquant',
        receivedDate: null,
        comment: 'Relancer le client'
      },
      {
        id: uuidv4(),
        name: "Profil investisseur",
        status: 'transmis',
        receivedDate: '2025-04-08',
        comment: 'En attente de validation'
      },
      {
        id: uuidv4(),
        name: "Lettre de mission",
        status: 'validation',
        receivedDate: '2025-04-09',
        comment: 'En cours de signature'
      }
    ]
  },
  {
    id: uuidv4(),
    name: '<PERSON>',
    steps: generateDefaultSteps()
  },
  {
    id: uuidv4(),
    name: 'Jean Moreau',
    steps: generateDefaultSteps()
  }
];
