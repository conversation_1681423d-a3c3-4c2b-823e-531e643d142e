/**
 * Hook personnalisé pour gérer les souscriptions Supabase de manière simplifiée
 * Fournit une interface simple pour s'abonner aux changements de tables
 */

import { useEffect, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { RealtimeChannel, RealtimePostgresChangesPayload } from '@supabase/supabase-js';

type SubscriptionEvent = 'INSERT' | 'UPDATE' | 'DELETE' | '*';
type SubscriptionHandler = (payload: RealtimePostgresChangesPayload<any>) => void;

interface Subscription {
  table: string;
  event: SubscriptionEvent;
  handler: SubscriptionHandler;
  filter?: string;
  filterValue?: string;
}

/**
 * Hook simplifié pour gérer les souscriptions Supabase
 * @param subscriptionKey - Clé unique pour identifier le canal
 * @param subscriptions - Liste des souscriptions à configurer
 */
export function useSupabaseSubscription(
  subscriptionKey: string,
  subscriptions: Subscription[]
) {
  const channelRef = useRef<RealtimeChannel | null>(null);
  const isMountedRef = useRef(true);

  useEffect(() => {
    isMountedRef.current = true;

    // Créer un canal unique pour cette clé
    const channelName = `realtime-${subscriptionKey}-channel`;
    console.log(`🔧 Configuration du canal ${channelName}...`);

    try {
      // Nettoyer le canal existant si nécessaire
      if (channelRef.current) {
        supabase.removeChannel(channelRef.current);
        channelRef.current = null;
      }

      // Créer le nouveau canal
      const channel = supabase.channel(channelName);
      channelRef.current = channel;

      // Ajouter toutes les souscriptions
      subscriptions.forEach(subscription => {
        const { table, event, handler, filter, filterValue } = subscription;

        const options: any = {
          event,
          schema: 'public',
          table
        };

        if (filter && filterValue) {
          options.filter = `${filter}=eq.${filterValue}`;
        }

        channel.on('postgres_changes', options, handler);
      });

      // S'abonner au canal
      channel.subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          console.log(`✅ Canal ${channelName} connecté`);
        } else if (status === 'CLOSED') {
          console.log(`⚠️ Canal ${channelName} fermé`);
        } else if (status === 'CHANNEL_ERROR') {
          console.error(`❌ Erreur canal ${channelName}`);
        }
      });

    } catch (error) {
      console.error(`Erreur lors de la configuration du canal ${channelName}:`, error);
    }

    // Nettoyage
    return () => {
      isMountedRef.current = false;
      if (channelRef.current) {
        console.log(`🧹 Nettoyage du canal ${channelName}`);
        try {
          supabase.removeChannel(channelRef.current);
        } catch (e) {
          console.warn(`Erreur lors du nettoyage:`, e);
        }
        channelRef.current = null;
      }
    };
  }, [subscriptionKey]); // Ne se reconfigurer que si la clé change
}

/**
 * Crée une souscription pour les insertions dans une table
 */
export function onInsert(
  table: string,
  handler: SubscriptionHandler,
  filter?: string,
  filterValue?: string
): Subscription {
  return {
    table,
    event: 'INSERT',
    handler,
    filter,
    filterValue
  };
}

/**
 * Crée une souscription pour les mises à jour dans une table
 */
export function onUpdate(
  table: string,
  handler: SubscriptionHandler,
  filter?: string,
  filterValue?: string
): Subscription {
  return {
    table,
    event: 'UPDATE',
    handler,
    filter,
    filterValue
  };
}

/**
 * Crée une souscription pour les suppressions dans une table
 */
export function onDelete(
  table: string,
  handler: SubscriptionHandler,
  filter?: string,
  filterValue?: string
): Subscription {
  return {
    table,
    event: 'DELETE',
    handler,
    filter,
    filterValue
  };
}

/**
 * Crée une souscription pour tous les événements dans une table
 */
export function onAll(
  table: string,
  handler: SubscriptionHandler,
  filter?: string,
  filterValue?: string
): Subscription {
  return {
    table,
    event: '*',
    handler,
    filter,
    filterValue
  };
}
