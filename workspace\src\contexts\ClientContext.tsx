/**
 * Contexte pour la gestion des clients et des étapes
 * Fournit des fonctions pour créer, modifier et supprimer des clients et des étapes
 */

import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import { Client, Step, Status, Assignee, SupabaseClient, SupabaseStep, SupabaseAssignee, SupabaseStepAssignment } from '@/types';
import { defaultSteps } from '@/data/defaultSteps';
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

import { useProfileContext } from "./ProfileContext";
import { useNotificationContext } from "./NotificationContext";
import { handleError, ErrorCode, createAppError } from "@/services/errorService";
import { logCreate, logRead, logUpdate, logDelete } from "@/utils/secure-logging";
import { purgeAllExpiredData } from "@/utils/data-retention";
import { calculateMultipleClientPatrimoine } from "@/utils/patrimoine";

import { assignDefaultUsersToStep } from "@/services/configService";
import { encryptText, safeDecrypt } from "@/utils/encryption";


import { useSupabaseSubscription, onInsert, onUpdate, onDelete } from '@/hooks/useSupabaseSubscription';

/**
 * Interface pour le contexte des clients
 */
interface ClientContextProps {
  clients: Client[];
  loading: boolean;
  addClient: (name: string) => Promise<void>;
  deleteClient: (id: string) => Promise<void>;
  getClient: (id: string) => Client | undefined;
  updateClientName: (id: string, name: string) => Promise<void>;
  updateClientDueDate: (id: string, dueDate: string | null) => Promise<void>;
  updateClientCompleted: (id: string, completed: boolean) => Promise<void>;
  toggleClientCompleted: (id: string) => Promise<void>;
  getComplianceProgress: (clientId: string) => { completed: number; total: number; percentage: number };
  addStep: (clientId: string, stepName: string) => Promise<void>;
  deleteStep: (clientId: string, stepId: string) => Promise<void>;
  updateStepStatus: (clientId: string, stepId: string, status: Status) => Promise<void>;
  updateStepDate: (clientId: string, stepId: string, date: string | null) => Promise<void>;
  updateStepComment: (clientId: string, stepId: string, comment: string) => Promise<void>;
  duplicateClientTemplate: (clientId: string) => Promise<void>;
  searchClients: (query: string) => Client[];
  getClientProgress: (client: Client) => { completed: number; total: number };
  getUrgentSteps: () => { client: Client; step: Step }[];
  getAssignees: () => Promise<Assignee[]>;
  assignStepToUser: (clientId: string, stepId: string, assigneeId: string) => Promise<void>;
  removeStepAssignment: (clientId: string, stepId: string, assigneeId: string) => Promise<void>;
  updateStepName: (clientId: string, stepId: string, name: string) => Promise<void>;
}

const ClientContext = createContext<ClientContextProps | undefined>(undefined);

/**
 * Provider pour le contexte des clients
 */
export const ClientProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [clients, setClients] = useState<Client[]>([]);
  const [loading, setLoading] = useState(true);
  const [complianceCache, setComplianceCache] = useState<Map<string, { completed: number; total: number; percentage: number; lastUpdated: number }>>(new Map());

  const { currentProfile } = useProfileContext();

  const { createNotification } = useNotificationContext();



  const mapSupabaseDataToClients = async (clientsData: SupabaseClient[], stepsData: SupabaseStep[]): Promise<Client[]> => {
    // Journaliser l'opération de lecture
    logRead('clients', null, currentProfile?.id, 'Chargement des données clients');

    const { data: assignmentsData, error: assignmentsError } = await supabase
      .from('step_assignments')
      .select('*');

    if (assignmentsError) {
      console.error("Error fetching assignments:", assignmentsError);
      return [];
    }

    const { data: assigneesData, error: assigneesError } = await supabase
      .from('assignees')
      .select('*');

    if (assigneesError) {
      console.error("Error fetching assignees:", assigneesError);
      return [];
    }

    const assignees = assigneesData as SupabaseAssignee[];
    const assignments = assignmentsData as SupabaseStepAssignment[];

    // Traiter les données client par client
    const mappedClients = await Promise.all(clientsData.map(async client => {
      // Déchiffrer le nom du client de manière sécurisée
      const clientName = await safeDecrypt(client.name);

      // Filtrer les étapes pour ce client
      const clientSteps = stepsData.filter(step => step.client_id === client.id);

      // Traiter les étapes avec déchiffrement des commentaires
      const processedSteps = await Promise.all(clientSteps.map(async step => {
        // Déchiffrer le commentaire de manière sécurisée
        const processedComment = await safeDecrypt(step.comment || '');

        // Récupérer les assignations pour cette étape
        const stepAssignments = assignments
          .filter(assignment => assignment.step_id === step.id)
          .map(assignment => {
            const assignee = assignees.find(a => a.id === assignment.assignee_id);
            if (!assignee) return undefined;

            return {
              id: assignee.id,
              name: assignee.name,
              createdAt: assignee.created_at
            };
          });

        // Filtrer les assignations nulles
        const filteredStepAssignments = stepAssignments.filter(Boolean) as Assignee[];

        // Formater la date si elle existe
        const formattedDate = step.received_date
          ? new Date(step.received_date).toISOString().split('T')[0]
          : step.received_date;

        // Utiliser directement le nom de l'étape
        const stepName = step.name;

        // Retourner l'étape avec les données déchiffrées
        return {
          id: step.id,
          name: stepName,
          status: step.status,
          receivedDate: formattedDate,
          comment: processedComment,
          assignees: filteredStepAssignments
        };
      }));

      // Formater la date de rendu si elle existe
      const formattedDueDate = client.due_date
        ? new Date(client.due_date).toISOString().split('T')[0]
        : null;

      // Retourner le client avec ses étapes traitées
      return {
        id: client.id,
        name: clientName, // Nom déchiffré
        dueDate: formattedDueDate, // Date de rendu du dossier
        completed: client.completed || false, // Statut de completion manuelle
        steps: processedSteps
      };
    }));

    return mappedClients;
  };

  // Fonction pour purger les données expirées selon les durées de conservation RGPD
  const purgeExpiredData = async () => {
    try {
      console.log('⚠️ Purge automatique des données expirées temporairement désactivée');

      // Temporairement désactivé à cause des erreurs 404 liées aux fonctions manquantes en base
      // TODO: Réactiver quand les fonctions PostgreSQL seront disponibles
      /*
      console.log('Purge automatique des données expirées selon les durées de conservation RGPD...');

      // Journaliser l'opération
      logDelete('multiple', null, currentProfile?.id, 'Purge automatique des données expirées (RGPD)');

      // Exécuter la purge
      const result = await purgeAllExpiredData();

      console.log('Résultat de la purge automatique:', result);

      // Si des données ont été purgées, rafraîchir les données
      const totalPurged = Object.values(result).reduce((sum, count) => sum + count, 0);
      if (totalPurged > 0) {
        console.log(`${totalPurged} enregistrements ont été purgés, rafraîchissement des données...`);
        // Pas besoin de notification à l'utilisateur pour cette opération en arrière-plan
      }
      */
    } catch (error) {
      console.error('Erreur lors de la purge automatique des données expirées:', error);
      // Ne pas afficher d'erreur à l'utilisateur pour cette opération en arrière-plan
    }
  };

  // Les scripts de migration ont été supprimés car ils ont terminé leur travail

  /**
   * Fonction pour recharger les données depuis la base de données
   * Utilisée à la fois au chargement initial et lors des mises à jour temps réel
   */
  const loadClientsData = async (showLoading: boolean = true) => {
    try {
      if (showLoading) setLoading(true);

      // Journaliser l'opération
      logRead('clients', null, currentProfile?.id, 'Chargement des clients');

      // Récupérer les clients avec la date de rendu et le statut de completion
      const { data: clientsData, error: clientsError } = await supabase
        .from('clients')
        .select('id, name, due_date, completed, created_at');

      if (clientsError) {
        throw createAppError(
          `Erreur lors du chargement des clients: ${clientsError.message}`,
          ErrorCode.DB_QUERY_ERROR
        );
      }

      // Récupérer les étapes
      const { data: stepsData, error: stepsError } = await supabase
        .from('steps')
        .select('*');

      if (stepsError) {
        throw createAppError(
          `Erreur lors du chargement des étapes: ${stepsError.message}`,
          ErrorCode.DB_QUERY_ERROR
        );
      }

      if (clientsData.length === 0) {
        setClients([]);
      } else {
        const mappedClients = await mapSupabaseDataToClients(
          clientsData as any[],
          stepsData as SupabaseStep[]
        );

        // Charger le patrimoine pour tous les clients
        const clientIds = mappedClients.map(client => client.id);
        const patrimoineMap = await calculateMultipleClientPatrimoine(clientIds);

        // Ajouter le patrimoine aux clients
        const clientsWithPatrimoine = mappedClients.map(client => ({
          ...client,
          totalPatrimoine: patrimoineMap.get(client.id) || 0
        }));

        setClients(clientsWithPatrimoine);
      }

      // Exécuter la purge des données expirées au démarrage (seulement lors du chargement initial)
      if (showLoading) {
        purgeExpiredData();

        // Réparation automatique désactivée pour éviter les remplacements intempestifs
        console.log('ℹ️ Réparation automatique désactivée');
      }
    } catch (error) {
      handleError(error, 'ClientContext.loadClientsData');
    } finally {
      if (showLoading) setLoading(false);
    }
  };

  // Hook pour forcer le rechargement quand le contexte des profils finit de charger
  useEffect(() => {
    // Si le currentProfile change ou devient disponible, on déclenche le chargement
    if (currentProfile) {
      console.log('🔄 Profil détecté, chargement des données clients...');
      loadClientsData(true);
    } else {
      console.log('⚠️ Aucun profil sélectionné, arrêt du loading');
      setLoading(false);
      setClients([]);
    }
  }, [currentProfile]);

  const addClient = async (name: string) => {
    try {
      // Chiffrer le nom du client pour protéger les données sensibles
      const encryptedName = await encryptText(name);
      console.log('Nom du client chiffré pour protection des données sensibles');

      const { data: newClient, error: clientError } = await supabase
        .from('clients')
        .insert({ name: encryptedName })
        .select()
        .single();

      if (clientError) throw clientError;

      // Créer les étapes par défaut (plus de chiffrement)
      const stepsToInsert = defaultSteps.map(stepName => ({
        client_id: newClient.id,
        name: stepName,
        status: 'manquant' as Status,
        received_date: null,
        comment: ''
      }));

      const { data: newSteps, error: stepsError } = await supabase
        .from('steps')
        .insert(stepsToInsert)
        .select();

      if (stepsError) throw stepsError;

      // Mettre à jour l'interface IMMÉDIATEMENT pour la réactivité
      const clientWithSteps: Client = {
        id: newClient.id,
        name: name, // Utiliser le nom en clair pour l'affichage local
        dueDate: null, // Pas de date de rendu par défaut
        completed: false, // Nouveau client non terminé par défaut
        steps: (newSteps as SupabaseStep[]).map((step, index) => ({
          id: step.id,
          name: step.name, // Utiliser le nom direct de l'étape
          status: step.status,
          receivedDate: step.received_date ? new Date(step.received_date).toISOString().split('T')[0] : null,
          comment: step.comment || '',
          assignees: [] // Sera mis à jour par le système temps réel après l'assignation
        }))
      };

      setClients(prev => [...prev, clientWithSteps]);
      toast(`${name} a été ajouté avec succès`);

      // ASSIGNATION AUTOMATIQUE EN ARRIÈRE-PLAN (non-bloquant)
      // L'utilisateur voit déjà le client, l'assignation se fait en arrière-plan
      console.log('Assignation automatique des utilisateurs par défaut en arrière-plan...');
      Promise.all(
        newSteps.map(step => assignDefaultUsersToStep(step.id))
      ).then(() => {
        console.log('Assignation automatique terminée pour toutes les étapes du client:', name);
        // Le système temps réel mettra automatiquement à jour l'interface
      }).catch(error => {
        console.error('Erreur lors de l\'assignation automatique:', error);
      });
    } catch (error) {
      console.error('Erreur lors de l\'ajout du client:', error);
      toast("Impossible d'ajouter le client");
    }
  };

  const deleteClient = async (id: string) => {
    try {
      const clientToDelete = clients.find(client => client.id === id);

      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setClients(prev => prev.filter(client => client.id !== id));

      if (clientToDelete) {
        toast(`${clientToDelete.name} a été supprimé`);
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du client:', error);
      toast("Impossible de supprimer le client");
    }
  };

  const getClient = (id: string) => {
    return clients.find(client => client.id === id);
  };

  const updateClientName = async (id: string, name: string) => {
    try {
      // Mettre à jour directement le nom du client (plus de chiffrement)
      const { error } = await supabase
        .from('clients')
        .update({ name: name })
        .eq('id', id);

      if (error) throw error;

      // Mettre à jour l'état local
      setClients(prev =>
        prev.map(client =>
          client.id === id ? { ...client, name } : client
        )
      );
    } catch (error) {
      console.error('Erreur lors de la mise à jour du nom du client:', error);
      toast("Impossible de mettre à jour le nom du client");
    }
  };

  const updateClientDueDate = async (id: string, dueDate: string | null) => {
    try {
      console.log('Mise à jour de la date de rendu:', { id, dueDate });

      // Journaliser l'opération de mise à jour
      logUpdate('clients', id, currentProfile?.id, 'Mise à jour de la date de rendu');

      // S'assurer que la date est au format ISO ou null
      const formattedDate = dueDate ? new Date(dueDate).toISOString().split('T')[0] : null;
      console.log('Date de rendu formatée:', formattedDate);

      const { error } = await supabase
        .from('clients')
        .update({ due_date: formattedDate } as any)
        .eq('id', id);

      if (error) {
        console.error('Erreur Supabase:', error);
        throw error;
      }

      console.log('Date de rendu mise à jour avec succès dans Supabase');

      // Mettre à jour l'état local
      setClients(prev =>
        prev.map(client =>
          client.id === id ? { ...client, dueDate: formattedDate } : client
        )
      );

      console.log('État local mis à jour avec la nouvelle date de rendu');
      toast("Date de rendu mise à jour");
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la date de rendu:', error);
      toast("Impossible de mettre à jour la date de rendu");
    }
  };

  const updateClientCompleted = async (id: string, completed: boolean) => {
    try {
      console.log('Mise à jour du statut de completion:', { id, completed });

      // Journaliser l'opération de mise à jour
      logUpdate('clients', id, currentProfile?.id, 'Mise à jour du statut de completion');

      const { error } = await supabase
        .from('clients')
        .update({ completed } as any)
        .eq('id', id);

      if (error) {
        console.error('Erreur Supabase:', error);
        throw error;
      }

      console.log('Statut de completion mis à jour avec succès dans Supabase');

      // Mettre à jour l'état local
      setClients(prev =>
        prev.map(client =>
          client.id === id ? { ...client, completed } : client
        )
      );

      console.log('État local mis à jour avec le nouveau statut de completion');
      toast(completed ? "Client marqué comme terminé" : "Client marqué comme actif");
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut de completion:', error);
      toast("Impossible de mettre à jour le statut du client");
    }
  };

  const toggleClientCompleted = async (id: string) => {
    const client = getClient(id);
    if (!client) {
      toast("Client introuvable");
      return;
    }

    await updateClientCompleted(id, !client.completed);
  };

  const addStep = async (clientId: string, stepName: string) => {
    try {
      // Insérer directement le nom de l'étape (plus de chiffrement)
      const { data: newStep, error } = await supabase
        .from('steps')
        .insert({
          client_id: clientId,
          name: stepName,
          status: 'manquant' as Status,
          received_date: null,
          comment: ''
        })
        .select()
        .single();

      if (error) throw error;

      // Mettre à jour l'interface IMMÉDIATEMENT pour la réactivité
      const mappedStep: Step = {
        id: newStep.id,
        name: newStep.name, // Utiliser le nom direct
        status: newStep.status as Status,
        receivedDate: newStep.received_date ? new Date(newStep.received_date).toISOString().split('T')[0] : null,
        comment: newStep.comment || '',
        assignees: [] // Sera mis à jour par le système temps réel après l'assignation
      };

      setClients(prev =>
        prev.map(client =>
          client.id === clientId
            ? { ...client, steps: [...client.steps, mappedStep] }
            : client
        )
      );

      toast(`L'étape "${stepName}" a été ajoutée`);

      // ASSIGNATION AUTOMATIQUE EN ARRIÈRE-PLAN (non-bloquant)
      // L'utilisateur voit déjà l'étape, l'assignation se fait en arrière-plan
      console.log('Assignation automatique des utilisateurs par défaut en arrière-plan...');
      assignDefaultUsersToStep(newStep.id).then(() => {
        console.log('Assignation automatique terminée pour l\'étape:', newStep.id);
        // Le système temps réel mettra automatiquement à jour l'interface
      }).catch(error => {
        console.error('Erreur lors de l\'assignation automatique:', error);
      });
    } catch (error) {
      console.error('Erreur lors de l\'ajout de l\'étape:', error);
      toast("Impossible d'ajouter l'étape");
    }
  };

  const deleteStep = async (clientId: string, stepId: string) => {
    try {
      const { error } = await supabase
        .from('steps')
        .delete()
        .eq('id', stepId);

      if (error) throw error;

      setClients(prev =>
        prev.map(client =>
          client.id === clientId
            ? {
                ...client,
                steps: client.steps.filter(step => step.id !== stepId)
              }
            : client
        )
      );

      toast("L'étape a été supprimée");
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'étape:', error);
      toast("Impossible de supprimer l'étape");
    }
  };

  const updateStepStatus = async (clientId: string, stepId: string, status: Status) => {
    try {
      const { error } = await supabase
        .from('steps')
        .update({ status })
        .eq('id', stepId);

      if (error) throw error;

      const client = clients.find(c => c.id === clientId);
      const step = client?.steps.find(s => s.id === stepId);

      if (client && step) {
        if (status === 'urgent' && step.assignees && step.assignees.length > 0) {
          for (const assignee of step.assignees) {
            if (currentProfile) {
              // Créer une notification pour la tâche urgente
              const notificationMessage = `Tâche urgente: ${step.name} pour ${client.name}`;
              await createNotification(assignee.id, stepId, notificationMessage);

              // Envoyer une notification système pour les tâches urgentes
              try {
                // Utiliser directement la fonction de notification pour plus de réactivité
                const { sendDesktopNotification } = await import('../utils/notifications');
                console.log('Envoi d\'une notification pour une tâche urgente');
                sendDesktopNotification(
                  "Tâche urgente",
                  `${step.name} pour ${client.name} a été marquée comme urgente`,
                  "/logo.png"
                );
              } catch (notifError) {
                console.error('Erreur lors de l\'envoi de la notification:', notifError);
              }
            }
          }
        }

        if (status === 'valide' && step.assignees && step.assignees.length > 0) {
          for (const assignee of step.assignees) {
            if (currentProfile) {
              // Créer une notification pour la tâche validée
              const notificationMessage = `Étape validée: ${step.name} pour ${client.name}`;
              await createNotification(assignee.id, stepId, notificationMessage);

              // Envoyer une notification système pour les tâches validées
              try {
                // Utiliser directement la fonction de notification pour plus de réactivité
                const { sendDesktopNotification } = await import('../utils/notifications');
                console.log('Envoi d\'une notification pour une tâche validée');
                sendDesktopNotification(
                  "Étape validée",
                  `${step.name} pour ${client.name} a été validée`,
                  "/logo.png"
                );

                // La notification sera gérée par le NotificationContext
              } catch (notifError) {
                console.error('Erreur lors de l\'envoi de la notification:', notifError);
              }
            }
          }
        }
      }

      setClients(prev =>
        prev.map(client =>
          client.id === clientId
            ? {
                ...client,
                steps: client.steps.map(step =>
                  step.id === stepId ? { ...step, status } : step
                )
              }
            : client
        )
      );
    } catch (error) {
      console.error('Erreur lors de la mise à jour du statut de l\'étape:', error);
      toast("Impossible de mettre à jour le statut");
    }
  };

  const updateStepDate = async (clientId: string, stepId: string, date: string | null) => {
    try {
      console.log('Mise à jour de la date:', { clientId, stepId, date });

      // Journaliser l'opération de mise à jour
      logUpdate('steps', stepId, currentProfile?.id, 'Mise à jour de date');

      // S'assurer que la date est au format ISO ou null
      const formattedDate = date ? new Date(date).toISOString().split('T')[0] : null;
      console.log('Date formatée:', formattedDate);

      const { error } = await supabase
        .from('steps')
        .update({ received_date: formattedDate })
        .eq('id', stepId);

      if (error) {
        console.error('Erreur Supabase:', error);
        throw error;
      }

      console.log('Date mise à jour avec succès dans Supabase');

      // Mettre à jour l'état local
      setClients(prev =>
        prev.map(client =>
          client.id === clientId
            ? {
                ...client,
                steps: client.steps.map(step =>
                  step.id === stepId ? { ...step, receivedDate: formattedDate } : step
                )
              }
            : client
        )
      );

      console.log('État local mis à jour avec la nouvelle date');
    } catch (error) {
      console.error('Erreur lors de la mise à jour de la date de l\'étape:', error);
      toast("Impossible de mettre à jour la date");
    }
  };

  const updateStepComment = async (clientId: string, stepId: string, comment: string) => {
    try {
      // Extraire les mentions avant le chiffrement
      const mentions = comment.match(/@\[(.*?)\]\(\w+\)/g) || [];
      const mentionIds = mentions.map((mention: string) => {
        const match = mention.match(/@\[.*?\]\((\w+)\)/);
        return match ? match[1] : null;
      }).filter(Boolean);

      // Chiffrer le commentaire si contient des informations sensibles
      // (commentaires de plus de 20 caractères ou contenant des mots-clés sensibles)
      const sensitiveKeywords = ['confidentiel', 'secret', 'personnel', 'privé', 'sensible'];
      const isSensitive = comment.length > 20 ||
                          sensitiveKeywords.some(keyword => comment.toLowerCase().includes(keyword));

      let commentToStore = comment;

      if (isSensitive) {
        // Obtenir la clé de chiffrement
        const encryptionKey = getEncryptionKey();
        // Chiffrer le commentaire
        commentToStore = await encryptText(comment, encryptionKey);
        console.log('Commentaire chiffré pour protection des données sensibles');
      }

      // Journaliser l'opération de mise à jour
      logUpdate('steps', stepId, currentProfile?.id, 'Mise à jour de commentaire');

      // Mettre à jour le commentaire dans la base de données
      const { error: commentError } = await supabase
        .from('steps')
        .update({ comment: commentToStore })
        .eq('id', stepId);

      if (commentError) throw commentError;

      // Gérer les notifications pour les mentions
      if (mentionIds.length && currentProfile) {
        const baseMessage = `${currentProfile.name} vous a mentionné dans un commentaire`;

        // Créer une notification pour chaque mention
        for (const userId of mentionIds) {
          await createNotification(userId, stepId, baseMessage);
        }

        // Journaliser la création de notifications
        logCreate('notifications', null, currentProfile.id, 'Création de notifications pour mentions');
      }

      // Mettre à jour l'état local avec le commentaire non chiffré pour l'affichage
      setClients(prev =>
        prev.map(client =>
          client.id === clientId
            ? {
                ...client,
                steps: client.steps.map(step =>
                  step.id === stepId ? { ...step, comment } : step
                )
              }
            : client
        )
      );
    } catch (error) {
      console.error('Erreur lors de la mise à jour du commentaire de l\'étape:', error);
      toast("Impossible de mettre à jour le commentaire");
    }
  };

  const updateStepName = async (clientId: string, stepId: string, name: string) => {
    try {
      // Mettre à jour directement le nom de l'étape (plus de chiffrement)
      const { error } = await supabase
        .from('steps')
        .update({ name: name })
        .eq('id', stepId);

      if (error) throw error;

      // Mettre à jour l'état local
      setClients(prev =>
        prev.map(client =>
          client.id === clientId
            ? {
                ...client,
                steps: client.steps.map(step =>
                  step.id === stepId ? { ...step, name } : step
                )
              }
            : client
        )
      );

      toast("Nom de l'étape mis à jour");
    } catch (error) {
      console.error('Erreur lors de la mise à jour du nom de l\'étape:', error);
      toast("Impossible de mettre à jour le nom");
      throw error;
    }
  };

  const duplicateClientTemplate = async (clientId: string) => {
    try {
      const sourceClient = clients.find(client => client.id === clientId);
      if (!sourceClient) return;

      // Chiffrer le nom du client dupliqué
      const encryptionKey = getEncryptionKey();
      const newName = `${sourceClient.name} (copie)`;
      const encryptedName = await encryptText(newName, encryptionKey);
      console.log('Nom du client dupliqué chiffré pour protection des données sensibles');

      const { data: newClient, error: clientError } = await supabase
        .from('clients')
        .insert({ name: encryptedName })
        .select()
        .single();

      if (clientError) throw clientError;

      // Chiffrer les noms des étapes pour la duplication
      const stepsToInsert = await Promise.all(sourceClient.steps.map(async step => {
        // Chiffrer le nom de l'étape
        const encryptedStepName = await encryptText(step.name, encryptionKey);
        console.log(`Nom de l'étape "${step.name}" chiffré pour la duplication`);

        return {
          client_id: newClient.id,
          name: encryptedStepName,
          status: 'manquant' as Status,
          received_date: null,
          comment: ''
        };
      }));

      const { data: newSteps, error: stepsError } = await supabase
        .from('steps')
        .insert(stepsToInsert)
        .select();

      if (stepsError) throw stepsError;

      // Créer un mapping entre les étapes originales et les nouvelles étapes
      const stepMapping = sourceClient.steps.map((originalStep, index) => ({
        originalName: originalStep.name,
        newStepId: (newSteps as SupabaseStep[])[index]?.id
      }));

      const clientWithSteps: Client = {
        id: newClient.id,
        name: newName, // Utiliser le nom en clair pour l'affichage local
        dueDate: sourceClient.dueDate, // Copier la date de rendu du client source
        completed: false, // Client dupliqué non terminé par défaut
        steps: (newSteps as SupabaseStep[]).map((step, index) => {
          // Trouver le nom original correspondant à cette étape
          const originalName = stepMapping.find(mapping => mapping.newStepId === step.id)?.originalName ||
                              sourceClient.steps[index]?.name ||
                              "Étape sans nom";

          return {
            id: step.id,
            name: originalName, // Utiliser le nom en clair de l'étape originale
            status: step.status,
            receivedDate: step.received_date ? new Date(step.received_date).toISOString().split('T')[0] : null,
            comment: step.comment || '',
            assignees: [] // Initialiser avec un tableau vide pour éviter les problèmes avec le composant AssigneeSelect
          };
        })
      };

      setClients(prev => [...prev, clientWithSteps]);

      toast(`Le client ${sourceClient.name} a été dupliqué`);
    } catch (error) {
      console.error('Erreur lors de la duplication du client:', error);
      toast("Impossible de dupliquer le client");
    }
  };

  const searchClients = (query: string) => {
    const searchTerm = query.toLowerCase();
    return clients.filter(client =>
      client.name.toLowerCase().includes(searchTerm)
    );
  };

  const getClientProgress = (client: Client) => {
    const total = client.steps.length;
    const completed = client.steps.filter(step => step.status === 'valide').length;
    return { completed, total };
  };

  const getUrgentSteps = () => {
    const urgentItems: { client: Client; step: Step }[] = [];

    clients.forEach(client => {
      client.steps.forEach(step => {
        if (step.status === 'urgent') {
          urgentItems.push({ client, step });
        }
      });
    });

    return urgentItems;
  };

  const getComplianceProgress = (clientId: string) => {
    // Documents de conformité par défaut
    const DEFAULT_COMPLIANCE_TOTAL = 3; // lettre_mission, declaration_adequation, document_entree_relation

    // Vérifier le cache (valide pendant 30 secondes)
    const cached = complianceCache.get(clientId);
    const now = Date.now();

    if (cached && now - cached.lastUpdated < 30000) {
      return cached;
    }

    // Charger les vraies données de conformité depuis Supabase en arrière-plan
    const loadComplianceProgress = async () => {
      try {
        // Récupérer les données de conformité avec les types corrects
        const { data: complianceDocs, error } = await supabase
          .from('compliance_documents')
          .select('status')
          .eq('client_id', clientId);

        if (error) {
          console.error('Erreur lors de la récupération des documents de conformité:', error);
          return;
        }

        const completed = (complianceDocs || []).filter(doc => doc.status === 'signe_valide').length;
        const total = Math.max(DEFAULT_COMPLIANCE_TOTAL, (complianceDocs || []).length);
        const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;

        const newProgress = { completed, total, percentage, lastUpdated: now };

        // Mettre à jour le cache
        setComplianceCache(prev => new Map(prev.set(clientId, newProgress)));

        console.log(`Progression de conformité mise à jour pour le client ${clientId}:`, newProgress);

        return newProgress;
      } catch (error) {
        console.error('Erreur lors du chargement de la progression de conformité:', error);
      }
    };

    // Charger en arrière-plan si pas de cache
    if (!cached) {
      loadComplianceProgress();
    }

    // Retourner le cache existant ou des valeurs par défaut
    const completed = cached?.completed || 0;
    const total = cached?.total || DEFAULT_COMPLIANCE_TOTAL;
    const percentage = cached?.percentage || 0;

    return { completed, total, percentage };
  };

  const getAssignees = async (): Promise<Assignee[]> => {
    try {
      console.log('🔍 Récupération des assignees...');
      const { data: assigneesData, error } = await supabase
        .from('assignees')
        .select('*')
        .order('name', { ascending: true });

      if (error) {
        console.error('❌ Erreur Supabase lors de la récupération des assignees:', error);
        throw error;
      }

      console.log('📊 Assignees récupérés (données brutes):', assigneesData);

      if (!assigneesData || assigneesData.length === 0) {
        console.log('ℹ️ Aucun assignee trouvé');
        return [];
      }

      // Les noms des assignees ne sont pas chiffrés selon la configuration
      const processedAssignees = assigneesData.map(assignee => ({
        id: assignee.id,
        name: assignee.name,
        createdAt: assignee.created_at
      }));

      console.log('✅ Assignees traités avec succès:', processedAssignees);
      return processedAssignees;
    } catch (error) {
      console.error('❌ Erreur lors de la récupération des personnes:', error);
      toast("Impossible de charger les personnes");
      return [];
    }
  };

  const assignStepToUser = async (clientId: string, stepId: string, assigneeId: string) => {
    try {
      console.log(`Assignation de l'étape ${stepId} à l'utilisateur ${assigneeId} pour le client ${clientId}`);

      // Journaliser l'opération d'assignation
      logUpdate('step_assignments', stepId, currentProfile?.id, 'Assignation d\'utilisateur');

      // Récupérer les informations de l'assignee
      const { data: assigneeData, error: assigneeError } = await supabase
        .from('assignees')
        .select('*')
        .eq('id', assigneeId)
        .single();

      if (assigneeError) {
        console.error('Erreur lors de la récupération des informations de l\'assignee:', assigneeError);
        throw assigneeError;
      }

      console.log('Informations de l\'assignee récupérées:', assigneeData);

      // Les noms des assignees ne sont pas chiffrés selon la configuration
      const assigneeName = assigneeData.name;

      // Insérer l'assignation dans la base de données
      const { data: insertData, error } = await supabase
        .from('step_assignments')
        .insert({ step_id: stepId, assignee_id: assigneeId })
        .select();

      if (error) {
        console.error('Erreur lors de l\'insertion de l\'assignation:', error);
        throw error;
      }

      console.log('Assignation insérée avec succès:', insertData);

      // Mettre à jour l'état local
      setClients(prev =>
        prev.map(client => {
          if (client.id !== clientId) return client;

          return {
            ...client,
            steps: client.steps.map(step => {
              if (step.id !== stepId) return step;

              // S'assurer que step.assignees est un tableau
              const currentAssignees = Array.isArray(step.assignees) ? step.assignees : [];

              const newAssignee: Assignee = {
                id: assigneeId,
                name: assigneeName, // Utiliser le nom déchiffré
                createdAt: assigneeData.created_at
              };

              return {
                ...step,
                assignees: [...currentAssignees, newAssignee]
              };
            })
          };
        })
      );

      console.log('État local mis à jour avec la nouvelle assignation');
      toast(`${assigneeName} a été assigné avec succès`);
    } catch (error) {
      console.error('Erreur lors de l\'assignation:', error);
      toast("Impossible d'assigner la personne");
    }
  };

  const removeStepAssignment = async (clientId: string, stepId: string, assigneeId: string) => {
    try {
      const { error } = await supabase
        .from('step_assignments')
        .delete()
        .eq('step_id', stepId)
        .eq('assignee_id', assigneeId);

      if (error) throw error;

      setClients(prev =>
        prev.map(client => {
          if (client.id !== clientId) return client;

          return {
            ...client,
            steps: client.steps.map(step => {
              if (step.id !== stepId) return step;

              return {
                ...step,
                assignees: step.assignees?.filter(a => a.id !== assigneeId)
              };
            })
          };
        })
      );

      toast("Assignation supprimée");
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'assignation:', error);
      toast("Impossible de retirer la personne");
    }
  };

  // Les fonctions liées aux notifications ont été déplacées vers le NotificationContext
  // Les fonctions liées aux pings ont été déplacées vers le PingContext

  /**
   * SOUSCRIPTION TEMPS RÉEL UNIFIÉE POUR LES DONNÉES CLIENTS
   * Un seul canal pour toutes les tables liées aux clients
   */
  useSupabaseSubscription(
    'client-data',
    [
      // Nouveau client créé
      onInsert('clients', (payload) => {
        console.log('Nouveau client détecté:', payload.new);
        loadClientsData(false);
      }),

      // Client modifié (nom changé)
      onUpdate('clients', (payload) => {
        console.log('Client modifié détecté:', payload.new);
        loadClientsData(false);
      }),

      // Client supprimé
      onDelete('clients', (payload) => {
        console.log('Client supprimé détecté:', payload.old);
        loadClientsData(false);
      }),

      // Nouvelle étape créée
      onInsert('steps', (payload) => {
        console.log('Nouvelle étape détectée:', payload.new);
        loadClientsData(false);
      }),

      // Étape modifiée (statut, date, commentaire, nom)
      onUpdate('steps', (payload) => {
        console.log('Étape modifiée détectée:', payload.new);
        loadClientsData(false);
      }),

      // Étape supprimée
      onDelete('steps', (payload) => {
        console.log('Étape supprimée détectée:', payload.old);
        loadClientsData(false);
      }),

      // Nouvelle assignation créée
      onInsert('step_assignments', (payload) => {
        console.log('Nouvelle assignation détectée:', payload.new);
        loadClientsData(false);
      }),

      // Assignation supprimée
      onDelete('step_assignments', (payload) => {
        console.log('Assignation supprimée détectée:', payload.old);
        loadClientsData(false);
      }),

      // Document de conformité créé
      onInsert('compliance_documents', (payload) => {
        console.log('Nouveau document de conformité détecté:', payload.new);
        // Invalider le cache pour ce client
        const clientId = payload.new.client_id;
        if (clientId) {
          setComplianceCache(prev => {
            const newCache = new Map(prev);
            newCache.delete(clientId);
            return newCache;
          });
        }
        // Émettre un événement personnalisé pour notifier les composants
        window.dispatchEvent(new CustomEvent('compliance-document-updated', {
          detail: payload.new
        }));
        // Forcer le re-render en rechargeant les clients (pour mettre à jour les barres de progression)
        setTimeout(() => {
          setClients(prev => [...prev]);
        }, 100);
      }),

      // Document de conformité modifié
      onUpdate('compliance_documents', (payload) => {
        console.log('Document de conformité modifié détecté:', payload.new);
        // Invalider le cache pour ce client
        const clientId = payload.new.client_id;
        if (clientId) {
          setComplianceCache(prev => {
            const newCache = new Map(prev);
            newCache.delete(clientId);
            return newCache;
          });
        }
        // Émettre un événement personnalisé pour notifier les composants
        window.dispatchEvent(new CustomEvent('compliance-document-updated', {
          detail: payload.new
        }));
        // Forcer le re-render en rechargeant les clients (pour mettre à jour les barres de progression)
        setTimeout(() => {
          setClients(prev => [...prev]);
        }, 100);
      }),

      // Document de conformité supprimé
      onDelete('compliance_documents', (payload) => {
        console.log('Document de conformité supprimé détecté:', payload.old);
        // Invalider le cache pour ce client
        const clientId = (payload.old as any)?.client_id;
        if (clientId) {
          setComplianceCache(prev => {
            const newCache = new Map(prev);
            newCache.delete(clientId);
            return newCache;
          });
        }
        // Émettre un événement personnalisé pour notifier les composants
        window.dispatchEvent(new CustomEvent('compliance-document-deleted', {
          detail: payload.old
        }));
        // Forcer le re-render en rechargeant les clients (pour mettre à jour les barres de progression)
        setTimeout(() => {
          setClients(prev => [...prev]);
        }, 100);
      }),

      // Patrimoine créé
      onInsert('client_patrimoine', (payload) => {
        console.log('Nouvelle entrée patrimoine détectée:', payload.new);
        loadClientsData(false);
      }),

      // Patrimoine modifié
      onUpdate('client_patrimoine', (payload) => {
        console.log('Entrée patrimoine modifiée détectée:', payload.new);
        loadClientsData(false);
      }),

      // Patrimoine supprimé
      onDelete('client_patrimoine', (payload) => {
        console.log('Entrée patrimoine supprimée détectée:', payload.old);
        loadClientsData(false);
      })
    ]
  );

  return (
    <ClientContext.Provider
      value={{
        clients,
        loading,
        addClient,
        deleteClient,
        getClient,
        updateClientName,
        updateClientDueDate,
        updateClientCompleted,
        toggleClientCompleted,
        getComplianceProgress,
        addStep,
        deleteStep,
        updateStepStatus,
        updateStepDate,
        updateStepComment,
        duplicateClientTemplate,
        searchClients,
        getClientProgress,
        getUrgentSteps,
        getAssignees,
        assignStepToUser,
        removeStepAssignment,
        updateStepName,
      }}
    >
      {children}
    </ClientContext.Provider>
  );
};

export const useClientContext = () => {
  const context = useContext(ClientContext);
  if (context === undefined) {
    throw new Error('useClientContext must be used within a ClientProvider');
  }
  return context;
};
