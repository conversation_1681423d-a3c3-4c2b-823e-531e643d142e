-- Migration pour activer les souscriptions temps réel sur les tables principales
-- Cette migration est nécessaire pour que les changements soient propagés en temps réel
-- entre tous les profils utilisateurs

-- 1. Activer les souscriptions temps réel sur la table 'clients'
-- Permet de détecter les ajouts, modifications et suppressions de clients
ALTER PUBLICATION supabase_realtime ADD TABLE public.clients;

-- 2. Activer les souscriptions temps réel sur la table 'steps'
-- Permet de détecter les changements de statut, dates, commentaires, etc.
ALTER PUBLICATION supabase_realtime ADD TABLE public.steps;

-- 3. Activer les souscriptions temps réel sur la table 'step_assignments'
-- Permet de détecter les assignations et désassignations d'étapes
ALTER PUBLICATION supabase_realtime ADD TABLE public.step_assignments;

-- 4. Activer les souscriptions temps réel sur la table 'assignees'
-- Permet de détecter les nouveaux profils créés ou supprimés
ALTER PUBLICATION supabase_realtime ADD TABLE public.assignees;

-- 5. Vérifier que les tables notifications et pings sont déjà activées
-- (elles devraient déjà l'être car les notifications et pings fonctionnent)
ALTER PUBLICATION supabase_realtime ADD TABLE public.notifications;
ALTER PUBLICATION supabase_realtime ADD TABLE public.pings;

-- 6. Journaliser l'activation du temps réel
INSERT INTO public.data_access_logs (
  operation,
  table_name,
  details,
  timestamp
) VALUES (
  'update',
  'system',
  'Activation des souscriptions temps réel pour les tables clients, steps, step_assignments, assignees',
  NOW()
);

-- 7. Commentaires sur la migration
COMMENT ON PUBLICATION supabase_realtime IS 'Publication temps réel pour toutes les tables principales de l''application WeMa Tracker';
