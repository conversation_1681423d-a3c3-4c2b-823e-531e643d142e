import { <PERSON>, Sun } from "lucide-react";
import { useTheme } from "next-themes";
import { Button } from "@/components/ui/button";
import { useEffect, useState } from "react";

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Éviter les problèmes d'hydratation en attendant que le composant soit monté
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return null;
  }

  return (
    <Button
      variant="outline"
      size="icon"
      onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
      title={theme === "dark" ? "Passer au mode clair" : "Passer au mode sombre"}
      className={`rounded-full transition-all duration-300 ${theme === "dark" ? "bg-blue-900/30 border-blue-700/50" : "bg-blue-50 border-blue-200"}`}
    >
      {theme === "dark" ? (
        <Sun className="h-[1.2rem] w-[1.2rem] text-yellow-400 transition-transform duration-300 rotate-0" />
      ) : (
        <Moon className="h-[1.2rem] w-[1.2rem] text-blue-700 transition-transform duration-300 rotate-0" />
      )}
      <span className="sr-only">Changer de thème</span>
    </Button>
  );
}
