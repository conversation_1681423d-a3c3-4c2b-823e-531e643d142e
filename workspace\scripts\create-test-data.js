/**
 * Script pour créer des données de test avec chiffrement correct
 */

// Configuration identique à encryption.ts
const ENCRYPTION_KEY = "WeMaTracker-2025-RGPD-Protection-Key-v1";
const PREFIX = "enc:";

// Fonction de génération de clé (identique à encryption.ts)
async function getKey() {
  const encoder = new TextEncoder();
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    encoder.encode(ENCRYPTION_KEY),
    { name: 'PBKDF2' },
    false,
    ['deriveBits', 'deriveKey']
  );

  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: encoder.encode('WeMaTracker-Salt'),
      iterations: 100000,
      hash: 'SHA-256'
    },
    keyMaterial,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt']
  );
}

// Fonction de chiffrement (identique à encryption.ts)
async function encryptText(plaintext) {
  if (!plaintext) return '';

  try {
    const key = await getKey();
    const encoder = new TextEncoder();
    const data = encoder.encode(plaintext);
    
    // Générer un IV aléatoire
    const iv = crypto.getRandomValues(new Uint8Array(12));
    
    // Chiffrer
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      data
    );

    // Combiner IV + données chiffrées
    const combined = new Uint8Array(iv.length + encrypted.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(encrypted), iv.length);

    // Encoder en base64
    const base64 = btoa(String.fromCharCode(...combined));
    
    return `${PREFIX}${base64}`;
  } catch (error) {
    console.error('Erreur lors du chiffrement:', error);
    throw new Error('Échec du chiffrement');
  }
}

// Données de test à chiffrer
const testData = {
  clients: [
    "Société Alpha SARL",
    "Entreprise Beta SAS", 
    "Groupe Gamma SA"
  ],
  comments: [
    "Document validé par le responsable compliance",
    "En attente de signature du client",
    "Dossier complet, prêt pour validation finale"
  ]
};

// Fonction principale
async function createTestData() {
  console.log("🔐 Génération des données de test chiffrées...");
  
  try {
    // Chiffrer les noms de clients
    const encryptedClients = await Promise.all(
      testData.clients.map(async (name, index) => {
        const encrypted = await encryptText(name);
        console.log(`Client ${index + 1}: "${name}" → "${encrypted}"`);
        return encrypted;
      })
    );

    // Chiffrer les commentaires
    const encryptedComments = await Promise.all(
      testData.comments.map(async (comment, index) => {
        const encrypted = await encryptText(comment);
        console.log(`Commentaire ${index + 1}: "${comment}" → "${encrypted}"`);
        return encrypted;
      })
    );

    console.log("\n✅ Données chiffrées générées avec succès !");
    console.log("\n📋 Utilisez ces valeurs pour créer les données de test :");
    
    console.log("\n-- Clients chiffrés --");
    encryptedClients.forEach((encrypted, index) => {
      console.log(`Client ${index + 1}: ${encrypted}`);
    });

    console.log("\n-- Commentaires chiffrés --");
    encryptedComments.forEach((encrypted, index) => {
      console.log(`Commentaire ${index + 1}: ${encrypted}`);
    });

    return { encryptedClients, encryptedComments };
    
  } catch (error) {
    console.error("❌ Erreur lors de la génération:", error);
  }
}

// Exporter pour utilisation
if (typeof window !== 'undefined') {
  window.createTestData = createTestData;
  window.encryptText = encryptText;
}

// Exécuter si appelé directement
if (typeof require !== 'undefined' && require.main === module) {
  createTestData();
}
