
import { cn } from '@/lib/utils';

interface ProgressBarProps {
  completed: number;
  total: number;
  className?: string;
}

export const ProgressBar = ({ completed, total, className }: ProgressBarProps) => {
  const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;
  
  const getColorClass = (percent: number) => {
    if (percent >= 80) return 'bg-green-500 text-green-600';
    if (percent >= 50) return 'bg-blue-500 text-blue-600';
    if (percent >= 20) return 'bg-orange-500 text-orange-600';
    return 'bg-red-500 text-red-600';
  };
  
  const colorClass = getColorClass(percentage);
  
  return (
    <div className={cn("w-full", className)}>
      <div className="flex justify-between text-xs mb-1">
        <span className="text-gray-600 font-medium">{completed}/{total} validés</span>
        <span className={`font-medium ${colorClass.split(' ')[1]}`}>
          {percentage}%
        </span>
      </div>
      <div className="w-full bg-gray-200 rounded-full h-2.5 overflow-hidden">
        <div 
          className={`h-2.5 rounded-full transition-all duration-500 ease-out ${colorClass.split(' ')[0]}`}
          style={{ width: `${percentage}%` }}
        />
      </div>
    </div>
  );
};
