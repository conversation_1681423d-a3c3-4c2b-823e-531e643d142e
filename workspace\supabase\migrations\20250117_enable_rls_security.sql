-- Migration critique : Activation de Row Level Security (RLS) sur toutes les tables
-- RÉSOUT : Avertissement sécurité Supabase "RLS Disabled in Public"
-- Date : 17 janvier 2025

-- ============================================================================
-- ACTIVATION DE RLS SUR TOUTES LES TABLES PUBLIQUES
-- ============================================================================

-- 1. Table clients - Protection des données clients
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;

-- Supprimer et recréer la politique pour éviter les conflits
DROP POLICY IF EXISTS "Authenticated users can access clients" ON public.clients;
CREATE POLICY "Authenticated users can access clients" ON public.clients
  FOR ALL 
  USING (auth.uid() IS NOT NULL);

-- 2. Table steps - Protection des étapes des dossiers
ALTER TABLE public.steps ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Authenticated users can access steps" ON public.steps;
CREATE POLICY "Authenticated users can access steps" ON public.steps
  FOR ALL 
  USING (auth.uid() IS NOT NULL);

-- 3. Table assignees - Protection des informations des assignees
ALTER TABLE public.assignees ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Authenticated users can access assignees" ON public.assignees;
CREATE POLICY "Authenticated users can access assignees" ON public.assignees
  FOR ALL 
  USING (auth.uid() IS NOT NULL);

-- 4. Table step_assignments - Protection des assignations
ALTER TABLE public.step_assignments ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Authenticated users can access step_assignments" ON public.step_assignments;
CREATE POLICY "Authenticated users can access step_assignments" ON public.step_assignments
  FOR ALL 
  USING (auth.uid() IS NOT NULL);

-- 5. Table compliance_documents - Protection des documents de conformité
ALTER TABLE public.compliance_documents ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Authenticated users can access compliance_documents" ON public.compliance_documents;
CREATE POLICY "Authenticated users can access compliance_documents" ON public.compliance_documents
  FOR ALL 
  USING (auth.uid() IS NOT NULL);

-- 6. Table profiles - Protection des profils utilisateurs  
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Supprimer toutes les politiques existantes pour les profils
DROP POLICY IF EXISTS "Users can view all profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can insert own profile" ON public.profiles;

-- Recréer les politiques pour les profils
CREATE POLICY "Users can view all profiles" ON public.profiles
  FOR SELECT 
  USING (auth.uid() IS NOT NULL);

CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE 
  USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
  FOR INSERT 
  WITH CHECK (auth.uid() = id);

-- 7. Table notifications - Protection des notifications
ALTER TABLE public.notifications ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can access own notifications" ON public.notifications;
CREATE POLICY "Users can access own notifications" ON public.notifications
  FOR ALL 
  USING (auth.uid() = user_id);

-- 8. Table pings - Protection des messages directs
ALTER TABLE public.pings ENABLE ROW LEVEL SECURITY;

DROP POLICY IF EXISTS "Users can access relevant pings" ON public.pings;
CREATE POLICY "Users can access relevant pings" ON public.pings
  FOR ALL 
  USING (auth.uid() = from_user_id OR auth.uid() = to_user_id);

-- 9. Table data_access_logs - Accès restreint aux administrateurs
-- (RLS déjà activé dans la migration précédente, mais on vérifie)
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_tables 
    WHERE tablename = 'data_access_logs' 
    AND rowsecurity = true
  ) THEN
    ALTER TABLE public.data_access_logs ENABLE ROW LEVEL SECURITY;
  END IF;
END $$;

-- ============================================================================
-- COMMENTAIRES EXPLICATIFS
-- ============================================================================

COMMENT ON POLICY "Authenticated users can access clients" ON public.clients 
IS 'Permet aux utilisateurs authentifiés d''accéder aux clients - sécurité applicative';

COMMENT ON POLICY "Authenticated users can access steps" ON public.steps 
IS 'Permet aux utilisateurs authentifiés d''accéder aux étapes - sécurité applicative';

COMMENT ON POLICY "Authenticated users can access assignees" ON public.assignees 
IS 'Permet aux utilisateurs authentifiés d''accéder aux assignees - sécurité applicative';

COMMENT ON POLICY "Authenticated users can access step_assignments" ON public.step_assignments 
IS 'Permet aux utilisateurs authentifiés d''accéder aux assignations - sécurité applicative';

COMMENT ON POLICY "Authenticated users can access compliance_documents" ON public.compliance_documents 
IS 'Permet aux utilisateurs authentifiés d''accéder aux documents de conformité - sécurité applicative';

COMMENT ON POLICY "Users can view all profiles" ON public.profiles 
IS 'Permet la consultation de tous les profils pour collaboration - sécurité applicative';

COMMENT ON POLICY "Users can update own profile" ON public.profiles 
IS 'Permet modification du profil personnel uniquement - protection données personnelles';

COMMENT ON POLICY "Users can insert own profile" ON public.profiles 
IS 'Permet création du profil personnel uniquement - protection données personnelles';

COMMENT ON POLICY "Users can access own notifications" ON public.notifications 
IS 'Accès aux notifications personnelles uniquement - protection vie privée';

COMMENT ON POLICY "Users can access relevant pings" ON public.pings 
IS 'Accès aux messages directs envoyés/reçus uniquement - protection vie privée';

-- ============================================================================
-- VÉRIFICATION DE L'ACTIVATION RLS
-- ============================================================================

-- Vérifier que RLS est activé sur toutes les tables
DO $$
DECLARE
  table_record RECORD;
  missing_rls TEXT[] := '{}';
BEGIN
  FOR table_record IN 
    SELECT schemaname, tablename, rowsecurity 
    FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename IN ('clients', 'steps', 'assignees', 'step_assignments', 
                      'compliance_documents', 'profiles', 'notifications', 'pings')
  LOOP
    IF NOT table_record.rowsecurity THEN
      missing_rls := missing_rls || (table_record.schemaname || '.' || table_record.tablename);
    END IF;
  END LOOP;

  IF array_length(missing_rls, 1) > 0 THEN
    RAISE WARNING 'RLS non activé sur les tables: %', array_to_string(missing_rls, ', ');
  ELSE
    RAISE NOTICE '✅ RLS activé avec succès sur toutes les tables publiques';
  END IF;
END $$; 