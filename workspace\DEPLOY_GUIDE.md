# 🚀 Guide de Déploiement - Dossier Tracker v2

## 📋 Prérequis sur l'autre appareil

Avant de commencer, assurez-vous d'avoir installé :

### 1. Node.js et npm
```bash
# Télécharger depuis https://nodejs.org (version LTS recommandée)
node --version  # Vérifier l'installation
npm --version   # Vérifier l'installation
```

### 2. Rust et Cargo
```bash
# Windows : Télécharger depuis https://rustup.rs/
# Ou exécuter dans PowerShell :
Invoke-WebRequest -Uri "https://win.rustup.rs/" -OutFile "rustup-init.exe"
.\rustup-init.exe

# Vérifier l'installation
rustc --version
cargo --version
```

### 3. Git
```bash
# Télécharger depuis https://git-scm.com/
git --version  # Vérifier l'installation
```

## 🔧 Installation du projet

### Étape 1 : <PERSON><PERSON><PERSON> le projet
```bash
git clone https://github.com/adrienlle/dossier-tracker-client-v2.git
cd dossier-tracker-client-v2
```

### Étape 2 : Installer les dépendances Node.js
```bash
npm install
```

### Étape 3 : Installer les dépendances Rust (Tauri)
```bash
cd src-tauri
cargo fetch
cd ..
```

### Étape 4 : Configuration de l'environnement
```bash
# Copier le fichier d'exemple d'environnement (si existe)
cp .env.example .env
# Puis éditer .env avec vos valeurs
```

## 🏃‍♂️ Lancement du projet

### Mode développement
```bash
# Terminal 1 : Lancer le frontend React
npm run dev

# Terminal 2 : Lancer l'application Tauri
npm run tauri dev
```

### Mode production
```bash
# Construire l'application
npm run tauri build
```

## 📁 Structure du projet

- `src/` - Code source React/TypeScript
- `src-tauri/` - Code source Rust pour Tauri
- `public/` - Fichiers statiques
- `docs/` - Documentation
- `supabase/` - Configuration Supabase

## 🔧 Scripts disponibles

- `npm run dev` - Lancer le frontend en mode dev
- `npm run build` - Construire le frontend
- `npm run tauri dev` - Lancer l'app Tauri en dev
- `npm run tauri build` - Construire l'app Tauri
- `npm run test` - Lancer les tests

## 🎯 Première utilisation

1. Lancez `npm run tauri dev`
2. L'application s'ouvrira automatiquement
3. Configurez vos paramètres dans l'interface
4. Commencez à utiliser le tracker !

## 🐛 Dépannage

### Erreur de compilation Rust
```bash
# Mettre à jour Rust
rustup update

# Nettoyer le cache Cargo
cd src-tauri
cargo clean
```

### Erreur de dépendances Node
```bash
# Supprimer node_modules et réinstaller
rm -rf node_modules package-lock.json
npm install
```

### Port déjà utilisé
```bash
# Changer le port dans vite.config.ts ou tuer le processus
netstat -ano | findstr :3000
taskkill /PID <PID> /F
```

## 📞 Support

Pour toute question, créez une issue sur le repository GitHub. 