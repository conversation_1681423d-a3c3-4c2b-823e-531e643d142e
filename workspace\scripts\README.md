# Script Keep-Alive pour Supabase

Ce dossier contient des scripts pour maintenir la base de données Supabase active en envoyant une requête tous les 3 jours.

## Configuration initiale

1. Assurez-vous que Node.js est installé sur votre ordinateur (téléchargez-le depuis [nodejs.org](https://nodejs.org/) si nécessaire)
2. Ouvrez une invite de commande dans ce dossier (`scripts`)
3. Exécutez `npm install` pour installer les dépendances nécessaires

## Configuration de la tâche planifiée dans Windows

1. Ouvrez le "Planificateur de tâches" de Windows (recherchez "Planificateur de tâches" dans le menu Démarrer)
2. C<PERSON>z sur "Créer une tâche de base..." dans le panneau Actions à droite
3. Donnez un nom à la tâche, par exemple "WeMa Tracker - Keep Alive Supabase"
4. Sélectionnez "Tous les 3 jours" comme déclencheur
5. S<PERSON>lection<PERSON>z "Démarrer un programme" comme action
6. Parcourez et sélectionnez le fichier `run-keep-alive.bat` dans ce dossier
7. <PERSON><PERSON><PERSON><PERSON> l'assistant

## Vérification

Le script crée un fichier de log `keep-alive.log` dans ce dossier qui enregistre chaque exécution. Vous pouvez vérifier ce fichier pour vous assurer que le script s'exécute correctement.

## Exécution manuelle

Vous pouvez exécuter le script manuellement en double-cliquant sur le fichier `run-keep-alive.bat`.

## Comment ça fonctionne

Le script envoie une simple requête à la base de données Supabase tous les 3 jours pour éviter que le projet ne se mette en pause en raison d'inactivité. Il n'effectue aucune modification des données, il se contente de faire une requête de comptage.

## Dépendances

- Node.js
- @supabase/supabase-js

Ces dépendances seront automatiquement installées lors de la première exécution du script batch.
