/**
 * Service pour la gestion des étapes
 * Centralise toutes les opérations CRUD sur les étapes
 */

import { supabase } from "@/integrations/supabase/client";
import { Status, Step, Assignee } from '@/types';
import { handleError, ErrorCode, createAppError } from "@/services/errorService";
import { logCreate, logUpdate, logDelete } from "@/utils/secure-logging";
import { encryptText, decryptText, getEncryptionKey, isEncrypted } from "@/utils/encryption";
import { sendDesktopNotification } from "@/utils/notifications";

/**
 * Met à jour le statut d'une étape dans la base de données
 * @param stepId ID de l'étape
 * @param status Nouveau statut
 */
export async function updateStepStatusInDatabase(stepId: string, status: Status): Promise<void> {
  const { error } = await supabase
    .from('steps')
    .update({ status })
    .eq('id', stepId);

  if (error) throw error;
}

/**
 * Récupère les informations d'une étape et du client associé
 * @param stepId ID de l'étape
 */
export async function getStepAndClientInfo(stepId: string) {
  const { data: stepData, error: stepError } = await supabase
    .from('steps')
    .select('*, clients(*)')
    .eq('id', stepId)
    .single();

  if (stepError) throw stepError;

  // Déchiffrer les noms si nécessaire
  const encryptionKey = getEncryptionKey();
  let stepName = stepData.name;
  let clientName = stepData.clients.name;

  if (isEncrypted(stepName)) {
    try {
      stepName = await decryptText(stepName, encryptionKey);
    } catch (error) {
      console.error(`Erreur lors du déchiffrement du nom de l'étape ${stepId}:`, error);
    }
  }

  if (isEncrypted(clientName)) {
    try {
      clientName = await decryptText(clientName, encryptionKey);
    } catch (error) {
      console.error(`Erreur lors du déchiffrement du nom du client ${stepData.clients.id}:`, error);
    }
  }

  return {
    step: {
      ...stepData,
      name: stepName
    },
    client: {
      ...stepData.clients,
      name: clientName
    }
  };
}

/**
 * Récupère les assignés d'une étape
 * @param stepId ID de l'étape
 */
export async function getStepAssignees(stepId: string): Promise<Assignee[]> {
  const { data: assignmentsData, error: assignmentsError } = await supabase
    .from('step_assignments')
    .select('*, assignees(*)')
    .eq('step_id', stepId);

  if (assignmentsError) throw assignmentsError;

  // Les noms des assignees ne sont pas chiffrés selon la configuration
  const assignees = assignmentsData.map(assignment => ({
    id: assignment.assignees.id,
    name: assignment.assignees.name,
    createdAt: assignment.assignees.created_at
  }));

  return assignees;
}

/**
 * Crée une notification pour un changement de statut
 * @param userId ID de l'utilisateur destinataire
 * @param stepId ID de l'étape
 * @param createdById ID du créateur de la notification
 * @param message Message de la notification
 */
export async function createStatusNotification(
  userId: string,
  stepId: string,
  createdById: string,
  message: string
): Promise<void> {
  // Insérer directement le message (plus de chiffrement)
  const { error } = await supabase
    .from('notifications')
    .insert({
      user_id: userId,
      step_id: stepId,
      created_by: createdById,
      message: message
    });

  if (error) throw error;
}

export class StepService {
  /**
   * Crée une nouvelle étape pour un client
   */
  static async createStep(
    clientId: string,
    stepName: string,
    currentProfileId?: string
  ): Promise<Step | null> {
    try {
      // Chiffrer le nom de l'étape
      const encryptionKey = getEncryptionKey();
      const encryptedName = await encryptText(stepName, encryptionKey);
      console.log('Nom de l\'étape chiffré pour protection des données sensibles');

      // Journaliser l'opération
      logCreate('steps', null, currentProfileId, `Création de l'étape: ${stepName}`);

      const { data: newStep, error } = await supabase
        .from('steps')
        .insert({
          client_id: clientId,
          name: encryptedName,
          status: 'manquant' as Status,
          received_date: null,
          comment: ''
        })
        .select()
        .single();

      if (error) throw error;

      // Assigner automatiquement Patrick et Quentin à la nouvelle étape
      const patrickId = 'ece3b34a-a36d-4334-b907-4cacbe147a70';
      const quentinId = '1d2e5e60-1014-45f0-8a17-0101ee032b69';

      const { error: assignmentError } = await supabase
        .from('step_assignments')
        .insert([
          { step_id: newStep.id, assignee_id: patrickId },
          { step_id: newStep.id, assignee_id: quentinId }
        ]);

      if (assignmentError) {
        console.error('Erreur lors de l\'assignation automatique:', assignmentError);
        // Ne pas faire échouer la création de l'étape pour autant
      }

      // Récupérer les assignés pour le retour
      const assignees = [
        { id: patrickId, name: 'Patrick', createdAt: new Date().toISOString() },
        { id: quentinId, name: 'Quentin', createdAt: new Date().toISOString() }
      ];

      return {
        id: newStep.id,
        name: stepName, // Nom en clair pour l'affichage
        status: newStep.status as Status,
        receivedDate: newStep.received_date ? new Date(newStep.received_date).toISOString().split('T')[0] : null,
        comment: newStep.comment || '',
        assignees: assignees
      };
    } catch (error) {
      handleError(error, 'StepService.createStep');
      return null;
    }
  }

  /**
   * Supprime une étape
   */
  static async deleteStep(stepId: string, currentProfileId?: string): Promise<boolean> {
    try {
      // Journaliser l'opération
      logDelete('steps', stepId, currentProfileId, 'Suppression de l\'étape');

      const { error } = await supabase
        .from('steps')
        .delete()
        .eq('id', stepId);

      if (error) throw error;

      return true;
    } catch (error) {
      handleError(error, 'StepService.deleteStep');
      return false;
    }
  }

  /**
   * Met à jour le statut d'une étape
   */
  static async updateStepStatus(
    stepId: string,
    status: Status,
    currentProfileId?: string
  ): Promise<boolean> {
    try {
      // Journaliser l'opération
      logUpdate('steps', stepId, currentProfileId, `Mise à jour du statut: ${status}`);

      const { error } = await supabase
        .from('steps')
        .update({ status })
        .eq('id', stepId);

      if (error) throw error;

      return true;
    } catch (error) {
      handleError(error, 'StepService.updateStepStatus');
      return false;
    }
  }

  /**
   * Met à jour la date de réception d'une étape
   */
  static async updateStepDate(
    stepId: string,
    date: string | null,
    currentProfileId?: string
  ): Promise<boolean> {
    try {
      // Journaliser l'opération
      logUpdate('steps', stepId, currentProfileId, 'Mise à jour de date');

      // Formater la date
      const formattedDate = date ? new Date(date).toISOString().split('T')[0] : null;

      const { error } = await supabase
        .from('steps')
        .update({ received_date: formattedDate })
        .eq('id', stepId);

      if (error) throw error;

      return true;
    } catch (error) {
      handleError(error, 'StepService.updateStepDate');
      return false;
    }
  }

  /**
   * Met à jour le commentaire d'une étape avec chiffrement intelligent
   */
  static async updateStepComment(
    stepId: string,
    comment: string,
    currentProfileId?: string
  ): Promise<{ success: boolean; mentionIds?: string[] }> {
    try {
      // Extraire les mentions avant le chiffrement
      const mentions = comment.match(/@\[(.*?)\]\(\w+\)/g) || [];
      const mentionIds = mentions.map((mention: string) => {
        const match = mention.match(/@\[.*?\]\((\w+)\)/);
        return match ? match[1] : null;
      }).filter(Boolean) as string[];

      // Déterminer si le commentaire doit être chiffré
      const sensitiveKeywords = ['confidentiel', 'secret', 'personnel', 'privé', 'sensible'];
      const isSensitive = comment.length > 20 ||
                          sensitiveKeywords.some(keyword => comment.toLowerCase().includes(keyword));

      let commentToStore = comment;

      if (isSensitive) {
        const encryptionKey = getEncryptionKey();
        commentToStore = await encryptText(comment, encryptionKey);
        console.log('Commentaire chiffré pour protection des données sensibles');
      }

      // Journaliser l'opération
      logUpdate('steps', stepId, currentProfileId, 'Mise à jour du commentaire');

      const { error } = await supabase
        .from('steps')
        .update({ comment: commentToStore })
        .eq('id', stepId);

      if (error) throw error;

      return { success: true, mentionIds };
    } catch (error) {
      handleError(error, 'StepService.updateStepComment');
      return { success: false };
    }
  }

  /**
   * Met à jour le nom d'une étape
   */
  static async updateStepName(
    stepId: string,
    name: string,
    currentProfileId?: string
  ): Promise<boolean> {
    try {
      // Chiffrer le nom de l'étape
      const encryptionKey = getEncryptionKey();
      const encryptedName = await encryptText(name, encryptionKey);
      console.log('Nom de l\'étape chiffré pour protection des données sensibles');

      // Journaliser l'opération
      logUpdate('steps', stepId, currentProfileId, `Mise à jour du nom: ${name}`);

      const { error } = await supabase
        .from('steps')
        .update({ name: encryptedName })
        .eq('id', stepId);

      if (error) throw error;

      return true;
    } catch (error) {
      handleError(error, 'StepService.updateStepName');
      return false;
    }
  }
}
