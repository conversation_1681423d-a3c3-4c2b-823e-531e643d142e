# Guide de signature de code pour WeMa Tracker

## Pourquoi signer le code ?

La signature de code est essentielle pour :
- <PERSON><PERSON><PERSON> les faux positifs antivirus
- Établir la confiance avec Windows Defender
- Permettre l'installation sans avertissements de sécurité
- Identifier clairement l'éditeur de l'application

## Étapes pour obtenir un certificat de signature de code

### 1. Acheter un certificat de signature de code
Fournisseurs recommandés :
- **DigiCert** (le plus reconnu)
- **Sectigo** (bon rapport qualité/prix)
- **GlobalSign**

### 2. Types de certificats
- **Standard Code Signing** : ~200-400€/an
- **EV Code Signing** : ~600-800€/an (recommandé, pas d'avertissements SmartScreen)

### 3. Configuration dans Tauri

Une fois le certificat obtenu, modifier `tauri.conf.json` :

```json
"windows": {
  "certificateThumbprint": "VOTRE_THUMBPRINT_ICI",
  "digestAlgorithm": "sha256",
  "timestampUrl": "http://timestamp.digicert.com"
}
```

### 4. Construction signée

```bash
# Avec certificat installé
npm run tauri build

# Ou avec fichier .p12
tauri build --config '{"bundle":{"windows":{"signTool":{"certificateFile":"path/to/cert.p12","certificatePassword":"password"}}}}'
```

## Solutions temporaires (sans certificat)

1. **Soumettre à Microsoft** : Envoyer l'exécutable à Microsoft pour analyse
2. **Whitelist antivirus** : Contacter les éditeurs d'antivirus pour signaler le faux positif
3. **Distribution interne** : Ajouter l'application aux exceptions antivirus de l'entreprise

## Recommandations

Pour WeMa (usage interne) :
1. Acheter un certificat EV Code Signing
2. Configurer la signature automatique dans le build
3. Distribuer via un serveur interne sécurisé
