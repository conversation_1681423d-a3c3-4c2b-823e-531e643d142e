/**
 * Service pour la gestion des assignations
 * Centralise toutes les opérations sur les assignations d'étapes
 */

import { supabase } from "@/integrations/supabase/client";
import { Assignee, SupabaseAssignee } from '@/types';
import { handleError, ErrorCode, createAppError } from "@/services/errorService";
import { logUpdate, logDelete, logRead } from "@/utils/secure-logging";

export class AssignmentService {
  /**
   * Récupère tous les assignees disponibles
   */
  static async getAllAssignees(): Promise<Assignee[]> {
    try {
      console.log('🔍 Récupération des assignees...');

      const { data: assigneesData, error } = await supabase
        .from('assignees')
        .select('*')
        .order('name');

      if (error) {
        throw createAppError(
          `Erreur lors du chargement des assignees: ${error.message}`,
          ErrorCode.DB_QUERY_ERROR
        );
      }

      if (!assigneesData || assigneesData.length === 0) {
        console.log('ℹ️ Aucun assignee trouvé');
        return [];
      }

      // Les noms des assignees ne sont pas chiffrés selon la configuration
      const processedAssignees = assigneesData.map(assignee => ({
        id: assignee.id,
        name: assignee.name,
        createdAt: assignee.created_at
      }));

      console.log(`✅ ${processedAssignees.length} assignees traités avec succès`);
      return processedAssignees;
    } catch (error) {
      handleError(error, 'AssignmentService.getAllAssignees');
      return [];
    }
  }

  /**
   * Assigne une étape à un utilisateur
   */
  static async assignStepToUser(
    stepId: string,
    assigneeId: string,
    currentProfileId?: string
  ): Promise<{ success: boolean; assignee?: Assignee }> {
    try {
      console.log(`Assignation de l'étape ${stepId} à l'utilisateur ${assigneeId}`);

      // Journaliser l'opération d'assignation
      logUpdate('step_assignments', stepId, currentProfileId, 'Assignation d\'utilisateur');

      // Vérifier si l'assignation existe déjà
      const { data: existingAssignment } = await supabase
        .from('step_assignments')
        .select('*')
        .eq('step_id', stepId)
        .eq('assignee_id', assigneeId)
        .single();

      if (existingAssignment) {
        console.log('Assignation déjà existante, pas de duplication');
        return { success: true };
      }

      // Créer l'assignation
      const { error: assignmentError } = await supabase
        .from('step_assignments')
        .insert({
          step_id: stepId,
          assignee_id: assigneeId
        });

      if (assignmentError) throw assignmentError;

      // Récupérer les informations de l'assignee
      const { data: assigneeData, error: assigneeError } = await supabase
        .from('assignees')
        .select('*')
        .eq('id', assigneeId)
        .single();

      if (assigneeError) throw assigneeError;

      // Les noms des assignees ne sont pas chiffrés selon la configuration
      const assigneeName = assigneeData.name;

      const assignee: Assignee = {
        id: assigneeId,
        name: assigneeName,
        createdAt: assigneeData.created_at
      };

      console.log(`✅ Assignation réussie:`, assignee);
      return { success: true, assignee };
    } catch (error) {
      handleError(error, 'AssignmentService.assignStepToUser');
      return { success: false };
    }
  }

  /**
   * Supprime l'assignation d'une étape à un utilisateur
   */
  static async removeStepAssignment(
    stepId: string,
    assigneeId: string,
    currentProfileId?: string
  ): Promise<boolean> {
    try {
      console.log(`Suppression de l'assignation de l'étape ${stepId} pour l'utilisateur ${assigneeId}`);

      // Journaliser l'opération
      logDelete('step_assignments', `${stepId}-${assigneeId}`, currentProfileId, 'Suppression d\'assignation');

      const { error } = await supabase
        .from('step_assignments')
        .delete()
        .eq('step_id', stepId)
        .eq('assignee_id', assigneeId);

      if (error) throw error;

      console.log('✅ Assignation supprimée avec succès');
      return true;
    } catch (error) {
      handleError(error, 'AssignmentService.removeStepAssignment');
      return false;
    }
  }

  /**
   * Récupère toutes les assignations d'une étape
   */
  static async getStepAssignments(stepId: string): Promise<Assignee[]> {
    try {
      const { data: assignmentsData, error: assignmentsError } = await supabase
        .from('step_assignments')
        .select('*, assignees(*)')
        .eq('step_id', stepId);

      if (assignmentsError) throw assignmentsError;

      if (!assignmentsData || assignmentsData.length === 0) {
        return [];
      }

      // Les noms des assignees ne sont pas chiffrés selon la configuration
      const assignees = assignmentsData.map(assignment => ({
        id: assignment.assignees.id,
        name: assignment.assignees.name,
        createdAt: assignment.assignees.created_at
      }));

      return assignees;
    } catch (error) {
      handleError(error, 'AssignmentService.getStepAssignments');
      return [];
    }
  }

  /**
   * Récupère toutes les assignations d'un utilisateur
   */
  static async getUserAssignments(assigneeId: string): Promise<string[]> {
    try {
      const { data: assignmentsData, error } = await supabase
        .from('step_assignments')
        .select('step_id')
        .eq('assignee_id', assigneeId);

      if (error) throw error;

      return assignmentsData?.map(assignment => assignment.step_id) || [];
    } catch (error) {
      handleError(error, 'AssignmentService.getUserAssignments');
      return [];
    }
  }

  /**
   * Vérifie si un utilisateur est assigné à une étape
   */
  static async isUserAssignedToStep(stepId: string, assigneeId: string): Promise<boolean> {
    try {
      const { data, error } = await supabase
        .from('step_assignments')
        .select('id')
        .eq('step_id', stepId)
        .eq('assignee_id', assigneeId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
        throw error;
      }

      return !!data;
    } catch (error) {
      handleError(error, 'AssignmentService.isUserAssignedToStep');
      return false;
    }
  }

  /**
   * Récupère un assignee par son ID avec déchiffrement
   */
  static async getAssigneeById(assigneeId: string): Promise<Assignee | null> {
    try {
      const { data: assigneeData, error } = await supabase
        .from('assignees')
        .select('*')
        .eq('id', assigneeId)
        .single();

      if (error) throw error;
      if (!assigneeData) return null;

      // Les noms des assignees ne sont pas chiffrés selon la configuration
      const assigneeName = assigneeData.name;

      return {
        id: assigneeData.id,
        name: assigneeName,
        createdAt: assigneeData.created_at
      };
    } catch (error) {
      handleError(error, 'AssignmentService.getAssigneeById');
      return null;
    }
  }
}
