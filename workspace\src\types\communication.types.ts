/**
 * Types spécifiques à la communication (notifications, pings, messages)
 */

import { UUID, ISODateString } from './client.types';

/**
 * Interface pour les profils utilisateurs
 */
export interface Profile {
  id: UUID;
  name: string;
  avatar_url?: string;
  created_at?: ISODateString;
}

/**
 * Interface pour les notifications
 */
export interface Notification {
  id: UUID;
  user_id: UUID;
  step_id: UUID;
  created_by: UUID;
  message: string;
  read: boolean;
  created_at: ISODateString;
  created_by_profile?: {
    name: string;
  };
}

/**
 * Interface pour les messages directs (pings)
 */
export interface Ping {
  id: UUID;
  fromUserId: UUID;
  toUserId: UUID;
  message: string;
  read: boolean;
  createdAt: ISODateString;
  fromUserName?: string;
  toUserName?: string;
}

/**
 * Types pour les différents types de notifications
 */
export type NotificationType = 
  | 'step_status_changed'
  | 'step_assigned'
  | 'step_unassigned'
  | 'client_completed'
  | 'urgent_task'
  | 'due_date_approaching'
  | 'comment_added'
  | 'mention';

/**
 * Interface pour les données de notification typées
 */
export interface TypedNotificationData {
  type: NotificationType;
  clientId: UUID;
  clientName: string;
  stepId?: UUID;
  stepName?: string;
  oldValue?: any;
  newValue?: any;
  dueDate?: ISODateString;
  assigneeId?: UUID;
  assigneeName?: string;
  comment?: string;
  mentionedUsers?: UUID[];
}

/**
 * Interface pour les notifications enrichies
 */
export interface EnrichedNotification extends Notification {
  data: TypedNotificationData;
  isUrgent: boolean;
  actionUrl?: string;
  actionLabel?: string;
}

/**
 * Interface pour les préférences de notification
 */
export interface NotificationPreferences {
  userId: UUID;
  emailNotifications: boolean;
  pushNotifications: boolean;
  inAppNotifications: boolean;
  notificationTypes: {
    [K in NotificationType]: boolean;
  };
  quietHours?: {
    enabled: boolean;
    startTime: string; // Format HH:mm
    endTime: string;   // Format HH:mm
    timezone: string;
  };
  frequency: 'immediate' | 'hourly' | 'daily' | 'weekly';
}

/**
 * Interface pour les canaux de communication
 */
export interface CommunicationChannel {
  id: UUID;
  name: string;
  description?: string;
  type: 'public' | 'private' | 'direct';
  members: UUID[];
  created_by: UUID;
  created_at: ISODateString;
  last_activity?: ISODateString;
}

/**
 * Interface pour les messages dans les canaux
 */
export interface ChannelMessage {
  id: UUID;
  channel_id: UUID;
  user_id: UUID;
  content: string;
  message_type: 'text' | 'file' | 'system' | 'mention';
  reply_to?: UUID;
  edited_at?: ISODateString;
  created_at: ISODateString;
  reactions?: MessageReaction[];
  attachments?: MessageAttachment[];
}

/**
 * Interface pour les réactions aux messages
 */
export interface MessageReaction {
  id: UUID;
  message_id: UUID;
  user_id: UUID;
  emoji: string;
  created_at: ISODateString;
}

/**
 * Interface pour les pièces jointes
 */
export interface MessageAttachment {
  id: UUID;
  message_id: UUID;
  filename: string;
  file_size: number;
  mime_type: string;
  url: string;
  created_at: ISODateString;
}

/**
 * Interface pour les mentions dans les messages
 */
export interface MessageMention {
  id: UUID;
  message_id: UUID;
  mentioned_user_id: UUID;
  position: number;
  length: number;
  created_at: ISODateString;
}

/**
 * Interface pour les conversations directes
 */
export interface DirectConversation {
  id: UUID;
  participants: UUID[];
  last_message?: ChannelMessage;
  unread_count: number;
  created_at: ISODateString;
  updated_at: ISODateString;
}

/**
 * Interface pour les statuts de présence
 */
export interface UserPresence {
  user_id: UUID;
  status: 'online' | 'away' | 'busy' | 'offline';
  last_seen: ISODateString;
  custom_status?: string;
}

/**
 * Interface pour les événements de communication temps réel
 */
export type CommunicationEvent = 
  | { type: 'notification_received'; notification: Notification }
  | { type: 'ping_received'; ping: Ping }
  | { type: 'message_sent'; message: ChannelMessage }
  | { type: 'user_typing'; userId: UUID; channelId: UUID }
  | { type: 'user_presence_changed'; presence: UserPresence }
  | { type: 'notification_read'; notificationId: UUID }
  | { type: 'ping_read'; pingId: UUID };

/**
 * Interface pour les callbacks d'événements de communication
 */
export interface CommunicationEventCallbacks {
  onNotificationReceived?: (notification: Notification) => void;
  onPingReceived?: (ping: Ping) => void;
  onMessageReceived?: (message: ChannelMessage) => void;
  onUserTyping?: (userId: UUID, channelId: UUID) => void;
  onPresenceChanged?: (presence: UserPresence) => void;
  onNotificationRead?: (notificationId: UUID) => void;
  onPingRead?: (pingId: UUID) => void;
}

/**
 * Interface pour les statistiques de communication
 */
export interface CommunicationStats {
  totalNotifications: number;
  unreadNotifications: number;
  totalPings: number;
  unreadPings: number;
  totalMessages: number;
  activeConversations: number;
  onlineUsers: number;
  responseTime: number; // temps moyen de réponse en minutes
}

/**
 * Interface pour les filtres de communication
 */
export interface CommunicationFilters {
  type?: NotificationType[];
  read?: boolean;
  urgent?: boolean;
  dateRange?: {
    start: ISODateString;
    end: ISODateString;
  };
  fromUser?: UUID;
  toUser?: UUID;
  clientId?: UUID;
  stepId?: UUID;
}

/**
 * Interface pour les résultats de recherche de communication
 */
export interface CommunicationSearchResult {
  notifications: Notification[];
  pings: Ping[];
  messages: ChannelMessage[];
  totalCount: number;
  hasMore: boolean;
}

/**
 * Types pour les templates de notification
 */
export interface NotificationTemplate {
  id: UUID;
  type: NotificationType;
  title: string;
  body: string;
  variables: string[]; // Variables disponibles comme {clientName}, {stepName}, etc.
  isActive: boolean;
  created_at: ISODateString;
}

/**
 * Interface pour la configuration de communication
 */
export interface CommunicationConfig {
  maxNotificationsPerUser: number;
  notificationRetentionDays: number;
  pingRetentionDays: number;
  messageRetentionDays: number;
  enableRealtime: boolean;
  enableEmailNotifications: boolean;
  enablePushNotifications: boolean;
  rateLimiting: {
    notificationsPerMinute: number;
    pingsPerMinute: number;
    messagesPerMinute: number;
  };
}
