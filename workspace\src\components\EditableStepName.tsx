
import React, { useState, useEffect, useRef } from 'react';
import { Input } from '@/components/ui/input';

interface EditableStepNameProps {
  name: string;
  onSave: (newName: string) => void;
}

export const EditableStepName: React.FC<EditableStepNameProps> = ({ name, onSave }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(name);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (isEditing) {
      inputRef.current?.focus();
      inputRef.current?.select();
    }
  }, [isEditing]);

  const handleDoubleClick = () => {
    setIsEditing(true);
  };

  const handleBlur = () => {
    if (value.trim() && value !== name) {
      onSave(value);
    } else {
      setValue(name);
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleBlur();
    } else if (e.key === 'Escape') {
      setValue(name);
      setIsEditing(false);
    }
  };

  if (isEditing) {
    return (
      <Input
        ref={inputRef}
        value={value}
        onChange={(e) => setValue(e.target.value)}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        className="font-medium w-full max-w-[280px]"
      />
    );
  }

  return (
    <span 
      onDoubleClick={handleDoubleClick}
      className="font-medium cursor-pointer hover:text-blue-600 transition-colors"
    >
      {name}
    </span>
  );
};
