/**
 * Utilitaire de journalisation sécurisée des accès aux données personnelles
 * Conforme aux recommandations de la CNIL pour la protection des données
 */

import { supabase } from "@/integrations/supabase/client";
import { pseudonymizeText } from "./pseudonymization";

/**
 * Types d'opérations sur les données personnelles
 */
export enum DataOperationType {
  READ = 'read',
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  EXPORT = 'export',
  ANONYMIZE = 'anonymize'
}

/**
 * Interface pour les entrées de journal
 */
interface LogEntry {
  operation: DataOperationType;
  table: string;
  record_id?: string;
  user_id?: string;
  details?: string;
  timestamp: string;
}

/**
 * Table de journalisation dans Supabase
 * Note: Cette table doit être créée manuellement dans Supabase
 * avec la structure appropriée
 */
const LOGS_TABLE = 'data_access_logs';

/**
 * Vérifie si la table de journalisation existe
 * @returns true si la table existe
 */
async function checkLogsTableExists(): Promise<boolean> {
  // Désactiver complètement la journalisation en base de données pour éviter les erreurs
  // Retourner toujours false pour utiliser uniquement la journalisation locale
  return false;

  // Code original commenté pour référence future
  /*
  try {
    // Utiliser une approche plus simple pour vérifier si la table existe
    // Éviter d'utiliser count(*) qui peut causer des erreurs 400
    const { data, error } = await supabase
      .from(LOGS_TABLE)
      .select('id')
      .limit(1);

    // Si pas d'erreur, la table existe
    return !error;
  } catch (error) {
    // En cas d'erreur, considérer que la table n'existe pas
    console.warn('La table de journalisation n\'existe pas ou n\'est pas accessible:', error);
    return false;
  }
  */
}

/**
 * Crée la table de journalisation si elle n'existe pas
 * Note: Cette fonction nécessite des droits d'administration sur la base de données
 * et ne fonctionnera pas avec les droits standard de l'application
 */
export async function createLogsTableIfNeeded(): Promise<boolean> {
  const exists = await checkLogsTableExists();
  if (exists) return true;

  try {
    // Cette opération nécessite des droits d'administration
    // et devrait être effectuée lors de la configuration initiale
    console.warn('La création automatique de la table de journalisation n\'est pas supportée');
    console.warn('Veuillez créer manuellement la table data_access_logs dans Supabase');
    return false;
  } catch (error) {
    console.error('Erreur lors de la création de la table de journalisation:', error);
    return false;
  }
}

/**
 * Journalise un accès aux données personnelles
 * @param operation - Type d'opération
 * @param table - Table concernée
 * @param recordId - ID de l'enregistrement (optionnel)
 * @param userId - ID de l'utilisateur (optionnel)
 * @param details - Détails supplémentaires (optionnel)
 * @returns true si la journalisation a réussi
 */
export async function logDataAccess(
  operation: DataOperationType,
  table: string,
  recordId?: string,
  userId?: string,
  details?: string
): Promise<boolean> {
  try {
    // Créer l'entrée de journal
    const logEntry: LogEntry = {
      operation,
      table,
      record_id: recordId,
      user_id: userId,
      // Pseudonymiser les détails pour éviter de stocker des données sensibles dans les logs
      details: details ? pseudonymizeText(details) : undefined,
      timestamp: new Date().toISOString()
    };

    // Journaliser localement dans tous les cas pour le débogage
    console.info('Journal d\'accès aux données:', logEntry);

    // Vérifier si la journalisation en base de données est activée
    const logsEnabled = await checkLogsTableExists();
    if (!logsEnabled) {
      // Si la table n'existe pas, ne pas essayer d'insérer en base
      return true;
    }

    try {
      // Insérer dans la table de journalisation
      const { error } = await supabase
        .from(LOGS_TABLE)
        .insert(logEntry);

      if (error) {
        // En cas d'erreur, journaliser l'erreur mais ne pas bloquer l'application
        console.warn('Erreur lors de la journalisation en base de données:', error);
        return true; // Retourner true quand même pour ne pas bloquer l'application
      }

      return true;
    } catch (insertError) {
      // En cas d'exception lors de l'insertion, journaliser l'erreur mais ne pas bloquer
      console.warn('Exception lors de la journalisation en base de données:', insertError);
      return true; // Retourner true quand même pour ne pas bloquer l'application
    }
  } catch (error) {
    // En cas d'erreur générale, journaliser l'erreur mais ne pas bloquer
    console.error('Erreur générale lors de la journalisation:', error);
    return true; // Retourner true quand même pour ne pas bloquer l'application
  }
}

/**
 * Journalise un accès en lecture aux données personnelles
 * @param table - Table concernée
 * @param recordId - ID de l'enregistrement (optionnel)
 * @param userId - ID de l'utilisateur (optionnel)
 * @param details - Détails supplémentaires (optionnel)
 */
export function logRead(table: string, recordId?: string, userId?: string, details?: string): void {
  logDataAccess(DataOperationType.READ, table, recordId, userId, details).catch(console.error);
}

/**
 * Journalise une création de données personnelles
 * @param table - Table concernée
 * @param recordId - ID de l'enregistrement (optionnel)
 * @param userId - ID de l'utilisateur (optionnel)
 * @param details - Détails supplémentaires (optionnel)
 */
export function logCreate(table: string, recordId?: string, userId?: string, details?: string): void {
  logDataAccess(DataOperationType.CREATE, table, recordId, userId, details).catch(console.error);
}

/**
 * Journalise une mise à jour de données personnelles
 * @param table - Table concernée
 * @param recordId - ID de l'enregistrement (optionnel)
 * @param userId - ID de l'utilisateur (optionnel)
 * @param details - Détails supplémentaires (optionnel)
 */
export function logUpdate(table: string, recordId?: string, userId?: string, details?: string): void {
  logDataAccess(DataOperationType.UPDATE, table, recordId, userId, details).catch(console.error);
}

/**
 * Journalise une suppression de données personnelles
 * @param table - Table concernée
 * @param recordId - ID de l'enregistrement (optionnel)
 * @param userId - ID de l'utilisateur (optionnel)
 * @param details - Détails supplémentaires (optionnel)
 */
export function logDelete(table: string, recordId?: string, userId?: string, details?: string): void {
  logDataAccess(DataOperationType.DELETE, table, recordId, userId, details).catch(console.error);
}
