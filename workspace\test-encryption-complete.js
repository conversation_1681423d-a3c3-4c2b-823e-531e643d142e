/**
 * Test complet du système de chiffrement
 * À exécuter dans la console du navigateur sur http://localhost:8082
 */

// Test 1: Vérifier que les fonctions sont disponibles
console.log("🔍 Test 1: Vérification des fonctions de chiffrement");
try {
    // Importer les fonctions depuis le module
    const { encryptText, safeDecrypt, isEncrypted } = await import('./src/utils/encryption.ts');
    console.log("✅ Fonctions importées avec succès");
    
    // Test 2: Chiffrement d'un texte simple
    console.log("\n🔐 Test 2: Chiffrement d'un texte");
    const testText = "Test Client SARL";
    const encrypted = await encryptText(testText);
    console.log(`Texte original: "${testText}"`);
    console.log(`Texte chiffré: "${encrypted}"`);
    console.log(`Préfixe correct: ${encrypted.startsWith('enc:')}`);
    
    // Test 3: Déchiffrement
    console.log("\n🔓 Test 3: Déchiffrement");
    const decrypted = await safeDecrypt(encrypted);
    console.log(`Texte déchiffré: "${decrypted}"`);
    console.log(`Déchiffrement correct: ${decrypted === testText}`);
    
    // Test 4: Détection de chiffrement
    console.log("\n🔍 Test 4: Détection de chiffrement");
    console.log(`isEncrypted("${encrypted}"): ${isEncrypted(encrypted)}`);
    console.log(`isEncrypted("texte normal"): ${isEncrypted("texte normal")}`);
    console.log(`isEncrypted("enc:faux"): ${isEncrypted("enc:faux")}`);
    
    // Test 5: Test avec le vrai nom du client
    console.log("\n🏢 Test 5: Déchiffrement du vrai client");
    const realEncrypted = "enc:9U9+hDW3zTdSONiF9nTsDxXyI6oNka0agKfeSJTxK5ap";
    const realDecrypted = await safeDecrypt(realEncrypted);
    console.log(`Nom réel du client: "${realDecrypted}"`);
    
    console.log("\n✅ Tous les tests réussis !");
    
} catch (error) {
    console.error("❌ Erreur lors des tests:", error);
}

// Test 6: Vérifier l'état des clients dans l'application
console.log("\n📊 Test 6: État des clients dans l'application");
// Cette partie nécessite d'être dans le contexte de l'application
