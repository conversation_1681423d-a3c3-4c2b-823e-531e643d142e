-- Migration pour ajouter la date de rendu du dossier aux clients
-- Cette colonne permettra de trier et afficher les dates de rendu sur les cartes clients

-- 1. Ajouter la colonne due_date à la table clients
ALTER TABLE public.clients 
ADD COLUMN due_date DATE;

-- 2. Ajouter un commentaire pour documenter la colonne
COMMENT ON COLUMN public.clients.due_date IS 'Date de rendu prévue du dossier client';

-- 3. Créer un index pour optimiser les requêtes de tri par date de rendu
CREATE INDEX idx_clients_due_date ON public.clients(due_date);

-- 4. Journaliser l'ajout de la colonne
INSERT INTO public.data_access_logs (
  operation,
  table_name,
  details,
  timestamp
) VALUES (
  'update',
  'clients',
  'Ajout de la colonne due_date pour la date de rendu du dossier',
  NOW()
);

-- 5. Commentaire sur la migration
COMMENT ON TABLE public.clients IS 'Table des clients avec informations de base et date de rendu du dossier';
