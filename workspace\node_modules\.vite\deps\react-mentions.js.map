{"version": 3, "sources": ["../../invariant/browser.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/arrayWithoutHoles.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/iterableToArray.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/nonIterableSpread.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/toConsumableArray.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/extends.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/classCallCheck.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/createClass.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/assertThisInitialized.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/setPrototypeOf.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/inherits.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/typeof.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/possibleConstructorReturn.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/getPrototypeOf.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/defineProperty.js", "../../react-mentions/dist/react-mentions.esm.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/arrayWithHoles.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/iterableToArrayLimit.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/nonIterableRest.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/slicedToArray.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js", "../../react-mentions/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js", "../../substyle/es6/PropsDecoratorProvider.js", "../../@babel/runtime/helpers/esm/typeof.js", "../../@babel/runtime/helpers/esm/toPrimitive.js", "../../@babel/runtime/helpers/esm/toPropertyKey.js", "../../@babel/runtime/helpers/esm/defineProperty.js", "../../@babel/runtime/helpers/esm/arrayLikeToArray.js", "../../@babel/runtime/helpers/esm/arrayWithoutHoles.js", "../../@babel/runtime/helpers/esm/iterableToArray.js", "../../@babel/runtime/helpers/esm/unsupportedIterableToArray.js", "../../@babel/runtime/helpers/esm/nonIterableSpread.js", "../../@babel/runtime/helpers/esm/toConsumableArray.js", "../../substyle/es6/utils.js", "../../substyle/es6/filterKeys.js", "../../substyle/es6/pickStyles.js", "../../substyle/es6/defaultPropsDecorator.js", "../../substyle/es6/createSubstyle.js", "../../substyle/es6/coerceSelection.js", "../../substyle/es6/memoize.js", "../../substyle/es6/inline.js", "../../substyle/es6/useStyles.js", "../../substyle/es6/index.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nvar invariant = function(condition, format, a, b, c, d, e, f) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  }\n\n  if (!condition) {\n    var error;\n    if (format === undefined) {\n      error = new Error(\n        'Minified exception occurred; use the non-minified dev environment ' +\n        'for the full error message and additional helpful warnings.'\n      );\n    } else {\n      var args = [a, b, c, d, e, f];\n      var argIndex = 0;\n      error = new Error(\n        format.replace(/%s/g, function() { return args[argIndex++]; })\n      );\n      error.name = 'Invariant Violation';\n    }\n\n    error.framesToPop = 1; // we don't care about invariant's own frame\n    throw error;\n  }\n};\n\nmodule.exports = invariant;\n", "export default function _arrayWithoutHoles(arr) {\n  if (Array.isArray(arr)) {\n    for (var i = 0, arr2 = new Array(arr.length); i < arr.length; i++) {\n      arr2[i] = arr[i];\n    }\n\n    return arr2;\n  }\n}", "export default function _iterableToArray(iter) {\n  if (Symbol.iterator in Object(iter) || Object.prototype.toString.call(iter) === \"[object Arguments]\") return Array.from(iter);\n}", "export default function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance\");\n}", "import arrayWithoutHoles from \"./arrayWithoutHoles\";\nimport iterableToArray from \"./iterableToArray\";\nimport nonIterableSpread from \"./nonIterableSpread\";\nexport default function _toConsumableArray(arr) {\n  return arrayWithoutHoles(arr) || iterableToArray(arr) || nonIterableSpread();\n}", "export default function _extends() {\n  _extends = Object.assign || function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n\n  return _extends.apply(this, arguments);\n}", "export default function _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}", "function _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\n\nexport default function _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  return Constructor;\n}", "export default function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n\n  return self;\n}", "export default function _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf || function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n\n  return _setPrototypeOf(o, p);\n}", "import setPrototypeOf from \"./setPrototypeOf\";\nexport default function _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) setPrototypeOf(subClass, superClass);\n}", "function _typeof2(obj) { if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") { _typeof2 = function _typeof2(obj) { return typeof obj; }; } else { _typeof2 = function _typeof2(obj) { return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj; }; } return _typeof2(obj); }\n\nexport default function _typeof(obj) {\n  if (typeof Symbol === \"function\" && _typeof2(Symbol.iterator) === \"symbol\") {\n    _typeof = function _typeof(obj) {\n      return _typeof2(obj);\n    };\n  } else {\n    _typeof = function _typeof(obj) {\n      return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : _typeof2(obj);\n    };\n  }\n\n  return _typeof(obj);\n}", "import _typeof from \"../../helpers/esm/typeof\";\nimport assertThisInitialized from \"./assertThisInitialized\";\nexport default function _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  }\n\n  return assertThisInitialized(self);\n}", "export default function _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}", "export default function _defineProperty(obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n\n  return obj;\n}", "import _toConsumableArray from '@babel/runtime/helpers/esm/toConsumableArray';\nimport _extends from '@babel/runtime/helpers/esm/extends';\nimport _classCallCheck from '@babel/runtime/helpers/esm/classCallCheck';\nimport _createClass from '@babel/runtime/helpers/esm/createClass';\nimport _assertThisInitialized from '@babel/runtime/helpers/esm/assertThisInitialized';\nimport _inherits from '@babel/runtime/helpers/esm/inherits';\nimport _possibleConstructorReturn from '@babel/runtime/helpers/esm/possibleConstructorReturn';\nimport _getPrototypeOf from '@babel/runtime/helpers/esm/getPrototypeOf';\nimport _defineProperty from '@babel/runtime/helpers/esm/defineProperty';\nimport React, { Children, useState, useEffect } from 'react';\nimport invariant from 'invariant';\nimport _slicedToArray from '@babel/runtime/helpers/esm/slicedToArray';\nimport _objectWithoutProperties from '@babel/runtime/helpers/esm/objectWithoutProperties';\nimport useStyles, { inline } from 'substyle';\nimport PropTypes from 'prop-types';\nimport ReactDOM from 'react-dom';\n\n// escape RegExp special characters https://stackoverflow.com/a/9310752/5142490\nvar escapeRegex = function escapeRegex(str) {\n  return str.replace(/[-[\\]{}()*+?.,\\\\^$|#\\s]/g, '\\\\$&');\n};\n\nvar PLACEHOLDERS = {\n  id: '__id__',\n  display: '__display__'\n};\n\nvar findPositionOfCapturingGroup = function findPositionOfCapturingGroup(markup, parameterName) {\n  invariant(parameterName === 'id' || parameterName === 'display', \"Second arg must be either \\\"id\\\" or \\\"display\\\", got: \\\"\".concat(parameterName, \"\\\"\")); // find positions of placeholders in the markup\n\n  var indexDisplay = markup.indexOf(PLACEHOLDERS.display);\n  var indexId = markup.indexOf(PLACEHOLDERS.id); // set indices to null if not found\n\n  if (indexDisplay < 0) indexDisplay = null;\n  if (indexId < 0) indexId = null; // markup must contain one of the mandatory placeholders\n\n  invariant(indexDisplay !== null || indexId !== null, \"The markup '\".concat(markup, \"' does not contain either of the placeholders '__id__' or '__display__'\"));\n\n  if (indexDisplay !== null && indexId !== null) {\n    // both placeholders are used, return 0 or 1 depending on the position of the requested parameter\n    return parameterName === 'id' && indexId <= indexDisplay || parameterName === 'display' && indexDisplay <= indexId ? 0 : 1;\n  } // just one placeholder is being used, we'll use the captured string for both parameters\n\n\n  return 0;\n};\n\nvar combineRegExps = function combineRegExps(regExps) {\n  var serializedRegexParser = /^\\/(.+)\\/(\\w+)?$/;\n  return new RegExp(regExps.map(function (regex) {\n    var _serializedRegexParse = serializedRegexParser.exec(regex.toString()),\n        _serializedRegexParse2 = _slicedToArray(_serializedRegexParse, 3),\n        regexString = _serializedRegexParse2[1],\n        regexFlags = _serializedRegexParse2[2];\n\n    invariant(!regexFlags, \"RegExp flags are not supported. Change /\".concat(regexString, \"/\").concat(regexFlags, \" into /\").concat(regexString, \"/\"));\n    return \"(\".concat(regexString, \")\");\n  }).join('|'), 'g');\n};\n\nvar countPlaceholders = function countPlaceholders(markup) {\n  var count = 0;\n  if (markup.indexOf('__id__') >= 0) count++;\n  if (markup.indexOf('__display__') >= 0) count++;\n  return count;\n};\n\nvar emptyFn = function emptyFn() {}; // Finds all occurrences of the markup in the value and calls the `markupIteratee` callback for each of them.\n// The optional `textIteratee` callback is called for each plain text ranges in between these markup occurrences.\n\n\nvar iterateMentionsMarkup = function iterateMentionsMarkup(value, config, markupIteratee) {\n  var textIteratee = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : emptyFn;\n  var regex = combineRegExps(config.map(function (c) {\n    return c.regex;\n  }));\n  var accOffset = 2; // first is whole match, second is the for the capturing group of first regexp component\n\n  var captureGroupOffsets = config.map(function (_ref) {\n    var markup = _ref.markup;\n    var result = accOffset; // + 1 is for the capturing group we add around each regexp component in combineRegExps\n\n    accOffset += countPlaceholders(markup) + 1;\n    return result;\n  });\n  var match;\n  var start = 0;\n  var currentPlainTextIndex = 0; // detect all mention markup occurrences in the value and iterate the matches\n\n  while ((match = regex.exec(value)) !== null) {\n    var offset = captureGroupOffsets.find(function (o) {\n      return !!match[o];\n    }); // eslint-disable-line no-loop-func\n\n    var mentionChildIndex = captureGroupOffsets.indexOf(offset);\n    var _config$mentionChildI = config[mentionChildIndex],\n        markup = _config$mentionChildI.markup,\n        displayTransform = _config$mentionChildI.displayTransform;\n    var idPos = offset + findPositionOfCapturingGroup(markup, 'id');\n    var displayPos = offset + findPositionOfCapturingGroup(markup, 'display');\n    var id = match[idPos];\n    var display = displayTransform(id, match[displayPos]);\n    var substr = value.substring(start, match.index);\n    textIteratee(substr, start, currentPlainTextIndex);\n    currentPlainTextIndex += substr.length;\n    markupIteratee(match[0], match.index, currentPlainTextIndex, id, display, mentionChildIndex, start);\n    currentPlainTextIndex += display.length;\n    start = regex.lastIndex;\n  }\n\n  if (start < value.length) {\n    textIteratee(value.substring(start), start, currentPlainTextIndex);\n  }\n};\n\nvar getPlainText = function getPlainText(value, config) {\n  var result = '';\n  iterateMentionsMarkup(value, config, function (match, index, plainTextIndex, id, display) {\n    result += display;\n  }, function (plainText) {\n    result += plainText;\n  });\n  return result;\n};\n\n// in the marked up value string.\n// If the passed character index lies inside a mention, the value of `inMarkupCorrection` defines the\n// correction to apply:\n//   - 'START' to return the index of the mention markup's first char (default)\n//   - 'END' to return the index after its last char\n//   - 'NULL' to return null\n\nvar mapPlainTextIndex = function mapPlainTextIndex(value, config, indexInPlainText) {\n  var inMarkupCorrection = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 'START';\n\n  if (typeof indexInPlainText !== 'number') {\n    return indexInPlainText;\n  }\n\n  var result;\n\n  var textIteratee = function textIteratee(substr, index, substrPlainTextIndex) {\n    if (result !== undefined) return;\n\n    if (substrPlainTextIndex + substr.length >= indexInPlainText) {\n      // found the corresponding position in the current plain text range\n      result = index + indexInPlainText - substrPlainTextIndex;\n    }\n  };\n\n  var markupIteratee = function markupIteratee(markup, index, mentionPlainTextIndex, id, display, childIndex, lastMentionEndIndex) {\n    if (result !== undefined) return;\n\n    if (mentionPlainTextIndex + display.length > indexInPlainText) {\n      // found the corresponding position inside current match,\n      // return the index of the first or after the last char of the matching markup\n      // depending on whether the `inMarkupCorrection`\n      if (inMarkupCorrection === 'NULL') {\n        result = null;\n      } else {\n        result = index + (inMarkupCorrection === 'END' ? markup.length : 0);\n      }\n    }\n  };\n\n  iterateMentionsMarkup(value, config, markupIteratee, textIteratee); // when a mention is at the end of the value and we want to get the caret position\n  // at the end of the string, result is undefined\n\n  return result === undefined ? value.length : result;\n};\n\nvar spliceString = function spliceString(str, start, end, insert) {\n  return str.substring(0, start) + insert + str.substring(end);\n};\n\n// guided by the textarea text selection ranges before and after the change\n\nvar applyChangeToValue = function applyChangeToValue(value, plainTextValue, _ref, config) {\n  var selectionStartBefore = _ref.selectionStartBefore,\n      selectionEndBefore = _ref.selectionEndBefore,\n      selectionEndAfter = _ref.selectionEndAfter;\n  var oldPlainTextValue = getPlainText(value, config);\n  var lengthDelta = oldPlainTextValue.length - plainTextValue.length;\n\n  if (selectionStartBefore === 'undefined') {\n    selectionStartBefore = selectionEndAfter + lengthDelta;\n  }\n\n  if (selectionEndBefore === 'undefined') {\n    selectionEndBefore = selectionStartBefore;\n  } // Fixes an issue with replacing combined characters for complex input. Eg like acented letters on OSX\n\n\n  if (selectionStartBefore === selectionEndBefore && selectionEndBefore === selectionEndAfter && oldPlainTextValue.length === plainTextValue.length) {\n    selectionStartBefore = selectionStartBefore - 1;\n  } // extract the insertion from the new plain text value\n\n\n  var insert = plainTextValue.slice(selectionStartBefore, selectionEndAfter); // handling for Backspace key with no range selection\n\n  var spliceStart = Math.min(selectionStartBefore, selectionEndAfter);\n  var spliceEnd = selectionEndBefore;\n\n  if (selectionStartBefore === selectionEndAfter) {\n    // handling for Delete key with no range selection\n    spliceEnd = Math.max(selectionEndBefore, selectionStartBefore + lengthDelta);\n  }\n\n  var mappedSpliceStart = mapPlainTextIndex(value, config, spliceStart, 'START');\n  var mappedSpliceEnd = mapPlainTextIndex(value, config, spliceEnd, 'END');\n  var controlSpliceStart = mapPlainTextIndex(value, config, spliceStart, 'NULL');\n  var controlSpliceEnd = mapPlainTextIndex(value, config, spliceEnd, 'NULL');\n  var willRemoveMention = controlSpliceStart === null || controlSpliceEnd === null;\n  var newValue = spliceString(value, mappedSpliceStart, mappedSpliceEnd, insert);\n\n  if (!willRemoveMention) {\n    // test for auto-completion changes\n    var controlPlainTextValue = getPlainText(newValue, config);\n\n    if (controlPlainTextValue !== plainTextValue) {\n      // some auto-correction is going on\n      // find start of diff\n      spliceStart = 0;\n\n      while (plainTextValue[spliceStart] === controlPlainTextValue[spliceStart]) {\n        spliceStart++;\n      } // extract auto-corrected insertion\n\n\n      insert = plainTextValue.slice(spliceStart, selectionEndAfter); // find index of the unchanged remainder\n\n      spliceEnd = oldPlainTextValue.lastIndexOf(plainTextValue.substring(selectionEndAfter)); // re-map the corrected indices\n\n      mappedSpliceStart = mapPlainTextIndex(value, config, spliceStart, 'START');\n      mappedSpliceEnd = mapPlainTextIndex(value, config, spliceEnd, 'END');\n      newValue = spliceString(value, mappedSpliceStart, mappedSpliceEnd, insert);\n    }\n  }\n\n  return newValue;\n};\n\n// returns a the index of of the first char of the mention in the plain text.\n// If indexInPlainText does not lie inside a mention, returns indexInPlainText.\n\nvar findStartOfMentionInPlainText = function findStartOfMentionInPlainText(value, config, indexInPlainText) {\n  var result = indexInPlainText;\n  var foundMention = false;\n\n  var markupIteratee = function markupIteratee(markup, index, mentionPlainTextIndex, id, display, childIndex, lastMentionEndIndex) {\n    if (mentionPlainTextIndex <= indexInPlainText && mentionPlainTextIndex + display.length > indexInPlainText) {\n      result = mentionPlainTextIndex;\n      foundMention = true;\n    }\n  };\n\n  iterateMentionsMarkup(value, config, markupIteratee);\n\n  if (foundMention) {\n    return result;\n  }\n};\n\nvar getMentions = function getMentions(value, config) {\n  var mentions = [];\n  iterateMentionsMarkup(value, config, function (match, index, plainTextIndex, id, display, childIndex, start) {\n    mentions.push({\n      id: id,\n      display: display,\n      childIndex: childIndex,\n      index: index,\n      plainTextIndex: plainTextIndex\n    });\n  });\n  return mentions;\n};\n\nvar getSuggestionHtmlId = function getSuggestionHtmlId(prefix, id) {\n  return \"\".concat(prefix, \"-\").concat(id);\n};\n\nvar countSuggestions = function countSuggestions(suggestions) {\n  return Object.values(suggestions).reduce(function (acc, _ref) {\n    var results = _ref.results;\n    return acc + results.length;\n  }, 0);\n};\n\nvar getEndOfLastMention = function getEndOfLastMention(value, config) {\n  var mentions = getMentions(value, config);\n  var lastMention = mentions[mentions.length - 1];\n  return lastMention ? lastMention.plainTextIndex + lastMention.display.length : 0;\n};\n\nvar markupToRegex = function markupToRegex(markup) {\n  var escapedMarkup = escapeRegex(markup);\n  var charAfterDisplay = markup[markup.indexOf(PLACEHOLDERS.display) + PLACEHOLDERS.display.length];\n  var charAfterId = markup[markup.indexOf(PLACEHOLDERS.id) + PLACEHOLDERS.id.length];\n  return new RegExp(escapedMarkup.replace(PLACEHOLDERS.display, \"([^\".concat(escapeRegex(charAfterDisplay || ''), \"]+?)\")).replace(PLACEHOLDERS.id, \"([^\".concat(escapeRegex(charAfterId || ''), \"]+?)\")));\n};\n\nvar readConfigFromChildren = function readConfigFromChildren(children) {\n  return Children.toArray(children).map(function (_ref) {\n    var _ref$props = _ref.props,\n        markup = _ref$props.markup,\n        regex = _ref$props.regex,\n        displayTransform = _ref$props.displayTransform;\n    return {\n      markup: markup,\n      regex: regex ? coerceCapturingGroups(regex, markup) : markupToRegex(markup),\n      displayTransform: displayTransform || function (id, display) {\n        return display || id;\n      }\n    };\n  });\n}; // make sure that the custom regex defines the correct number of capturing groups\n\n\nvar coerceCapturingGroups = function coerceCapturingGroups(regex, markup) {\n  var numberOfGroups = new RegExp(regex.toString() + '|').exec('').length - 1;\n  var numberOfPlaceholders = countPlaceholders(markup);\n  invariant(numberOfGroups === numberOfPlaceholders, \"Number of capturing groups in RegExp \".concat(regex.toString(), \" (\").concat(numberOfGroups, \") does not match the number of placeholders in the markup '\").concat(markup, \"' (\").concat(numberOfPlaceholders, \")\"));\n  return regex;\n};\n\nvar makeMentionsMarkup = function makeMentionsMarkup(markup, id, display) {\n  return markup.replace(PLACEHOLDERS.id, id).replace(PLACEHOLDERS.display, display);\n};\n\n// This contains all the latin letters and the regex that match these letters with diacritics\n// https://stackoverflow.com/questions/990904/remove-accents-diacritics-in-a-string-in-javascript\nvar lettersDiacritics = [{\n  base: 'A',\n  letters: /(&#65;|&#9398;|&#65313;|&#192;|&#193;|&#194;|&#7846;|&#7844;|&#7850;|&#7848;|&#195;|&#256;|&#258;|&#7856;|&#7854;|&#7860;|&#7858;|&#550;|&#480;|&#196;|&#478;|&#7842;|&#197;|&#506;|&#461;|&#512;|&#514;|&#7840;|&#7852;|&#7862;|&#7680;|&#260;|&#570;|&#11375;|[\\u0041\\u24B6\\uFF21\\u00C0\\u00C1\\u00C2\\u1EA6\\u1EA4\\u1EAA\\u1EA8\\u00C3\\u0100\\u0102\\u1EB0\\u1EAE\\u1EB4\\u1EB2\\u0226\\u01E0\\u00C4\\u01DE\\u1EA2\\u00C5\\u01FA\\u01CD\\u0200\\u0202\\u1EA0\\u1EAC\\u1EB6\\u1E00\\u0104\\u023A\\u2C6F])/g\n}, {\n  base: 'AA',\n  letters: /(&#42802;|[\\uA732])/g\n}, {\n  base: 'AE',\n  letters: /(&#198;|&#508;|&#482;|[\\u00C6\\u01FC\\u01E2])/g\n}, {\n  base: 'AO',\n  letters: /(&#42804;|[\\uA734])/g\n}, {\n  base: 'AU',\n  letters: /(&#42806;|[\\uA736])/g\n}, {\n  base: 'AV',\n  letters: /(&#42808;|&#42810;|[\\uA738\\uA73A])/g\n}, {\n  base: 'AY',\n  letters: /(&#42812;|[\\uA73C])/g\n}, {\n  base: 'B',\n  letters: /(&#66;|&#9399;|&#65314;|&#7682;|&#7684;|&#7686;|&#579;|&#386;|&#385;|[\\u0042\\u24B7\\uFF22\\u1E02\\u1E04\\u1E06\\u0243\\u0182\\u0181])/g\n}, {\n  base: 'C',\n  letters: /(&#67;|&#9400;|&#65315;|&#262;|&#264;|&#266;|&#268;|&#199;|&#7688;|&#391;|&#571;|&#42814;|[\\u0043\\u24B8\\uFF23\\u0106\\u0108\\u010A\\u010C\\u00C7\\u1E08\\u0187\\u023B\\uA73E])/g\n}, {\n  base: 'D',\n  letters: /(&#68;|&#9401;|&#65316;|&#7690;|&#270;|&#7692;|&#7696;|&#7698;|&#7694;|&#272;|&#395;|&#394;|&#393;|&#42873;|&#208;|[\\u0044\\u24B9\\uFF24\\u1E0A\\u010E\\u1E0C\\u1E10\\u1E12\\u1E0E\\u0110\\u018B\\u018A\\u0189\\uA779\\u00D0])/g\n}, {\n  base: 'DZ',\n  letters: /(&#497;|&#452;|[\\u01F1\\u01C4])/g\n}, {\n  base: 'Dz',\n  letters: /(&#498;|&#453;|[\\u01F2\\u01C5])/g\n}, {\n  base: 'E',\n  letters: /(&#69;|&#9402;|&#65317;|&#200;|&#201;|&#202;|&#7872;|&#7870;|&#7876;|&#7874;|&#7868;|&#274;|&#7700;|&#7702;|&#276;|&#278;|&#203;|&#7866;|&#282;|&#516;|&#518;|&#7864;|&#7878;|&#552;|&#7708;|&#280;|&#7704;|&#7706;|&#400;|&#398;|[\\u0045\\u24BA\\uFF25\\u00C8\\u00C9\\u00CA\\u1EC0\\u1EBE\\u1EC4\\u1EC2\\u1EBC\\u0112\\u1E14\\u1E16\\u0114\\u0116\\u00CB\\u1EBA\\u011A\\u0204\\u0206\\u1EB8\\u1EC6\\u0228\\u1E1C\\u0118\\u1E18\\u1E1A\\u0190\\u018E])/g\n}, {\n  base: 'F',\n  letters: /(&#70;|&#9403;|&#65318;|&#7710;|&#401;|&#42875;|[\\u0046\\u24BB\\uFF26\\u1E1E\\u0191\\uA77B])/g\n}, {\n  base: 'G',\n  letters: /(&#71;|&#9404;|&#65319;|&#500;|&#284;|&#7712;|&#286;|&#288;|&#486;|&#290;|&#484;|&#403;|&#42912;|&#42877;|&#42878;|[\\u0047\\u24BC\\uFF27\\u01F4\\u011C\\u1E20\\u011E\\u0120\\u01E6\\u0122\\u01E4\\u0193\\uA7A0\\uA77D\\uA77E])/g\n}, {\n  base: 'H',\n  letters: /(&#72;|&#9405;|&#65320;|&#292;|&#7714;|&#7718;|&#542;|&#7716;|&#7720;|&#7722;|&#294;|&#11367;|&#11381;|&#42893;|[\\u0048\\u24BD\\uFF28\\u0124\\u1E22\\u1E26\\u021E\\u1E24\\u1E28\\u1E2A\\u0126\\u2C67\\u2C75\\uA78D])/g\n}, {\n  base: 'I',\n  letters: /(&#73;|&#9406;|&#65321;|&#204;|&#205;|&#206;|&#296;|&#298;|&#300;|&#304;|&#207;|&#7726;|&#7880;|&#463;|&#520;|&#522;|&#7882;|&#302;|&#7724;|&#407;|[\\u0049\\u24BE\\uFF29\\u00CC\\u00CD\\u00CE\\u0128\\u012A\\u012C\\u0130\\u00CF\\u1E2E\\u1EC8\\u01CF\\u0208\\u020A\\u1ECA\\u012E\\u1E2C\\u0197])/g\n}, {\n  base: 'J',\n  letters: /(&#74;|&#9407;|&#65322;|&#308;|&#584;|[\\u004A\\u24BF\\uFF2A\\u0134\\u0248])/g\n}, {\n  base: 'K',\n  letters: /(&#75;|&#9408;|&#65323;|&#7728;|&#488;|&#7730;|&#310;|&#7732;|&#408;|&#11369;|&#42816;|&#42818;|&#42820;|&#42914;|[\\u004B\\u24C0\\uFF2B\\u1E30\\u01E8\\u1E32\\u0136\\u1E34\\u0198\\u2C69\\uA740\\uA742\\uA744\\uA7A2])/g\n}, {\n  base: 'L',\n  letters: /(&#76;|&#9409;|&#65324;|&#319;|&#313;|&#317;|&#7734;|&#7736;|&#315;|&#7740;|&#7738;|&#321;|&#573;|&#11362;|&#11360;|&#42824;|&#42822;|&#42880;|[\\u004C\\u24C1\\uFF2C\\u013F\\u0139\\u013D\\u1E36\\u1E38\\u013B\\u1E3C\\u1E3A\\u0141\\u023D\\u2C62\\u2C60\\uA748\\uA746\\uA780])/g\n}, {\n  base: 'LJ',\n  letters: /(&#455;|[\\u01C7])/g\n}, {\n  base: 'Lj',\n  letters: /(&#456;|[\\u01C8])/g\n}, {\n  base: 'M',\n  letters: /(&#77;|&#9410;|&#65325;|&#7742;|&#7744;|&#7746;|&#11374;|&#412;|[\\u004D\\u24C2\\uFF2D\\u1E3E\\u1E40\\u1E42\\u2C6E\\u019C])/g\n}, {\n  base: 'N',\n  letters: /(&#78;|&#9411;|&#65326;|&#504;|&#323;|&#209;|&#7748;|&#327;|&#7750;|&#325;|&#7754;|&#7752;|&#544;|&#413;|&#42896;|&#42916;|&#330;|[\\u004E\\u24C3\\uFF2E\\u01F8\\u0143\\u00D1\\u1E44\\u0147\\u1E46\\u0145\\u1E4A\\u1E48\\u0220\\u019D\\uA790\\uA7A4\\u014A])/g\n}, {\n  base: 'NJ',\n  letters: /(&#458;|[\\u01CA])/g\n}, {\n  base: 'Nj',\n  letters: /(&#459;|[\\u01CB])/g\n}, {\n  base: 'O',\n  letters: /(&#79;|&#9412;|&#65327;|&#210;|&#211;|&#212;|&#7890;|&#7888;|&#7894;|&#7892;|&#213;|&#7756;|&#556;|&#7758;|&#332;|&#7760;|&#7762;|&#334;|&#558;|&#560;|&#214;|&#554;|&#7886;|&#336;|&#465;|&#524;|&#526;|&#416;|&#7900;|&#7898;|&#7904;|&#7902;|&#7906;|&#7884;|&#7896;|&#490;|&#492;|&#216;|&#510;|&#390;|&#415;|&#42826;|&#42828;|[\\u004F\\u24C4\\uFF2F\\u00D2\\u00D3\\u00D4\\u1ED2\\u1ED0\\u1ED6\\u1ED4\\u00D5\\u1E4C\\u022C\\u1E4E\\u014C\\u1E50\\u1E52\\u014E\\u022E\\u0230\\u00D6\\u022A\\u1ECE\\u0150\\u01D1\\u020C\\u020E\\u01A0\\u1EDC\\u1EDA\\u1EE0\\u1EDE\\u1EE2\\u1ECC\\u1ED8\\u01EA\\u01EC\\u00D8\\u01FE\\u0186\\u019F\\uA74A\\uA74C])/g\n}, {\n  base: 'OE',\n  letters: /(&#338;|[\\u0152])/g\n}, {\n  base: 'OI',\n  letters: /(&#418;|[\\u01A2])/g\n}, {\n  base: 'OO',\n  letters: /(&#42830;|[\\uA74E])/g\n}, {\n  base: 'OU',\n  letters: /(&#546;|[\\u0222])/g\n}, {\n  base: 'P',\n  letters: /(&#80;|&#9413;|&#65328;|&#7764;|&#7766;|&#420;|&#11363;|&#42832;|&#42834;|&#42836;|[\\u0050\\u24C5\\uFF30\\u1E54\\u1E56\\u01A4\\u2C63\\uA750\\uA752\\uA754])/g\n}, {\n  base: 'Q',\n  letters: /(&#81;|&#9414;|&#65329;|&#42838;|&#42840;|&#586;|[\\u0051\\u24C6\\uFF31\\uA756\\uA758\\u024A])/g\n}, {\n  base: 'R',\n  letters: /(&#82;|&#9415;|&#65330;|&#340;|&#7768;|&#344;|&#528;|&#530;|&#7770;|&#7772;|&#342;|&#7774;|&#588;|&#11364;|&#42842;|&#42918;|&#42882;|[\\u0052\\u24C7\\uFF32\\u0154\\u1E58\\u0158\\u0210\\u0212\\u1E5A\\u1E5C\\u0156\\u1E5E\\u024C\\u2C64\\uA75A\\uA7A6\\uA782])/g\n}, {\n  base: 'S',\n  letters: /(&#83;|&#9416;|&#65331;|&#7838;|&#346;|&#7780;|&#348;|&#7776;|&#352;|&#7782;|&#7778;|&#7784;|&#536;|&#350;|&#11390;|&#42920;|&#42884;|[\\u0053\\u24C8\\uFF33\\u1E9E\\u015A\\u1E64\\u015C\\u1E60\\u0160\\u1E66\\u1E62\\u1E68\\u0218\\u015E\\u2C7E\\uA7A8\\uA784])/g\n}, {\n  base: 'T',\n  letters: /(&#84;|&#9417;|&#65332;|&#7786;|&#356;|&#7788;|&#538;|&#354;|&#7792;|&#7790;|&#358;|&#428;|&#430;|&#574;|&#42886;|[\\u0054\\u24C9\\uFF34\\u1E6A\\u0164\\u1E6C\\u021A\\u0162\\u1E70\\u1E6E\\u0166\\u01AC\\u01AE\\u023E\\uA786])/g\n}, {\n  base: 'TH',\n  letters: /(&#222;|[\\u00DE])/g\n}, {\n  base: 'TZ',\n  letters: /(&#42792;|[\\uA728])/g\n}, {\n  base: 'U',\n  letters: /(&#85;|&#9418;|&#65333;|&#217;|&#218;|&#219;|&#360;|&#7800;|&#362;|&#7802;|&#364;|&#220;|&#475;|&#471;|&#469;|&#473;|&#7910;|&#366;|&#368;|&#467;|&#532;|&#534;|&#431;|&#7914;|&#7912;|&#7918;|&#7916;|&#7920;|&#7908;|&#7794;|&#370;|&#7798;|&#7796;|&#580;|[\\u0055\\u24CA\\uFF35\\u00D9\\u00DA\\u00DB\\u0168\\u1E78\\u016A\\u1E7A\\u016C\\u00DC\\u01DB\\u01D7\\u01D5\\u01D9\\u1EE6\\u016E\\u0170\\u01D3\\u0214\\u0216\\u01AF\\u1EEA\\u1EE8\\u1EEE\\u1EEC\\u1EF0\\u1EE4\\u1E72\\u0172\\u1E76\\u1E74\\u0244])/g\n}, {\n  base: 'V',\n  letters: /(&#86;|&#9419;|&#65334;|&#7804;|&#7806;|&#434;|&#42846;|&#581;|[\\u0056\\u24CB\\uFF36\\u1E7C\\u1E7E\\u01B2\\uA75E\\u0245])/g\n}, {\n  base: 'VY',\n  letters: /(&#42848;|[\\uA760])/g\n}, {\n  base: 'W',\n  letters: /(&#87;|&#9420;|&#65335;|&#7808;|&#7810;|&#372;|&#7814;|&#7812;|&#7816;|&#11378;|[\\u0057\\u24CC\\uFF37\\u1E80\\u1E82\\u0174\\u1E86\\u1E84\\u1E88\\u2C72])/g\n}, {\n  base: 'X',\n  letters: /(&#88;|&#9421;|&#65336;|&#7818;|&#7820;|[\\u0058\\u24CD\\uFF38\\u1E8A\\u1E8C])/g\n}, {\n  base: 'Y',\n  letters: /(&#89;|&#9422;|&#65337;|&#7922;|&#221;|&#374;|&#7928;|&#562;|&#7822;|&#376;|&#7926;|&#7924;|&#435;|&#590;|&#7934;|[\\u0059\\u24CE\\uFF39\\u1EF2\\u00DD\\u0176\\u1EF8\\u0232\\u1E8E\\u0178\\u1EF6\\u1EF4\\u01B3\\u024E\\u1EFE])/g\n}, {\n  base: 'Z',\n  letters: /(&#90;|&#9423;|&#65338;|&#377;|&#7824;|&#379;|&#381;|&#7826;|&#7828;|&#437;|&#548;|&#11391;|&#11371;|&#42850;|[\\u005A\\u24CF\\uFF3A\\u0179\\u1E90\\u017B\\u017D\\u1E92\\u1E94\\u01B5\\u0224\\u2C7F\\u2C6B\\uA762])/g\n}, {\n  base: 'a',\n  letters: /(&#97;|&#9424;|&#65345;|&#7834;|&#224;|&#225;|&#226;|&#7847;|&#7845;|&#7851;|&#7849;|&#227;|&#257;|&#259;|&#7857;|&#7855;|&#7861;|&#7859;|&#551;|&#481;|&#228;|&#479;|&#7843;|&#229;|&#507;|&#462;|&#513;|&#515;|&#7841;|&#7853;|&#7863;|&#7681;|&#261;|&#11365;|&#592;|[\\u0061\\u24D0\\uFF41\\u1E9A\\u00E0\\u00E1\\u00E2\\u1EA7\\u1EA5\\u1EAB\\u1EA9\\u00E3\\u0101\\u0103\\u1EB1\\u1EAF\\u1EB5\\u1EB3\\u0227\\u01E1\\u00E4\\u01DF\\u1EA3\\u00E5\\u01FB\\u01CE\\u0201\\u0203\\u1EA1\\u1EAD\\u1EB7\\u1E01\\u0105\\u2C65\\u0250])/g\n}, {\n  base: 'aa',\n  letters: /(&#42803;|[\\uA733])/g\n}, {\n  base: 'ae',\n  letters: /(&#230;|&#509;|&#483;|[\\u00E6\\u01FD\\u01E3])/g\n}, {\n  base: 'ao',\n  letters: /(&#42805;|[\\uA735])/g\n}, {\n  base: 'au',\n  letters: /(&#42807;|[\\uA737])/g\n}, {\n  base: 'av',\n  letters: /(&#42809;|&#42811;|[\\uA739\\uA73B])/g\n}, {\n  base: 'ay',\n  letters: /(&#42813;|[\\uA73D])/g\n}, {\n  base: 'b',\n  letters: /(&#98;|&#9425;|&#65346;|&#7683;|&#7685;|&#7687;|&#384;|&#387;|&#595;|[\\u0062\\u24D1\\uFF42\\u1E03\\u1E05\\u1E07\\u0180\\u0183\\u0253])/g\n}, {\n  base: 'c',\n  letters: /(&#99;|&#9426;|&#65347;|&#263;|&#265;|&#267;|&#269;|&#231;|&#7689;|&#392;|&#572;|&#42815;|&#8580;|[\\u0063\\u24D2\\uFF43\\u0107\\u0109\\u010B\\u010D\\u00E7\\u1E09\\u0188\\u023C\\uA73F\\u2184])/g\n}, {\n  base: 'd',\n  letters: /(&#100;|&#9427;|&#65348;|&#7691;|&#271;|&#7693;|&#7697;|&#7699;|&#7695;|&#273;|&#396;|&#598;|&#599;|&#42874;|&#240;|[\\u0064\\u24D3\\uFF44\\u1E0B\\u010F\\u1E0D\\u1E11\\u1E13\\u1E0F\\u0111\\u018C\\u0256\\u0257\\uA77A\\u00F0])/g\n}, {\n  base: 'dz',\n  letters: /(&#499;|&#454;|[\\u01F3\\u01C6])/g\n}, {\n  base: 'e',\n  letters: /(&#101;|&#9428;|&#65349;|&#232;|&#233;|&#234;|&#7873;|&#7871;|&#7877;|&#7875;|&#7869;|&#275;|&#7701;|&#7703;|&#277;|&#279;|&#235;|&#7867;|&#283;|&#517;|&#519;|&#7865;|&#7879;|&#553;|&#7709;|&#281;|&#7705;|&#7707;|&#583;|&#603;|&#477;|[\\u0065\\u24D4\\uFF45\\u00E8\\u00E9\\u00EA\\u1EC1\\u1EBF\\u1EC5\\u1EC3\\u1EBD\\u0113\\u1E15\\u1E17\\u0115\\u0117\\u00EB\\u1EBB\\u011B\\u0205\\u0207\\u1EB9\\u1EC7\\u0229\\u1E1D\\u0119\\u1E19\\u1E1B\\u0247\\u025B\\u01DD])/g\n}, {\n  base: 'f',\n  letters: /(&#102;|&#9429;|&#65350;|&#7711;|&#402;|&#42876;|[\\u0066\\u24D5\\uFF46\\u1E1F\\u0192\\uA77C])/g\n}, {\n  base: 'g',\n  letters: /(&#103;|&#9430;|&#65351;|&#501;|&#285;|&#7713;|&#287;|&#289;|&#487;|&#291;|&#485;|&#608;|&#42913;|&#7545;|&#42879;|[\\u0067\\u24D6\\uFF47\\u01F5\\u011D\\u1E21\\u011F\\u0121\\u01E7\\u0123\\u01E5\\u0260\\uA7A1\\u1D79\\uA77F])/g\n}, {\n  base: 'h',\n  letters: /(&#104;|&#9431;|&#65352;|&#293;|&#7715;|&#7719;|&#543;|&#7717;|&#7721;|&#7723;|&#7830;|&#295;|&#11368;|&#11382;|&#613;|[\\u0068\\u24D7\\uFF48\\u0125\\u1E23\\u1E27\\u021F\\u1E25\\u1E29\\u1E2B\\u1E96\\u0127\\u2C68\\u2C76\\u0265])/g\n}, {\n  base: 'hv',\n  letters: /(&#405;|[\\u0195])/g\n}, {\n  base: 'i',\n  letters: /(&#105;|&#9432;|&#65353;|&#236;|&#237;|&#238;|&#297;|&#299;|&#301;|&#239;|&#7727;|&#7881;|&#464;|&#521;|&#523;|&#7883;|&#303;|&#7725;|&#616;|&#305;|[\\u0069\\u24D8\\uFF49\\u00EC\\u00ED\\u00EE\\u0129\\u012B\\u012D\\u00EF\\u1E2F\\u1EC9\\u01D0\\u0209\\u020B\\u1ECB\\u012F\\u1E2D\\u0268\\u0131])/g\n}, {\n  base: 'ij',\n  letters: /(&#307;|[\\u0133])/g\n}, {\n  base: 'j',\n  letters: /(&#106;|&#9433;|&#65354;|&#309;|&#496;|&#585;|[\\u006A\\u24D9\\uFF4A\\u0135\\u01F0\\u0249])/g\n}, {\n  base: 'k',\n  letters: /(&#107;|&#9434;|&#65355;|&#7729;|&#489;|&#7731;|&#311;|&#7733;|&#409;|&#11370;|&#42817;|&#42819;|&#42821;|&#42915;|[\\u006B\\u24DA\\uFF4B\\u1E31\\u01E9\\u1E33\\u0137\\u1E35\\u0199\\u2C6A\\uA741\\uA743\\uA745\\uA7A3])/g\n}, {\n  base: 'l',\n  letters: /(&#108;|&#9435;|&#65356;|&#320;|&#314;|&#318;|&#7735;|&#7737;|&#316;|&#7741;|&#7739;|&#322;|&#410;|&#619;|&#11361;|&#42825;|&#42881;|&#42823;|[\\u006C\\u24DB\\uFF4C\\u0140\\u013A\\u013E\\u1E37\\u1E39\\u013C\\u1E3D\\u1E3B\\u0142\\u019A\\u026B\\u2C61\\uA749\\uA781\\uA747])/g\n}, {\n  base: 'lj',\n  letters: /(&#457;|[\\u01C9])/g\n}, {\n  base: 'm',\n  letters: /(&#109;|&#9436;|&#65357;|&#7743;|&#7745;|&#7747;|&#625;|&#623;|[\\u006D\\u24DC\\uFF4D\\u1E3F\\u1E41\\u1E43\\u0271\\u026F])/g\n}, {\n  base: 'n',\n  letters: /(&#110;|&#9437;|&#65358;|&#505;|&#324;|&#241;|&#7749;|&#328;|&#7751;|&#326;|&#7755;|&#7753;|&#414;|&#626;|&#329;|&#42897;|&#42917;|&#331;|[\\u006E\\u24DD\\uFF4E\\u01F9\\u0144\\u00F1\\u1E45\\u0148\\u1E47\\u0146\\u1E4B\\u1E49\\u019E\\u0272\\u0149\\uA791\\uA7A5\\u014B])/g\n}, {\n  base: 'nj',\n  letters: /(&#460;|[\\u01CC])/g\n}, {\n  base: 'o',\n  letters: /(&#111;|&#9438;|&#65359;|&#242;|&#243;|&#244;|&#7891;|&#7889;|&#7895;|&#7893;|&#245;|&#7757;|&#557;|&#7759;|&#333;|&#7761;|&#7763;|&#335;|&#559;|&#561;|&#246;|&#555;|&#7887;|&#337;|&#466;|&#525;|&#527;|&#417;|&#7901;|&#7899;|&#7905;|&#7903;|&#7907;|&#7885;|&#7897;|&#491;|&#493;|&#248;|&#511;|&#596;|&#42827;|&#42829;|&#629;|[\\u006F\\u24DE\\uFF4F\\u00F2\\u00F3\\u00F4\\u1ED3\\u1ED1\\u1ED7\\u1ED5\\u00F5\\u1E4D\\u022D\\u1E4F\\u014D\\u1E51\\u1E53\\u014F\\u022F\\u0231\\u00F6\\u022B\\u1ECF\\u0151\\u01D2\\u020D\\u020F\\u01A1\\u1EDD\\u1EDB\\u1EE1\\u1EDF\\u1EE3\\u1ECD\\u1ED9\\u01EB\\u01ED\\u00F8\\u01FF\\u0254\\uA74B\\uA74D\\u0275])/g\n}, {\n  base: 'oe',\n  letters: /(&#339;|[\\u0153])/g\n}, {\n  base: 'oi',\n  letters: /(&#419;|[\\u01A3])/g\n}, {\n  base: 'ou',\n  letters: /(&#547;|[\\u0223])/g\n}, {\n  base: 'oo',\n  letters: /(&#42831;|[\\uA74F])/g\n}, {\n  base: 'p',\n  letters: /(&#112;|&#9439;|&#65360;|&#7765;|&#7767;|&#421;|&#7549;|&#42833;|&#42835;|&#42837;|[\\u0070\\u24DF\\uFF50\\u1E55\\u1E57\\u01A5\\u1D7D\\uA751\\uA753\\uA755])/g\n}, {\n  base: 'q',\n  letters: /(&#113;|&#9440;|&#65361;|&#587;|&#42839;|&#42841;|[\\u0071\\u24E0\\uFF51\\u024B\\uA757\\uA759])/g\n}, {\n  base: 'r',\n  letters: /(&#114;|&#9441;|&#65362;|&#341;|&#7769;|&#345;|&#529;|&#531;|&#7771;|&#7773;|&#343;|&#7775;|&#589;|&#637;|&#42843;|&#42919;|&#42883;|[\\u0072\\u24E1\\uFF52\\u0155\\u1E59\\u0159\\u0211\\u0213\\u1E5B\\u1E5D\\u0157\\u1E5F\\u024D\\u027D\\uA75B\\uA7A7\\uA783])/g\n}, {\n  base: 's',\n  letters: /(&#115;|&#9442;|&#65363;|&#347;|&#7781;|&#349;|&#7777;|&#353;|&#7783;|&#7779;|&#7785;|&#537;|&#351;|&#575;|&#42921;|&#42885;|&#7835;|&#383;|[\\u0073\\u24E2\\uFF53\\u015B\\u1E65\\u015D\\u1E61\\u0161\\u1E67\\u1E63\\u1E69\\u0219\\u015F\\u023F\\uA7A9\\uA785\\u1E9B\\u017F])/g\n}, {\n  base: 'ss',\n  letters: /(&#223;|[\\u00DF])/g\n}, {\n  base: 't',\n  letters: /(&#116;|&#9443;|&#65364;|&#7787;|&#7831;|&#357;|&#7789;|&#539;|&#355;|&#7793;|&#7791;|&#359;|&#429;|&#648;|&#11366;|&#42887;|[\\u0074\\u24E3\\uFF54\\u1E6B\\u1E97\\u0165\\u1E6D\\u021B\\u0163\\u1E71\\u1E6F\\u0167\\u01AD\\u0288\\u2C66\\uA787])/g\n}, {\n  base: 'th',\n  letters: /(&#254;|[\\u00FE])/g\n}, {\n  base: 'tz',\n  letters: /(&#42793;|[\\uA729])/g\n}, {\n  base: 'u',\n  letters: /(&#117;|&#9444;|&#65365;|&#249;|&#250;|&#251;|&#361;|&#7801;|&#363;|&#7803;|&#365;|&#252;|&#476;|&#472;|&#470;|&#474;|&#7911;|&#367;|&#369;|&#468;|&#533;|&#535;|&#432;|&#7915;|&#7913;|&#7919;|&#7917;|&#7921;|&#7909;|&#7795;|&#371;|&#7799;|&#7797;|&#649;|[\\u0075\\u24E4\\uFF55\\u00F9\\u00FA\\u00FB\\u0169\\u1E79\\u016B\\u1E7B\\u016D\\u00FC\\u01DC\\u01D8\\u01D6\\u01DA\\u1EE7\\u016F\\u0171\\u01D4\\u0215\\u0217\\u01B0\\u1EEB\\u1EE9\\u1EEF\\u1EED\\u1EF1\\u1EE5\\u1E73\\u0173\\u1E77\\u1E75\\u0289])/g\n}, {\n  base: 'v',\n  letters: /(&#118;|&#9445;|&#65366;|&#7805;|&#7807;|&#651;|&#42847;|&#652;|[\\u0076\\u24E5\\uFF56\\u1E7D\\u1E7F\\u028B\\uA75F\\u028C])/g\n}, {\n  base: 'vy',\n  letters: /(&#42849;|[\\uA761])/g\n}, {\n  base: 'w',\n  letters: /(&#119;|&#9446;|&#65367;|&#7809;|&#7811;|&#373;|&#7815;|&#7813;|&#7832;|&#7817;|&#11379;|[\\u0077\\u24E6\\uFF57\\u1E81\\u1E83\\u0175\\u1E87\\u1E85\\u1E98\\u1E89\\u2C73])/g\n}, {\n  base: 'x',\n  letters: /(&#120;|&#9447;|&#65368;|&#7819;|&#7821;|[\\u0078\\u24E7\\uFF58\\u1E8B\\u1E8D])/g\n}, {\n  base: 'y',\n  letters: /(&#121;|&#9448;|&#65369;|&#7923;|&#253;|&#375;|&#7929;|&#563;|&#7823;|&#255;|&#7927;|&#7833;|&#7925;|&#436;|&#591;|&#7935;|[\\u0079\\u24E8\\uFF59\\u1EF3\\u00FD\\u0177\\u1EF9\\u0233\\u1E8F\\u00FF\\u1EF7\\u1E99\\u1EF5\\u01B4\\u024F\\u1EFF])/g\n}, {\n  base: 'z',\n  letters: /(&#122;|&#9449;|&#65370;|&#378;|&#7825;|&#380;|&#382;|&#7827;|&#7829;|&#438;|&#549;|&#576;|&#11372;|&#42851;|[\\u007A\\u24E9\\uFF5A\\u017A\\u1E91\\u017C\\u017E\\u1E93\\u1E95\\u01B6\\u0225\\u0240\\u2C6C\\uA763])/g\n}];\n\nvar removeAccents = function removeAccents(str) {\n  var formattedStr = str;\n  lettersDiacritics.forEach(function (letterDiacritics) {\n    formattedStr = formattedStr.replace(letterDiacritics.letters, letterDiacritics.base);\n  });\n  return formattedStr;\n};\n\nvar normalizeString = function normalizeString(str) {\n  return removeAccents(str).toLowerCase();\n};\n\nvar getSubstringIndex = function getSubstringIndex(str, substr, ignoreAccents) {\n  if (!ignoreAccents) {\n    return str.toLowerCase().indexOf(substr.toLowerCase());\n  }\n\n  return normalizeString(str).indexOf(normalizeString(substr));\n};\n\nvar isIE = function isIE() {\n  return !!document.documentMode;\n};\n\nvar isNumber = function isNumber(val) {\n  return typeof val === 'number';\n};\n\nvar keys = function keys(obj) {\n  return obj === Object(obj) ? Object.keys(obj) : [];\n};\n\nvar omit = function omit(obj) {\n  var _ref;\n\n  for (var _len = arguments.length, rest = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    rest[_key - 1] = arguments[_key];\n  }\n\n  var keys = (_ref = []).concat.apply(_ref, rest);\n\n  return Object.keys(obj).reduce(function (acc, k) {\n    if (obj.hasOwnProperty(k) && !keys.includes(k) && obj[k] !== undefined) {\n      acc[k] = obj[k];\n    }\n\n    return acc;\n  }, {});\n};\n\nvar _excluded = [\"style\", \"className\", \"classNames\"];\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction createDefaultStyle(defaultStyle, getModifiers) {\n  var enhance = function enhance(ComponentToWrap) {\n    var DefaultStyleEnhancer = function DefaultStyleEnhancer(_ref) {\n      var style = _ref.style,\n          className = _ref.className,\n          classNames = _ref.classNames,\n          rest = _objectWithoutProperties(_ref, _excluded);\n\n      var modifiers = getModifiers ? getModifiers(rest) : undefined;\n      var styles = useStyles(defaultStyle, {\n        style: style,\n        className: className,\n        classNames: classNames\n      }, modifiers);\n      return /*#__PURE__*/React.createElement(ComponentToWrap, _extends({}, rest, {\n        style: styles\n      }));\n    };\n\n    var displayName = ComponentToWrap.displayName || ComponentToWrap.name || 'Component';\n    DefaultStyleEnhancer.displayName = \"defaultStyle(\".concat(displayName, \")\"); // return DefaultStyleEnhancer\n\n    return /*#__PURE__*/React.forwardRef(function (props, ref) {\n      return DefaultStyleEnhancer(_objectSpread(_objectSpread({}, props), {}, {\n        ref: ref\n      }));\n    });\n  };\n\n  return enhance;\n}\n\nvar _generateComponentKey = function _generateComponentKey(usedKeys, id) {\n  if (!usedKeys.hasOwnProperty(id)) {\n    usedKeys[id] = 0;\n  } else {\n    usedKeys[id]++;\n  }\n\n  return id + '_' + usedKeys[id];\n};\n\nfunction Highlighter(_ref) {\n  var selectionStart = _ref.selectionStart,\n      selectionEnd = _ref.selectionEnd,\n      _ref$value = _ref.value,\n      value = _ref$value === void 0 ? '' : _ref$value,\n      onCaretPositionChange = _ref.onCaretPositionChange,\n      containerRef = _ref.containerRef,\n      children = _ref.children,\n      singleLine = _ref.singleLine,\n      style = _ref.style;\n\n  var _useState = useState({\n    left: undefined,\n    top: undefined\n  }),\n      _useState2 = _slicedToArray(_useState, 2),\n      position = _useState2[0],\n      setPosition = _useState2[1];\n\n  var _useState3 = useState(),\n      _useState4 = _slicedToArray(_useState3, 2),\n      caretElement = _useState4[0],\n      setCaretElement = _useState4[1];\n\n  useEffect(function () {\n    notifyCaretPosition();\n  });\n\n  var notifyCaretPosition = function notifyCaretPosition() {\n    if (!caretElement) {\n      return;\n    }\n\n    var offsetLeft = caretElement.offsetLeft,\n        offsetTop = caretElement.offsetTop;\n\n    if (position.left === offsetLeft && position.top === offsetTop) {\n      return;\n    }\n\n    var newPosition = {\n      left: offsetLeft,\n      top: offsetTop\n    };\n    setPosition(newPosition);\n    onCaretPositionChange(newPosition);\n  };\n\n  var config = readConfigFromChildren(children);\n  var caretPositionInMarkup;\n\n  if (selectionEnd === selectionStart) {\n    caretPositionInMarkup = mapPlainTextIndex(value, config, selectionStart, 'START');\n  }\n\n  var resultComponents = [];\n  var componentKeys = {};\n  var components = resultComponents;\n  var substringComponentKey = 0;\n\n  var textIteratee = function textIteratee(substr, index, indexInPlainText) {\n    // check whether the caret element has to be inserted inside the current plain substring\n    if (isNumber(caretPositionInMarkup) && caretPositionInMarkup >= index && caretPositionInMarkup <= index + substr.length) {\n      // if yes, split substr at the caret position and insert the caret component\n      var splitIndex = caretPositionInMarkup - index;\n      components.push(renderSubstring(substr.substring(0, splitIndex), substringComponentKey)); // add all following substrings and mention components as children of the caret component\n\n      components = [renderSubstring(substr.substring(splitIndex), substringComponentKey)];\n    } else {\n      components.push(renderSubstring(substr, substringComponentKey));\n    }\n\n    substringComponentKey++;\n  };\n\n  var mentionIteratee = function mentionIteratee(markup, index, indexInPlainText, id, display, mentionChildIndex, lastMentionEndIndex) {\n    var key = _generateComponentKey(componentKeys, id);\n\n    components.push(getMentionComponentForMatch(id, display, mentionChildIndex, key));\n  };\n\n  var renderSubstring = function renderSubstring(string, key) {\n    // set substring span to hidden, so that Emojis are not shown double in Mobile Safari\n    return /*#__PURE__*/React.createElement(\"span\", _extends({}, style('substring'), {\n      key: key\n    }), string);\n  };\n\n  var getMentionComponentForMatch = function getMentionComponentForMatch(id, display, mentionChildIndex, key) {\n    var props = {\n      id: id,\n      display: display,\n      key: key\n    };\n    var child = Children.toArray(children)[mentionChildIndex];\n    return /*#__PURE__*/React.cloneElement(child, props);\n  };\n\n  var renderHighlighterCaret = function renderHighlighterCaret(children) {\n    return /*#__PURE__*/React.createElement(\"span\", _extends({}, style('caret'), {\n      ref: setCaretElement,\n      key: \"caret\"\n    }), children);\n  };\n\n  iterateMentionsMarkup(value, config, mentionIteratee, textIteratee); // append a span containing a space, to ensure the last text line has the correct height\n\n  components.push(' ');\n\n  if (components !== resultComponents) {\n    // if a caret component is to be rendered, add all components that followed as its children\n    resultComponents.push(renderHighlighterCaret(components));\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, style, {\n    ref: containerRef\n  }), resultComponents);\n}\n\nHighlighter.propTypes = {\n  selectionStart: PropTypes.number,\n  selectionEnd: PropTypes.number,\n  value: PropTypes.string.isRequired,\n  onCaretPositionChange: PropTypes.func.isRequired,\n  containerRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: typeof Element === 'undefined' ? PropTypes.any : PropTypes.instanceOf(Element)\n  })]),\n  children: PropTypes.oneOfType([PropTypes.element, PropTypes.arrayOf(PropTypes.element)]).isRequired\n};\nvar styled = createDefaultStyle({\n  position: 'relative',\n  boxSizing: 'border-box',\n  width: '100%',\n  color: 'transparent',\n  overflow: 'hidden',\n  whiteSpace: 'pre-wrap',\n  wordWrap: 'break-word',\n  border: '1px solid transparent',\n  textAlign: 'start',\n  '&singleLine': {\n    whiteSpace: 'pre',\n    wordWrap: null\n  },\n  substring: {\n    visibility: 'hidden'\n  }\n}, function (props) {\n  return {\n    '&singleLine': props.singleLine\n  };\n});\nvar Highlighter$1 = styled(Highlighter);\n\nfunction Suggestion(_ref) {\n  var id = _ref.id,\n      focused = _ref.focused,\n      ignoreAccents = _ref.ignoreAccents,\n      index = _ref.index,\n      onClick = _ref.onClick,\n      onMouseEnter = _ref.onMouseEnter,\n      query = _ref.query,\n      renderSuggestion = _ref.renderSuggestion,\n      suggestion = _ref.suggestion,\n      style = _ref.style,\n      className = _ref.className,\n      classNames = _ref.classNames;\n  var rest = {\n    onClick: onClick,\n    onMouseEnter: onMouseEnter\n  };\n\n  var renderContent = function renderContent() {\n    var display = getDisplay();\n    var highlightedDisplay = renderHighlightedDisplay(display);\n\n    if (renderSuggestion) {\n      return renderSuggestion(suggestion, query, highlightedDisplay, index, focused);\n    }\n\n    return highlightedDisplay;\n  };\n\n  var getDisplay = function getDisplay() {\n    if (typeof suggestion === 'string') {\n      return suggestion;\n    }\n\n    var id = suggestion.id,\n        display = suggestion.display;\n\n    if (id === undefined || !display) {\n      return id;\n    }\n\n    return display;\n  };\n\n  var renderHighlightedDisplay = function renderHighlightedDisplay(display) {\n    var i = getSubstringIndex(display, query, ignoreAccents);\n\n    if (i === -1) {\n      return /*#__PURE__*/React.createElement(\"span\", style('display'), display);\n    }\n\n    return /*#__PURE__*/React.createElement(\"span\", style('display'), display.substring(0, i), /*#__PURE__*/React.createElement(\"b\", style('highlight'), display.substring(i, i + query.length)), display.substring(i + query.length));\n  };\n\n  return /*#__PURE__*/React.createElement(\"li\", _extends({\n    id: id,\n    role: \"option\",\n    \"aria-selected\": focused\n  }, rest, style), renderContent());\n}\n\nSuggestion.propTypes = {\n  id: PropTypes.string.isRequired,\n  query: PropTypes.string.isRequired,\n  index: PropTypes.number.isRequired,\n  ignoreAccents: PropTypes.bool,\n  suggestion: PropTypes.oneOfType([PropTypes.string, PropTypes.shape({\n    id: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired,\n    display: PropTypes.string\n  })]).isRequired,\n  renderSuggestion: PropTypes.func,\n  focused: PropTypes.bool\n};\nvar styled$1 = createDefaultStyle({\n  cursor: 'pointer'\n}, function (props) {\n  return {\n    '&focused': props.focused\n  };\n});\nvar Suggestion$1 = styled$1(Suggestion);\n\nfunction LoadingIndicator(_ref) {\n  var style = _ref.style,\n      className = _ref.className,\n      classNames = _ref.classNames;\n  var styles = useStyles(defaultstyle, {\n    style: style,\n    className: className,\n    classNames: classNames\n  });\n  var spinnerStyles = styles('spinner');\n  return /*#__PURE__*/React.createElement(\"div\", styles, /*#__PURE__*/React.createElement(\"div\", spinnerStyles, /*#__PURE__*/React.createElement(\"div\", spinnerStyles(['element', 'element1'])), /*#__PURE__*/React.createElement(\"div\", spinnerStyles(['element', 'element2'])), /*#__PURE__*/React.createElement(\"div\", spinnerStyles(['element', 'element3'])), /*#__PURE__*/React.createElement(\"div\", spinnerStyles(['element', 'element4'])), /*#__PURE__*/React.createElement(\"div\", spinnerStyles(['element', 'element5']))));\n}\n\nvar defaultstyle = {};\n\nfunction SuggestionsOverlay(_ref) {\n  var id = _ref.id,\n      _ref$suggestions = _ref.suggestions,\n      suggestions = _ref$suggestions === void 0 ? {} : _ref$suggestions,\n      a11ySuggestionsListLabel = _ref.a11ySuggestionsListLabel,\n      focusIndex = _ref.focusIndex,\n      position = _ref.position,\n      left = _ref.left,\n      right = _ref.right,\n      top = _ref.top,\n      scrollFocusedIntoView = _ref.scrollFocusedIntoView,\n      isLoading = _ref.isLoading,\n      isOpened = _ref.isOpened,\n      _ref$onSelect = _ref.onSelect,\n      onSelect = _ref$onSelect === void 0 ? function () {\n    return null;\n  } : _ref$onSelect,\n      ignoreAccents = _ref.ignoreAccents,\n      containerRef = _ref.containerRef,\n      children = _ref.children,\n      style = _ref.style,\n      customSuggestionsContainer = _ref.customSuggestionsContainer,\n      onMouseDown = _ref.onMouseDown,\n      onMouseEnter = _ref.onMouseEnter;\n\n  var _useState = useState(undefined),\n      _useState2 = _slicedToArray(_useState, 2),\n      ulElement = _useState2[0],\n      setUlElement = _useState2[1];\n\n  useEffect(function () {\n    if (!ulElement || ulElement.offsetHeight >= ulElement.scrollHeight || !scrollFocusedIntoView) {\n      return;\n    }\n\n    var scrollTop = ulElement.scrollTop;\n\n    var _ulElement$children$f = ulElement.children[focusIndex].getBoundingClientRect(),\n        top = _ulElement$children$f.top,\n        bottom = _ulElement$children$f.bottom;\n\n    var _ulElement$getBoundin = ulElement.getBoundingClientRect(),\n        topContainer = _ulElement$getBoundin.top;\n\n    top = top - topContainer + scrollTop;\n    bottom = bottom - topContainer + scrollTop;\n\n    if (top < scrollTop) {\n      ulElement.scrollTop = top;\n    } else if (bottom > ulElement.offsetHeight) {\n      ulElement.scrollTop = bottom - ulElement.offsetHeight;\n    }\n  }, [focusIndex, scrollFocusedIntoView, ulElement]);\n\n  var renderSuggestions = function renderSuggestions() {\n    var suggestionsToRender = /*#__PURE__*/React.createElement(\"ul\", _extends({\n      ref: setUlElement,\n      id: id,\n      role: \"listbox\",\n      \"aria-label\": a11ySuggestionsListLabel\n    }, style('list')), Object.values(suggestions).reduce(function (accResults, _ref2) {\n      var results = _ref2.results,\n          queryInfo = _ref2.queryInfo;\n      return [].concat(_toConsumableArray(accResults), _toConsumableArray(results.map(function (result, index) {\n        return renderSuggestion(result, queryInfo, accResults.length + index);\n      })));\n    }, []));\n    if (customSuggestionsContainer) return customSuggestionsContainer(suggestionsToRender);\n    return suggestionsToRender;\n  };\n\n  var renderSuggestion = function renderSuggestion(result, queryInfo, index) {\n    var isFocused = index === focusIndex;\n    var childIndex = queryInfo.childIndex,\n        query = queryInfo.query;\n    var renderSuggestion = Children.toArray(children)[childIndex].props.renderSuggestion;\n    return /*#__PURE__*/React.createElement(Suggestion$1, {\n      style: style('item'),\n      key: \"\".concat(childIndex, \"-\").concat(getID(result)),\n      id: getSuggestionHtmlId(id, index),\n      query: query,\n      index: index,\n      ignoreAccents: ignoreAccents,\n      renderSuggestion: renderSuggestion,\n      suggestion: result,\n      focused: isFocused,\n      onClick: function onClick() {\n        return select(result, queryInfo);\n      },\n      onMouseEnter: function onMouseEnter() {\n        return handleMouseEnter(index);\n      }\n    });\n  };\n\n  var renderLoadingIndicator = function renderLoadingIndicator() {\n    if (!isLoading) {\n      return;\n    }\n\n    return /*#__PURE__*/React.createElement(LoadingIndicator, {\n      style: style('loadingIndicator')\n    });\n  };\n\n  var handleMouseEnter = function handleMouseEnter(index, ev) {\n    if (onMouseEnter) {\n      onMouseEnter(index);\n    }\n  };\n\n  var select = function select(suggestion, queryInfo) {\n    onSelect(suggestion, queryInfo);\n  };\n\n  var getID = function getID(suggestion) {\n    if (typeof suggestion === 'string') {\n      return suggestion;\n    }\n\n    return suggestion.id;\n  };\n\n  if (!isOpened) {\n    return null;\n  }\n\n  return /*#__PURE__*/React.createElement(\"div\", _extends({}, inline({\n    position: position || 'absolute',\n    left: left,\n    right: right,\n    top: top\n  }, style), {\n    onMouseDown: onMouseDown,\n    ref: containerRef\n  }), renderSuggestions(), renderLoadingIndicator());\n}\n\nSuggestionsOverlay.propTypes = {\n  id: PropTypes.string.isRequired,\n  suggestions: PropTypes.object.isRequired,\n  a11ySuggestionsListLabel: PropTypes.string,\n  focusIndex: PropTypes.number,\n  position: PropTypes.string,\n  left: PropTypes.number,\n  right: PropTypes.number,\n  top: PropTypes.number,\n  scrollFocusedIntoView: PropTypes.bool,\n  isLoading: PropTypes.bool,\n  isOpened: PropTypes.bool.isRequired,\n  onSelect: PropTypes.func,\n  ignoreAccents: PropTypes.bool,\n  customSuggestionsContainer: PropTypes.func,\n  containerRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: typeof Element === 'undefined' ? PropTypes.any : PropTypes.instanceOf(Element)\n  })])\n};\nvar styled$2 = createDefaultStyle({\n  zIndex: 1,\n  backgroundColor: 'white',\n  marginTop: 14,\n  minWidth: 100,\n  list: {\n    margin: 0,\n    padding: 0,\n    listStyleType: 'none'\n  }\n});\nvar SuggestionsOverlay$1 = styled$2(SuggestionsOverlay);\n\nfunction ownKeys$1(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread$1(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys$1(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys$1(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }\n\nfunction _createSuper(Derived) { var hasNativeReflectConstruct = _isNativeReflectConstruct(); return function _createSuperInternal() { var Super = _getPrototypeOf(Derived), result; if (hasNativeReflectConstruct) { var NewTarget = _getPrototypeOf(this).constructor; result = Reflect.construct(Super, arguments, NewTarget); } else { result = Super.apply(this, arguments); } return _possibleConstructorReturn(this, result); }; }\n\nfunction _isNativeReflectConstruct() { if (typeof Reflect === \"undefined\" || !Reflect.construct) return false; if (Reflect.construct.sham) return false; if (typeof Proxy === \"function\") return true; try { Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); return true; } catch (e) { return false; } }\nvar makeTriggerRegex = function makeTriggerRegex(trigger) {\n  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n\n  if (trigger instanceof RegExp) {\n    return trigger;\n  } else {\n    var allowSpaceInQuery = options.allowSpaceInQuery;\n    var escapedTriggerChar = escapeRegex(trigger); // first capture group is the part to be replaced on completion\n    // second capture group is for extracting the search query\n\n    return new RegExp(\"(?:^|\\\\s)(\".concat(escapedTriggerChar, \"([^\").concat(allowSpaceInQuery ? '' : '\\\\s').concat(escapedTriggerChar, \"]*))$\"));\n  }\n};\n\nvar getDataProvider = function getDataProvider(data, ignoreAccents) {\n  if (data instanceof Array) {\n    // if data is an array, create a function to query that\n    return function (query, callback) {\n      var results = [];\n\n      for (var i = 0, l = data.length; i < l; ++i) {\n        var display = data[i].display || data[i].id;\n\n        if (getSubstringIndex(display, query, ignoreAccents) >= 0) {\n          results.push(data[i]);\n        }\n      }\n\n      return results;\n    };\n  } else {\n    // expect data to be a query function\n    return data;\n  }\n};\n\nvar KEY = {\n  TAB: 9,\n  RETURN: 13,\n  ESC: 27,\n  UP: 38,\n  DOWN: 40\n};\nvar isComposing = false;\nvar propTypes = {\n  /**\n   * If set to `true` a regular text input element will be rendered\n   * instead of a textarea\n   */\n  singleLine: PropTypes.bool,\n  allowSpaceInQuery: PropTypes.bool,\n  allowSuggestionsAboveCursor: PropTypes.bool,\n  forceSuggestionsAboveCursor: PropTypes.bool,\n  ignoreAccents: PropTypes.bool,\n  a11ySuggestionsListLabel: PropTypes.string,\n  value: PropTypes.string,\n  onKeyDown: PropTypes.func,\n  customSuggestionsContainer: PropTypes.func,\n  onSelect: PropTypes.func,\n  onBlur: PropTypes.func,\n  onChange: PropTypes.func,\n  suggestionsPortalHost: typeof Element === 'undefined' ? PropTypes.any : PropTypes.PropTypes.instanceOf(Element),\n  inputRef: PropTypes.oneOfType([PropTypes.func, PropTypes.shape({\n    current: typeof Element === 'undefined' ? PropTypes.any : PropTypes.instanceOf(Element)\n  })]),\n  children: PropTypes.oneOfType([PropTypes.element, PropTypes.arrayOf(PropTypes.element)]).isRequired\n};\n\nvar MentionsInput = /*#__PURE__*/function (_React$Component) {\n  _inherits(MentionsInput, _React$Component);\n\n  var _super = _createSuper(MentionsInput);\n\n  function MentionsInput(_props) {\n    var _this;\n\n    _classCallCheck(this, MentionsInput);\n\n    _this = _super.call(this, _props);\n\n    _defineProperty(_assertThisInitialized(_this), \"setContainerElement\", function (el) {\n      _this.containerElement = el;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"getInputProps\", function () {\n      var _this$props = _this.props,\n          readOnly = _this$props.readOnly,\n          disabled = _this$props.disabled,\n          style = _this$props.style; // pass all props that neither we, nor substyle, consume through to the input control\n\n      var props = omit(_this.props, ['style', 'classNames', 'className'], // substyle props\n      keys(propTypes));\n      return _objectSpread$1(_objectSpread$1(_objectSpread$1(_objectSpread$1({}, props), style('input')), {}, {\n        value: _this.getPlainText(),\n        onScroll: _this.updateHighlighterScroll\n      }, !readOnly && !disabled && {\n        onChange: _this.handleChange,\n        onSelect: _this.handleSelect,\n        onKeyDown: _this.handleKeyDown,\n        onBlur: _this.handleBlur,\n        onCompositionStart: _this.handleCompositionStart,\n        onCompositionEnd: _this.handleCompositionEnd\n      }), _this.isOpened() && {\n        role: 'combobox',\n        'aria-controls': _this.uuidSuggestionsOverlay,\n        'aria-expanded': true,\n        'aria-autocomplete': 'list',\n        'aria-haspopup': 'listbox',\n        'aria-activedescendant': getSuggestionHtmlId(_this.uuidSuggestionsOverlay, _this.state.focusIndex)\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"renderControl\", function () {\n      var _this$props2 = _this.props,\n          singleLine = _this$props2.singleLine,\n          style = _this$props2.style;\n\n      var inputProps = _this.getInputProps();\n\n      return /*#__PURE__*/React.createElement(\"div\", style('control'), _this.renderHighlighter(), singleLine ? _this.renderInput(inputProps) : _this.renderTextarea(inputProps));\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"renderInput\", function (props) {\n      return /*#__PURE__*/React.createElement(\"input\", _extends({\n        type: \"text\",\n        ref: _this.setInputRef\n      }, props));\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"renderTextarea\", function (props) {\n      return /*#__PURE__*/React.createElement(\"textarea\", _extends({\n        ref: _this.setInputRef\n      }, props));\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setInputRef\", function (el) {\n      _this.inputElement = el;\n      var inputRef = _this.props.inputRef;\n\n      if (typeof inputRef === 'function') {\n        inputRef(el);\n      } else if (inputRef) {\n        inputRef.current = el;\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setSuggestionsElement\", function (el) {\n      _this.suggestionsElement = el;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"renderSuggestionsOverlay\", function () {\n      if (!isNumber(_this.state.selectionStart)) {\n        // do not show suggestions when the input does not have the focus\n        return null;\n      }\n\n      var _this$state$suggestio = _this.state.suggestionsPosition,\n          position = _this$state$suggestio.position,\n          left = _this$state$suggestio.left,\n          top = _this$state$suggestio.top,\n          right = _this$state$suggestio.right;\n      var suggestionsNode = /*#__PURE__*/React.createElement(SuggestionsOverlay$1, {\n        id: _this.uuidSuggestionsOverlay,\n        style: _this.props.style('suggestions'),\n        position: position,\n        left: left,\n        top: top,\n        right: right,\n        focusIndex: _this.state.focusIndex,\n        scrollFocusedIntoView: _this.state.scrollFocusedIntoView,\n        containerRef: _this.setSuggestionsElement,\n        suggestions: _this.state.suggestions,\n        customSuggestionsContainer: _this.props.customSuggestionsContainer,\n        onSelect: _this.addMention,\n        onMouseDown: _this.handleSuggestionsMouseDown,\n        onMouseEnter: _this.handleSuggestionsMouseEnter,\n        isLoading: _this.isLoading(),\n        isOpened: _this.isOpened(),\n        ignoreAccents: _this.props.ignoreAccents,\n        a11ySuggestionsListLabel: _this.props.a11ySuggestionsListLabel\n      }, _this.props.children);\n\n      if (_this.props.suggestionsPortalHost) {\n        return /*#__PURE__*/ReactDOM.createPortal(suggestionsNode, _this.props.suggestionsPortalHost);\n      } else {\n        return suggestionsNode;\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"renderHighlighter\", function () {\n      var _this$state = _this.state,\n          selectionStart = _this$state.selectionStart,\n          selectionEnd = _this$state.selectionEnd;\n      var _this$props3 = _this.props,\n          singleLine = _this$props3.singleLine,\n          children = _this$props3.children,\n          value = _this$props3.value,\n          style = _this$props3.style;\n      return /*#__PURE__*/React.createElement(Highlighter$1, {\n        containerRef: _this.setHighlighterElement,\n        style: style('highlighter'),\n        value: value,\n        singleLine: singleLine,\n        selectionStart: selectionStart,\n        selectionEnd: selectionEnd,\n        onCaretPositionChange: _this.handleCaretPositionChange\n      }, children);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setHighlighterElement\", function (el) {\n      _this.highlighterElement = el;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleCaretPositionChange\", function (position) {\n      _this.setState({\n        caretPosition: position\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"getPlainText\", function () {\n      return getPlainText(_this.props.value || '', readConfigFromChildren(_this.props.children));\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"executeOnChange\", function (event) {\n      for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n        args[_key - 1] = arguments[_key];\n      }\n\n      if (_this.props.onChange) {\n        var _this$props4;\n\n        return (_this$props4 = _this.props).onChange.apply(_this$props4, [event].concat(args));\n      }\n\n      if (_this.props.valueLink) {\n        var _this$props$valueLink;\n\n        return (_this$props$valueLink = _this.props.valueLink).requestChange.apply(_this$props$valueLink, [event.target.value].concat(args));\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleChange\", function (ev) {\n      isComposing = false;\n\n      if (isIE()) {\n        // if we are inside iframe, we need to find activeElement within its contentDocument\n        var currentDocument = document.activeElement && document.activeElement.contentDocument || document;\n\n        if (currentDocument.activeElement !== ev.target) {\n          // fix an IE bug (blur from empty input element with placeholder attribute trigger \"input\" event)\n          return;\n        }\n      }\n\n      var value = _this.props.value || '';\n      var config = readConfigFromChildren(_this.props.children);\n      var newPlainTextValue = ev.target.value;\n      var selectionStartBefore = _this.state.selectionStart;\n\n      if (selectionStartBefore == null) {\n        selectionStartBefore = ev.target.selectionStart;\n      }\n\n      var selectionEndBefore = _this.state.selectionEnd;\n\n      if (selectionEndBefore == null) {\n        selectionEndBefore = ev.target.selectionEnd;\n      } // Derive the new value to set by applying the local change in the textarea's plain text\n\n\n      var newValue = applyChangeToValue(value, newPlainTextValue, {\n        selectionStartBefore: selectionStartBefore,\n        selectionEndBefore: selectionEndBefore,\n        selectionEndAfter: ev.target.selectionEnd\n      }, config); // In case a mention is deleted, also adjust the new plain text value\n\n      newPlainTextValue = getPlainText(newValue, config); // Save current selection after change to be able to restore caret position after rerendering\n\n      var selectionStart = ev.target.selectionStart;\n      var selectionEnd = ev.target.selectionEnd;\n      var setSelectionAfterMentionChange = false; // Adjust selection range in case a mention will be deleted by the characters outside of the\n      // selection range that are automatically deleted\n\n      var startOfMention = findStartOfMentionInPlainText(value, config, selectionStart);\n\n      if (startOfMention !== undefined && _this.state.selectionEnd > startOfMention) {\n        // only if a deletion has taken place\n        selectionStart = startOfMention + (ev.nativeEvent.data ? ev.nativeEvent.data.length : 0);\n        selectionEnd = selectionStart;\n        setSelectionAfterMentionChange = true;\n      }\n\n      _this.setState({\n        selectionStart: selectionStart,\n        selectionEnd: selectionEnd,\n        setSelectionAfterMentionChange: setSelectionAfterMentionChange\n      });\n\n      var mentions = getMentions(newValue, config);\n\n      if (ev.nativeEvent.isComposing && selectionStart === selectionEnd) {\n        _this.updateMentionsQueries(_this.inputElement.value, selectionStart);\n      } // Propagate change\n      // let handleChange = this.getOnChange(this.props) || emptyFunction;\n\n\n      var eventMock = {\n        target: {\n          value: newValue\n        }\n      }; // this.props.onChange.call(this, eventMock, newValue, newPlainTextValue, mentions);\n\n      _this.executeOnChange(eventMock, newValue, newPlainTextValue, mentions);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleSelect\", function (ev) {\n      // keep track of selection range / caret position\n      _this.setState({\n        selectionStart: ev.target.selectionStart,\n        selectionEnd: ev.target.selectionEnd\n      }); // do nothing while a IME composition session is active\n\n\n      if (isComposing) return; // refresh suggestions queries\n\n      var el = _this.inputElement;\n\n      if (ev.target.selectionStart === ev.target.selectionEnd) {\n        _this.updateMentionsQueries(el.value, ev.target.selectionStart);\n      } else {\n        _this.clearSuggestions();\n      } // sync highlighters scroll position\n\n\n      _this.updateHighlighterScroll();\n\n      _this.props.onSelect(ev);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleKeyDown\", function (ev) {\n      // do not intercept key events if the suggestions overlay is not shown\n      var suggestionsCount = countSuggestions(_this.state.suggestions);\n\n      if (suggestionsCount === 0 || !_this.suggestionsElement) {\n        _this.props.onKeyDown(ev);\n\n        return;\n      }\n\n      if (Object.values(KEY).indexOf(ev.keyCode) >= 0) {\n        ev.preventDefault();\n        ev.stopPropagation();\n      }\n\n      switch (ev.keyCode) {\n        case KEY.ESC:\n          {\n            _this.clearSuggestions();\n\n            return;\n          }\n\n        case KEY.DOWN:\n          {\n            _this.shiftFocus(+1);\n\n            return;\n          }\n\n        case KEY.UP:\n          {\n            _this.shiftFocus(-1);\n\n            return;\n          }\n\n        case KEY.RETURN:\n          {\n            _this.selectFocused();\n\n            return;\n          }\n\n        case KEY.TAB:\n          {\n            _this.selectFocused();\n\n            return;\n          }\n\n        default:\n          {\n            return;\n          }\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"shiftFocus\", function (delta) {\n      var suggestionsCount = countSuggestions(_this.state.suggestions);\n\n      _this.setState({\n        focusIndex: (suggestionsCount + _this.state.focusIndex + delta) % suggestionsCount,\n        scrollFocusedIntoView: true\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"selectFocused\", function () {\n      var _this$state2 = _this.state,\n          suggestions = _this$state2.suggestions,\n          focusIndex = _this$state2.focusIndex;\n      var _Object$values$reduce = Object.values(suggestions).reduce(function (acc, _ref) {\n        var results = _ref.results,\n            queryInfo = _ref.queryInfo;\n        return [].concat(_toConsumableArray(acc), _toConsumableArray(results.map(function (result) {\n          return {\n            result: result,\n            queryInfo: queryInfo\n          };\n        })));\n      }, [])[focusIndex],\n          result = _Object$values$reduce.result,\n          queryInfo = _Object$values$reduce.queryInfo;\n\n      _this.addMention(result, queryInfo);\n\n      _this.setState({\n        focusIndex: 0\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleBlur\", function (ev) {\n      var clickedSuggestion = _this._suggestionsMouseDown;\n      _this._suggestionsMouseDown = false; // only reset selection if the mousedown happened on an element\n      // other than the suggestions overlay\n\n      if (!clickedSuggestion) {\n        _this.setState({\n          selectionStart: null,\n          selectionEnd: null\n        });\n      }\n\n      window.setTimeout(function () {\n        _this.updateHighlighterScroll();\n      }, 1);\n\n      _this.props.onBlur(ev, clickedSuggestion);\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleSuggestionsMouseDown\", function (ev) {\n      _this._suggestionsMouseDown = true;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleSuggestionsMouseEnter\", function (focusIndex) {\n      _this.setState({\n        focusIndex: focusIndex,\n        scrollFocusedIntoView: false\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"updateSuggestionsPosition\", function () {\n      var caretPosition = _this.state.caretPosition;\n      var _this$props5 = _this.props,\n          suggestionsPortalHost = _this$props5.suggestionsPortalHost,\n          allowSuggestionsAboveCursor = _this$props5.allowSuggestionsAboveCursor,\n          forceSuggestionsAboveCursor = _this$props5.forceSuggestionsAboveCursor;\n\n      if (!caretPosition || !_this.suggestionsElement) {\n        return;\n      }\n\n      var suggestions = _this.suggestionsElement;\n      var highlighter = _this.highlighterElement; // first get viewport-relative position (highlighter is offsetParent of caret):\n\n      var caretOffsetParentRect = highlighter.getBoundingClientRect();\n      var caretHeight = getComputedStyleLengthProp(highlighter, 'font-size');\n      var viewportRelative = {\n        left: caretOffsetParentRect.left + caretPosition.left,\n        top: caretOffsetParentRect.top + caretPosition.top + caretHeight\n      };\n      var viewportHeight = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n\n      if (!suggestions) {\n        return;\n      }\n\n      var position = {}; // if suggestions menu is in a portal, update position to be releative to its portal node\n\n      if (suggestionsPortalHost) {\n        position.position = 'fixed';\n        var left = viewportRelative.left;\n        var top = viewportRelative.top; // absolute/fixed positioned elements are positioned according to their entire box including margins; so we remove margins here:\n\n        left -= getComputedStyleLengthProp(suggestions, 'margin-left');\n        top -= getComputedStyleLengthProp(suggestions, 'margin-top'); // take into account highlighter/textinput scrolling:\n\n        left -= highlighter.scrollLeft;\n        top -= highlighter.scrollTop; // guard for mentions suggestions list clipped by right edge of window\n\n        var viewportWidth = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n\n        if (left + suggestions.offsetWidth > viewportWidth) {\n          position.left = Math.max(0, viewportWidth - suggestions.offsetWidth);\n        } else {\n          position.left = left;\n        } // guard for mentions suggestions list clipped by bottom edge of window if allowSuggestionsAboveCursor set to true.\n        // Move the list up above the caret if it's getting cut off by the bottom of the window, provided that the list height\n        // is small enough to NOT cover up the caret\n\n\n        if (allowSuggestionsAboveCursor && top + suggestions.offsetHeight > viewportHeight && suggestions.offsetHeight < top - caretHeight || forceSuggestionsAboveCursor) {\n          position.top = Math.max(0, top - suggestions.offsetHeight - caretHeight);\n        } else {\n          position.top = top;\n        }\n      } else {\n        var _left = caretPosition.left - highlighter.scrollLeft;\n\n        var _top = caretPosition.top - highlighter.scrollTop; // guard for mentions suggestions list clipped by right edge of window\n\n\n        if (_left + suggestions.offsetWidth > _this.containerElement.offsetWidth) {\n          position.right = 0;\n        } else {\n          position.left = _left;\n        } // guard for mentions suggestions list clipped by bottom edge of window if allowSuggestionsAboveCursor set to true.\n        // move the list up above the caret if it's getting cut off by the bottom of the window, provided that the list height\n        // is small enough to NOT cover up the caret\n\n\n        if (allowSuggestionsAboveCursor && viewportRelative.top - highlighter.scrollTop + suggestions.offsetHeight > viewportHeight && suggestions.offsetHeight < caretOffsetParentRect.top - caretHeight - highlighter.scrollTop || forceSuggestionsAboveCursor) {\n          position.top = _top - suggestions.offsetHeight - caretHeight;\n        } else {\n          position.top = _top;\n        }\n      }\n\n      if (position.left === _this.state.suggestionsPosition.left && position.top === _this.state.suggestionsPosition.top && position.position === _this.state.suggestionsPosition.position) {\n        return;\n      }\n\n      _this.setState({\n        suggestionsPosition: position\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"updateHighlighterScroll\", function () {\n      var input = _this.inputElement;\n      var highlighter = _this.highlighterElement;\n\n      if (!input || !highlighter) {\n        // since the invocation of this function is deferred,\n        // the whole component may have been unmounted in the meanwhile\n        return;\n      }\n\n      highlighter.scrollLeft = input.scrollLeft;\n      highlighter.scrollTop = input.scrollTop;\n      highlighter.height = input.height;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleCompositionStart\", function () {\n      isComposing = true;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"handleCompositionEnd\", function () {\n      isComposing = false;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"setSelection\", function (selectionStart, selectionEnd) {\n      if (selectionStart === null || selectionEnd === null) return;\n      var el = _this.inputElement;\n\n      if (el.setSelectionRange) {\n        el.setSelectionRange(selectionStart, selectionEnd);\n      } else if (el.createTextRange) {\n        var range = el.createTextRange();\n        range.collapse(true);\n        range.moveEnd('character', selectionEnd);\n        range.moveStart('character', selectionStart);\n        range.select();\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"updateMentionsQueries\", function (plainTextValue, caretPosition) {\n      // Invalidate previous queries. Async results for previous queries will be neglected.\n      _this._queryId++;\n      _this.suggestions = {};\n\n      _this.setState({\n        suggestions: {}\n      });\n\n      var value = _this.props.value || '';\n      var children = _this.props.children;\n      var config = readConfigFromChildren(children);\n      var positionInValue = mapPlainTextIndex(value, config, caretPosition, 'NULL'); // If caret is inside of mention, do not query\n\n      if (positionInValue === null) {\n        return;\n      } // Extract substring in between the end of the previous mention and the caret\n\n\n      var substringStartIndex = getEndOfLastMention(value.substring(0, positionInValue), config);\n      var substring = plainTextValue.substring(substringStartIndex, caretPosition); // Check if suggestions have to be shown:\n      // Match the trigger patterns of all Mention children on the extracted substring\n\n      React.Children.forEach(children, function (child, childIndex) {\n        if (!child) {\n          return;\n        }\n\n        var regex = makeTriggerRegex(child.props.trigger, _this.props);\n        var match = substring.match(regex);\n\n        if (match) {\n          var querySequenceStart = substringStartIndex + substring.indexOf(match[1], match.index);\n\n          _this.queryData(match[2], childIndex, querySequenceStart, querySequenceStart + match[1].length, plainTextValue);\n        }\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"clearSuggestions\", function () {\n      // Invalidate previous queries. Async results for previous queries will be neglected.\n      _this._queryId++;\n      _this.suggestions = {};\n\n      _this.setState({\n        suggestions: {},\n        focusIndex: 0\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"queryData\", function (query, childIndex, querySequenceStart, querySequenceEnd, plainTextValue) {\n      var _this$props6 = _this.props,\n          children = _this$props6.children,\n          ignoreAccents = _this$props6.ignoreAccents;\n      var mentionChild = Children.toArray(children)[childIndex];\n      var provideData = getDataProvider(mentionChild.props.data, ignoreAccents);\n      var syncResult = provideData(query, _this.updateSuggestions.bind(null, _this._queryId, childIndex, query, querySequenceStart, querySequenceEnd, plainTextValue));\n\n      if (syncResult instanceof Array) {\n        _this.updateSuggestions(_this._queryId, childIndex, query, querySequenceStart, querySequenceEnd, plainTextValue, syncResult);\n      }\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"updateSuggestions\", function (queryId, childIndex, query, querySequenceStart, querySequenceEnd, plainTextValue, results) {\n      // neglect async results from previous queries\n      if (queryId !== _this._queryId) return; // save in property so that multiple sync state updates from different mentions sources\n      // won't overwrite each other\n\n      _this.suggestions = _objectSpread$1(_objectSpread$1({}, _this.suggestions), {}, _defineProperty({}, childIndex, {\n        queryInfo: {\n          childIndex: childIndex,\n          query: query,\n          querySequenceStart: querySequenceStart,\n          querySequenceEnd: querySequenceEnd,\n          plainTextValue: plainTextValue\n        },\n        results: results\n      }));\n      var focusIndex = _this.state.focusIndex;\n      var suggestionsCount = countSuggestions(_this.suggestions);\n\n      _this.setState({\n        suggestions: _this.suggestions,\n        focusIndex: focusIndex >= suggestionsCount ? Math.max(suggestionsCount - 1, 0) : focusIndex\n      });\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"addMention\", function (_ref2, _ref3) {\n      var id = _ref2.id,\n          display = _ref2.display;\n      var childIndex = _ref3.childIndex,\n          querySequenceStart = _ref3.querySequenceStart,\n          querySequenceEnd = _ref3.querySequenceEnd,\n          plainTextValue = _ref3.plainTextValue;\n      // Insert mention in the marked up value at the correct position\n      var value = _this.props.value || '';\n      var config = readConfigFromChildren(_this.props.children);\n      var mentionsChild = Children.toArray(_this.props.children)[childIndex];\n      var _mentionsChild$props = mentionsChild.props,\n          markup = _mentionsChild$props.markup,\n          displayTransform = _mentionsChild$props.displayTransform,\n          appendSpaceOnAdd = _mentionsChild$props.appendSpaceOnAdd,\n          onAdd = _mentionsChild$props.onAdd;\n      var start = mapPlainTextIndex(value, config, querySequenceStart, 'START');\n      var end = start + querySequenceEnd - querySequenceStart;\n      var insert = makeMentionsMarkup(markup, id, display);\n\n      if (appendSpaceOnAdd) {\n        insert += ' ';\n      }\n\n      var newValue = spliceString(value, start, end, insert); // Refocus input and set caret position to end of mention\n\n      _this.inputElement.focus();\n\n      var displayValue = displayTransform(id, display);\n\n      if (appendSpaceOnAdd) {\n        displayValue += ' ';\n      }\n\n      var newCaretPosition = querySequenceStart + displayValue.length;\n\n      _this.setState({\n        selectionStart: newCaretPosition,\n        selectionEnd: newCaretPosition,\n        setSelectionAfterMentionChange: true\n      }); // Propagate change\n\n\n      var eventMock = {\n        target: {\n          value: newValue\n        }\n      };\n      var mentions = getMentions(newValue, config);\n      var newPlainTextValue = spliceString(plainTextValue, querySequenceStart, querySequenceEnd, displayValue);\n\n      _this.executeOnChange(eventMock, newValue, newPlainTextValue, mentions);\n\n      if (onAdd) {\n        onAdd(id, display, start, end);\n      } // Make sure the suggestions overlay is closed\n\n\n      _this.clearSuggestions();\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"isLoading\", function () {\n      var isLoading = false;\n      React.Children.forEach(_this.props.children, function (child) {\n        isLoading = isLoading || child && child.props.isLoading;\n      });\n      return isLoading;\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"isOpened\", function () {\n      return isNumber(_this.state.selectionStart) && (countSuggestions(_this.state.suggestions) !== 0 || _this.isLoading());\n    });\n\n    _defineProperty(_assertThisInitialized(_this), \"_queryId\", 0);\n\n    _this.suggestions = {};\n    _this.uuidSuggestionsOverlay = Math.random().toString(16).substring(2);\n    _this.handleCopy = _this.handleCopy.bind(_assertThisInitialized(_this));\n    _this.handleCut = _this.handleCut.bind(_assertThisInitialized(_this));\n    _this.handlePaste = _this.handlePaste.bind(_assertThisInitialized(_this));\n    _this.state = {\n      focusIndex: 0,\n      selectionStart: null,\n      selectionEnd: null,\n      suggestions: {},\n      caretPosition: null,\n      suggestionsPosition: {},\n      setSelectionAfterHandlePaste: false\n    };\n    return _this;\n  }\n\n  _createClass(MentionsInput, [{\n    key: \"componentDidMount\",\n    value: function componentDidMount() {\n      document.addEventListener('copy', this.handleCopy);\n      document.addEventListener('cut', this.handleCut);\n      document.addEventListener('paste', this.handlePaste);\n      this.updateSuggestionsPosition();\n    }\n  }, {\n    key: \"componentDidUpdate\",\n    value: function componentDidUpdate(prevProps, prevState) {\n      // Update position of suggestions unless this componentDidUpdate was\n      // triggered by an update to suggestionsPosition.\n      if (prevState.suggestionsPosition === this.state.suggestionsPosition) {\n        this.updateSuggestionsPosition();\n      } // maintain selection in case a mention is added/removed causing\n      // the cursor to jump to the end\n\n\n      if (this.state.setSelectionAfterMentionChange) {\n        this.setState({\n          setSelectionAfterMentionChange: false\n        });\n        this.setSelection(this.state.selectionStart, this.state.selectionEnd);\n      }\n\n      if (this.state.setSelectionAfterHandlePaste) {\n        this.setState({\n          setSelectionAfterHandlePaste: false\n        });\n        this.setSelection(this.state.selectionStart, this.state.selectionEnd);\n      }\n    }\n  }, {\n    key: \"componentWillUnmount\",\n    value: function componentWillUnmount() {\n      document.removeEventListener('copy', this.handleCopy);\n      document.removeEventListener('cut', this.handleCut);\n      document.removeEventListener('paste', this.handlePaste);\n    }\n  }, {\n    key: \"render\",\n    value: function render() {\n      return /*#__PURE__*/React.createElement(\"div\", _extends({\n        ref: this.setContainerElement\n      }, this.props.style), this.renderControl(), this.renderSuggestionsOverlay());\n    }\n  }, {\n    key: \"handlePaste\",\n    value: function handlePaste(event) {\n      if (event.target !== this.inputElement) {\n        return;\n      }\n\n      if (!this.supportsClipboardActions(event)) {\n        return;\n      }\n\n      event.preventDefault();\n      var _this$state3 = this.state,\n          selectionStart = _this$state3.selectionStart,\n          selectionEnd = _this$state3.selectionEnd;\n      var _this$props7 = this.props,\n          value = _this$props7.value,\n          children = _this$props7.children;\n      var config = readConfigFromChildren(children);\n      var markupStartIndex = mapPlainTextIndex(value, config, selectionStart, 'START');\n      var markupEndIndex = mapPlainTextIndex(value, config, selectionEnd, 'END');\n      var pastedMentions = event.clipboardData.getData('text/react-mentions');\n      var pastedData = event.clipboardData.getData('text/plain');\n      var newValue = spliceString(value, markupStartIndex, markupEndIndex, pastedMentions || pastedData).replace(/\\r/g, '');\n      var newPlainTextValue = getPlainText(newValue, config);\n      var eventMock = {\n        target: _objectSpread$1(_objectSpread$1({}, event.target), {}, {\n          value: newValue\n        })\n      };\n      this.executeOnChange(eventMock, newValue, newPlainTextValue, getMentions(newValue, config)); // Move the cursor position to the end of the pasted data\n\n      var startOfMention = findStartOfMentionInPlainText(value, config, selectionStart);\n      var nextPos = (startOfMention || selectionStart) + getPlainText(pastedMentions || pastedData, config).length;\n      this.setState({\n        selectionStart: nextPos,\n        selectionEnd: nextPos,\n        setSelectionAfterHandlePaste: true\n      });\n    }\n  }, {\n    key: \"saveSelectionToClipboard\",\n    value: function saveSelectionToClipboard(event) {\n      // use the actual selectionStart & selectionEnd instead of the one stored\n      // in state to ensure copy & paste also works on disabled inputs & textareas\n      var selectionStart = this.inputElement.selectionStart;\n      var selectionEnd = this.inputElement.selectionEnd;\n      var _this$props8 = this.props,\n          children = _this$props8.children,\n          value = _this$props8.value;\n      var config = readConfigFromChildren(children);\n      var markupStartIndex = mapPlainTextIndex(value, config, selectionStart, 'START');\n      var markupEndIndex = mapPlainTextIndex(value, config, selectionEnd, 'END');\n      event.clipboardData.setData('text/plain', event.target.value.slice(selectionStart, selectionEnd));\n      event.clipboardData.setData('text/react-mentions', value.slice(markupStartIndex, markupEndIndex));\n    }\n  }, {\n    key: \"supportsClipboardActions\",\n    value: function supportsClipboardActions(event) {\n      return !!event.clipboardData;\n    }\n  }, {\n    key: \"handleCopy\",\n    value: function handleCopy(event) {\n      if (event.target !== this.inputElement) {\n        return;\n      }\n\n      if (!this.supportsClipboardActions(event)) {\n        return;\n      }\n\n      event.preventDefault();\n      this.saveSelectionToClipboard(event);\n    }\n  }, {\n    key: \"handleCut\",\n    value: function handleCut(event) {\n      if (event.target !== this.inputElement) {\n        return;\n      }\n\n      if (!this.supportsClipboardActions(event)) {\n        return;\n      }\n\n      event.preventDefault();\n      this.saveSelectionToClipboard(event);\n      var _this$state4 = this.state,\n          selectionStart = _this$state4.selectionStart,\n          selectionEnd = _this$state4.selectionEnd;\n      var _this$props9 = this.props,\n          children = _this$props9.children,\n          value = _this$props9.value;\n      var config = readConfigFromChildren(children);\n      var markupStartIndex = mapPlainTextIndex(value, config, selectionStart, 'START');\n      var markupEndIndex = mapPlainTextIndex(value, config, selectionEnd, 'END');\n      var newValue = [value.slice(0, markupStartIndex), value.slice(markupEndIndex)].join('');\n      var newPlainTextValue = getPlainText(newValue, config);\n      var eventMock = {\n        target: _objectSpread$1(_objectSpread$1({}, event.target), {}, {\n          value: newPlainTextValue\n        })\n      };\n      this.executeOnChange(eventMock, newValue, newPlainTextValue, getMentions(value, config));\n    } // Handle input element's change event\n\n  }]);\n\n  return MentionsInput;\n}(React.Component);\n/**\n * Returns the computed length property value for the provided element.\n * Note: According to spec and testing, can count on length values coming back in pixels. See https://developer.mozilla.org/en-US/docs/Web/CSS/used_value#Difference_from_computed_value\n */\n\n\n_defineProperty(MentionsInput, \"propTypes\", propTypes);\n\n_defineProperty(MentionsInput, \"defaultProps\", {\n  ignoreAccents: false,\n  singleLine: false,\n  allowSuggestionsAboveCursor: false,\n  onKeyDown: function onKeyDown() {\n    return null;\n  },\n  onSelect: function onSelect() {\n    return null;\n  },\n  onBlur: function onBlur() {\n    return null;\n  }\n});\n\nvar getComputedStyleLengthProp = function getComputedStyleLengthProp(forElement, propertyName) {\n  var length = parseFloat(window.getComputedStyle(forElement, null).getPropertyValue(propertyName));\n  return isFinite(length) ? length : 0;\n};\n\nvar isMobileSafari = typeof navigator !== 'undefined' && /iPhone|iPad|iPod/i.test(navigator.userAgent);\nvar styled$3 = createDefaultStyle({\n  position: 'relative',\n  overflowY: 'visible',\n  input: {\n    display: 'block',\n    width: '100%',\n    position: 'absolute',\n    margin: 0,\n    top: 0,\n    left: 0,\n    boxSizing: 'border-box',\n    backgroundColor: 'transparent',\n    fontFamily: 'inherit',\n    fontSize: 'inherit',\n    letterSpacing: 'inherit'\n  },\n  '&multiLine': {\n    input: _objectSpread$1({\n      height: '100%',\n      bottom: 0,\n      overflow: 'hidden',\n      resize: 'none'\n    }, isMobileSafari ? {\n      marginTop: 1,\n      marginLeft: -3\n    } : null)\n  }\n}, function (_ref4) {\n  var singleLine = _ref4.singleLine;\n  return {\n    '&singleLine': singleLine,\n    '&multiLine': !singleLine\n  };\n});\nvar MentionsInput$1 = styled$3(MentionsInput);\n\nvar defaultStyle = {\n  fontWeight: 'inherit'\n};\n\nvar Mention = function Mention(_ref) {\n  var display = _ref.display,\n      style = _ref.style,\n      className = _ref.className,\n      classNames = _ref.classNames;\n  var styles = useStyles(defaultStyle, {\n    style: style,\n    className: className,\n    classNames: classNames\n  });\n  return /*#__PURE__*/React.createElement(\"strong\", styles, display);\n};\n\nMention.propTypes = {\n  /**\n   * Called when a new mention is added in the input\n   *\n   * Example:\n   *\n   * ```js\n   * function(id, display) {\n   *   console.log(\"user \" + display + \" was mentioned!\");\n   * }\n   * ```\n   */\n  onAdd: PropTypes.func,\n  onRemove: PropTypes.func,\n  renderSuggestion: PropTypes.func,\n  trigger: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(RegExp)]),\n  markup: PropTypes.string,\n  displayTransform: PropTypes.func,\n\n  /**\n   * If set to `true` spaces will not interrupt matching suggestions\n   */\n  allowSpaceInQuery: PropTypes.bool,\n  isLoading: PropTypes.bool\n};\nMention.defaultProps = {\n  trigger: '@',\n  markup: '@[__display__](__id__)',\n  displayTransform: function displayTransform(id, display) {\n    return display || id;\n  },\n  onAdd: function onAdd() {\n    return null;\n  },\n  onRemove: function onRemove() {\n    return null;\n  },\n  renderSuggestion: null,\n  isLoading: false,\n  appendSpaceOnAdd: false\n};\n\nexport { Mention, MentionsInput$1 as MentionsInput };\n", "export default function _arrayWithHoles(arr) {\n  if (Array.isArray(arr)) return arr;\n}", "export default function _iterableToArrayLimit(arr, i) {\n  var _arr = [];\n  var _n = true;\n  var _d = false;\n  var _e = undefined;\n\n  try {\n    for (var _i = arr[Symbol.iterator](), _s; !(_n = (_s = _i.next()).done); _n = true) {\n      _arr.push(_s.value);\n\n      if (i && _arr.length === i) break;\n    }\n  } catch (err) {\n    _d = true;\n    _e = err;\n  } finally {\n    try {\n      if (!_n && _i[\"return\"] != null) _i[\"return\"]();\n    } finally {\n      if (_d) throw _e;\n    }\n  }\n\n  return _arr;\n}", "export default function _nonIterableRest() {\n  throw new TypeError(\"Invalid attempt to destructure non-iterable instance\");\n}", "import arrayWithHoles from \"./arrayWithHoles\";\nimport iterableToArrayLimit from \"./iterableToArrayLimit\";\nimport nonIterableRest from \"./nonIterableRest\";\nexport default function _slicedToArray(arr, i) {\n  return arrayWithHoles(arr) || iterableToArrayLimit(arr, i) || nonIterableRest();\n}", "export default function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}", "import objectWithoutPropertiesLoose from \"./objectWithoutPropertiesLoose\";\nexport default function _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n\n  return target;\n}", "import { createContext } from 'react';\nimport defaultPropsDecorator from './defaultPropsDecorator';\nexport var PropsDecoratorContext = /*#__PURE__*/createContext(defaultPropsDecorator);\nexport default PropsDecoratorContext.Provider;", "function _typeof(o) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n    return typeof o;\n  } : function (o) {\n    return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n  }, _typeof(o);\n}\nexport { _typeof as default };", "import _typeof from \"./typeof.js\";\nfunction toPrimitive(t, r) {\n  if (\"object\" != _typeof(t) || !t) return t;\n  var e = t[Symbol.toPrimitive];\n  if (void 0 !== e) {\n    var i = e.call(t, r || \"default\");\n    if (\"object\" != _typeof(i)) return i;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (\"string\" === r ? String : Number)(t);\n}\nexport { toPrimitive as default };", "import _typeof from \"./typeof.js\";\nimport toPrimitive from \"./toPrimitive.js\";\nfunction toPropertyKey(t) {\n  var i = toPrimitive(t, \"string\");\n  return \"symbol\" == _typeof(i) ? i : i + \"\";\n}\nexport { toPropertyKey as default };", "import toPropertyKey from \"./toPropertyKey.js\";\nfunction _defineProperty(e, r, t) {\n  return (r = toPropertyKey(r)) in e ? Object.defineProperty(e, r, {\n    value: t,\n    enumerable: !0,\n    configurable: !0,\n    writable: !0\n  }) : e[r] = t, e;\n}\nexport { _defineProperty as default };", "function _arrayLikeToArray(r, a) {\n  (null == a || a > r.length) && (a = r.length);\n  for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e];\n  return n;\n}\nexport { _arrayLikeToArray as default };", "import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _arrayWithoutHoles(r) {\n  if (Array.isArray(r)) return arrayLikeToArray(r);\n}\nexport { _arrayWithoutHoles as default };", "function _iterableToArray(r) {\n  if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r);\n}\nexport { _iterableToArray as default };", "import arrayLikeToArray from \"./arrayLikeToArray.js\";\nfunction _unsupportedIterableToArray(r, a) {\n  if (r) {\n    if (\"string\" == typeof r) return arrayLikeToArray(r, a);\n    var t = {}.toString.call(r).slice(8, -1);\n    return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? arrayLikeToArray(r, a) : void 0;\n  }\n}\nexport { _unsupportedIterableToArray as default };", "function _nonIterableSpread() {\n  throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n}\nexport { _nonIterableSpread as default };", "import arrayWithoutHoles from \"./arrayWithoutHoles.js\";\nimport iterableToArray from \"./iterableToArray.js\";\nimport unsupportedIterableToArray from \"./unsupportedIterableToArray.js\";\nimport nonIterableSpread from \"./nonIterableSpread.js\";\nfunction _toConsumableArray(r) {\n  return arrayWithoutHoles(r) || iterableToArray(r) || unsupportedIterableToArray(r) || nonIterableSpread();\n}\nexport { _toConsumableArray as default };", "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nexport var keys = function keys(obj) {\n  return obj === Object(obj) ? Object.keys(obj) : [];\n};\nexport var values = function values(obj) {\n  return obj === Object(obj) ? Object.values(obj) : [];\n};\n\nfunction mergeDeep(target, source) {\n  var output = Object.assign({}, target);\n\n  if (isPlainObject(target) && isPlainObject(source)) {\n    keys(source).forEach(function (key) {\n      if (isPlainObject(source[key])) {\n        if (!(key in target)) Object.assign(output, _defineProperty({}, key, source[key]));else output[key] = mergeDeep(target[key], source[key]);\n      } else {\n        Object.assign(output, _defineProperty({}, key, source[key]));\n      }\n    });\n  }\n\n  return output;\n}\n\nexport var merge = function merge(target) {\n  for (var _len = arguments.length, sources = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    sources[_key - 1] = arguments[_key];\n  }\n\n  return sources.reduce(function (t, s) {\n    return mergeDeep(t, s);\n  }, target);\n};\nexport var identity = function identity(value) {\n  return value;\n};\nexport var omit = function omit(obj, keys) {\n  var other = Object.assign({}, obj);\n\n  if (keys) {\n    for (var i = 0; i < keys.length; i++) {\n      delete other[keys[i]];\n    }\n  }\n\n  return other;\n};\nexport var isPlainObject = function isPlainObject(obj) {\n  return obj === Object(obj) && !(obj instanceof Date) && !Array.isArray(obj);\n};\nexport var compact = function compact(arr) {\n  return (arr || []).filter(Boolean);\n};", "export var isModifier = function isModifier(key) {\n  return key[0] === '&';\n};\nexport var isElement = function isElement(key) {\n  return !isModifier(key);\n};", "import _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\nimport { keys, merge, omit, values } from './utils';\nimport { isModifier } from './filterKeys';\n\nvar camelize = function camelize(key) {\n  return key.replace(/-(\\w)/g, function (m, c) {\n    return c.toUpperCase();\n  });\n};\n\nexport var pickDirectStyles = function pickDirectStyles(style) {\n  var objectPropertiesWhitelist = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var styleKeys = keys(style);\n  var result = {};\n\n  for (var i = 0, l = styleKeys.length; i < l; i += 1) {\n    var key = styleKeys[i];\n    var isDirect = Object.prototype.toString.call(style[key]) !== '[object Object]' || // style defs\n    key[0] === ':' || // pseudo selectors\n    key[0] === '@' || // @media / @keyframes / @supports / @font-face\n    objectPropertiesWhitelist.indexOf(key) >= 0; // whitelisted object-type properties\n\n    if (isDirect) {\n      result[key] = style[key];\n    }\n  }\n\n  return result;\n};\nexport var pickNestedStyles = function pickNestedStyles(style, keysToPick) {\n  var camelizedKeysToPick = keysToPick.map(camelize);\n  var styleKeys = keys(style);\n  var result = {};\n\n  for (var i = 0, l = styleKeys.length; i < l; i += 1) {\n    var key = styleKeys[i];\n\n    if (keysToPick.indexOf(key) >= 0 || camelizedKeysToPick.indexOf(camelize(key)) >= 0) {\n      result[key] = style[key];\n    }\n  }\n\n  return result;\n}; // breadth-first hoisting of selected modifier style subtrees\n// does not traverse into element, :pseudo-selector or @directive subtrees\n\nexport var hoistModifierStylesRecursive = function hoistModifierStylesRecursive(style, modifierKeysToPick) {\n  // hoist styles for selected modifiers on current level\n  var result = merge.apply(void 0, [{}, omit(style, modifierKeysToPick)].concat(_toConsumableArray(values(pickNestedStyles(style, modifierKeysToPick))))); // traverse nested styled for ALL modifiers\n\n  var modifierKeys = keys(result).filter(isModifier);\n\n  for (var i = 0, l = modifierKeys.length; i < l; i += 1) {\n    var key = modifierKeys[i];\n    var subresult = hoistModifierStylesRecursive(result[key], modifierKeysToPick);\n\n    if (modifierKeysToPick.indexOf(key) >= 0) {\n      // selected modifier: hoist subresult\n      delete result[key];\n      result = merge({}, result, subresult);\n    } else {\n      // non-selected modifier: replace with subresult\n      result[key] = subresult;\n    }\n  }\n\n  return result;\n};", "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport { pickDirectStyles } from './pickStyles';\n// many css-in-js libs process keyframes objects as the value for `animationName`\nvar defaultObjectPropsWhitelist = ['animationName'];\n\nvar defaultPropsDecorator = function defaultPropsDecorator(_ref) {\n  var style = _ref.style,\n      className = _ref.className;\n  return _objectSpread(_objectSpread({}, style ? {\n    style: pickDirectStyles(style, defaultObjectPropsWhitelist)\n  } : {}), className ? {\n    className: className\n  } : {});\n};\n\nexport default defaultPropsDecorator;", "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport _toConsumableArray from \"@babel/runtime/helpers/toConsumableArray\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nimport invariant from 'invariant';\nimport coerceSelection from './coerceSelection';\nimport defaultPropsDecorator from './defaultPropsDecorator';\nimport { isElement, isModifier } from './filterKeys';\nimport memoize from './memoize';\nimport { hoistModifierStylesRecursive, pickNestedStyles } from './pickStyles';\nimport { compact, isPlainObject, keys, merge, values } from './utils';\n\nvar guessBaseClassName = function guessBaseClassName(classNames) {\n  // all class names must start with the same prefix: the component's base class name\n  // which will finally go to the container element\n  var firstKey = classNames && keys(classNames)[0];\n  return firstKey && firstKey.split('__')[0].split('--')[0];\n};\n\nvar deriveClassNames = function deriveClassNames(className, elementKeys, modifierKeys) {\n  // do not derive class names, if the user did not specify any class name\n  if (!className) {\n    return undefined;\n  } // derive class names based using the passed modifier/element keys\n\n\n  var firstClassName = className.split(' ')[0];\n  var derivedClassNames = [].concat(_toConsumableArray(elementKeys.length === 0 ? modifierKeys.map(function (key) {\n    return \"\".concat(firstClassName, \"--\").concat(key.substring(1));\n  }) : []), _toConsumableArray(elementKeys.map(function (key) {\n    return \"\".concat(firstClassName, \"__\").concat(key);\n  }))); // also use the provided `className` if there is no sub-element selection\n\n  return elementKeys.length === 0 ? [className].concat(_toConsumableArray(derivedClassNames)) : derivedClassNames;\n};\n\nfunction createSubstyle(_ref) {\n  var style = _ref.style,\n      className = _ref.className,\n      classNames = _ref.classNames;\n  var propsDecorator = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : defaultPropsDecorator;\n  var baseClassName = className || guessBaseClassName(classNames) || (style === null || style === void 0 ? void 0 : style.className);\n  var substyle = typeof style === 'function' ? style : memoize(function (select, defaultStyle) {\n    var selectedKeys = coerceSelection(select);\n    invariant(Array.isArray(selectedKeys), 'First parameter must be a string, an array of strings, ' + 'a plain object with boolean values, or a falsy value.');\n    invariant(!defaultStyle || isPlainObject(defaultStyle), 'Optional second parameter must be a plain object.');\n    var modifierKeys = selectedKeys.filter(isModifier);\n    var elementKeys = selectedKeys.filter(isElement);\n    var collectElementStyles = elementKeys.length > 0 ? function (fromStyle) {\n      return values(pickNestedStyles(fromStyle, elementKeys));\n    } : function (fromStyle) {\n      return [fromStyle];\n    };\n\n    var collectSelectedStyles = function collectSelectedStyles() {\n      var fromStyle = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      return collectElementStyles(hoistModifierStylesRecursive(fromStyle, modifierKeys));\n    };\n\n    var derivedClassNames = deriveClassNames(baseClassName, elementKeys, modifierKeys);\n    return createSubstyle(_objectSpread(_objectSpread(_objectSpread({}, (style || defaultStyle) && {\n      style: merge.apply(void 0, [{}].concat(_toConsumableArray(collectSelectedStyles(defaultStyle)), _toConsumableArray(collectSelectedStyles(style))))\n    }), derivedClassNames && {\n      className: derivedClassNames.join(' ')\n    }), classNames && {\n      classNames: classNames\n    }), propsDecorator);\n  });\n\n  var styleProps = _objectSpread({}, typeof style === 'function' ? style : {\n    style: style\n  });\n\n  var classNameSplit = _toConsumableArray(new Set([].concat(_toConsumableArray(styleProps.className ? styleProps.className.split(' ') : []), _toConsumableArray(baseClassName ? baseClassName.split(' ') : []))));\n\n  var mappedClassNames = classNames ? compact(classNameSplit.map(function (singleClassName) {\n    return classNames[singleClassName];\n  })) : classNameSplit;\n  var propsForSpread = propsDecorator(_objectSpread(_objectSpread({}, styleProps), mappedClassNames.length > 0 ? {\n    className: mappedClassNames.join(' ')\n  } : {})); // assign `style`, `className`, and/or any props added by the decorator to the return function object\n\n  Object.assign(substyle, propsForSpread);\n  return substyle;\n}\n\nexport default createSubstyle;", "import { keys } from './utils';\n\nvar coerceSelection = function coerceSelection(select) {\n  if (!select) {\n    return [];\n  } else if (typeof select === 'string') {\n    return [select];\n  } else if (!Array.isArray(select)) {\n    var objSelect = select; // workaround for https://github.com/facebook/flow/issues/5781\n\n    return keys(select).reduce(function (acc, key) {\n      return acc.concat(objSelect[key] ? [key] : []);\n    }, []);\n  }\n\n  return select;\n};\n\nexport default coerceSelection;", "import coerceSelection from './coerceSelection';\nvar EMPTY = {};\n\nvar memoize = function memoize(substyle) {\n  return function (select, defaultStyle) {\n    var cacheKey = defaultStyle || EMPTY;\n    substyle.memoize = substyle.memoize || new WeakMap();\n    var mapEntry;\n\n    if (!substyle.memoize.has(cacheKey)) {\n      mapEntry = {};\n      substyle.memoize.set(cacheKey, mapEntry);\n    } else {\n      mapEntry = substyle.memoize.get(cacheKey);\n    }\n\n    var selectHash = coerceSelection(select).join(' ');\n    return selectHash in mapEntry ? mapEntry[selectHash] : mapEntry[selectHash] = substyle(select || [], defaultStyle);\n  };\n};\n\nexport default memoize;", "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\n\nfunction ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); if (enumerableOnly) { symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; }); } keys.push.apply(keys, symbols); } return keys; }\n\nfunction _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i] != null ? arguments[i] : {}; if (i % 2) { ownKeys(Object(source), true).forEach(function (key) { _defineProperty(target, key, source[key]); }); } else if (Object.getOwnPropertyDescriptors) { Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)); } else { ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } } return target; }\n\nvar inline = function inline() {\n  for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n    args[_key] = arguments[_key];\n  }\n\n  return args.reduce(function (result, arg) {\n    return _objectSpread(_objectSpread(_objectSpread({}, result), typeof arg === 'function' ? arg : {}), {}, {\n      style: _objectSpread(_objectSpread({}, result.style), typeof arg === 'function' ? arg.style : arg)\n    });\n  }, {});\n};\n\nexport default inline;", "import { useContext, useMemo } from 'react';\nimport { PropsDecoratorContext } from './PropsDecoratorProvider';\nimport createSubstyle from './createSubstyle';\n\nvar useStyles = function useStyles(defaultStyle, _ref, modifiers) {\n  var style = _ref.style,\n      className = _ref.className,\n      classNames = _ref.classNames;\n  var propsDecorator = useContext(PropsDecoratorContext);\n  var substyle = useMemo(function () {\n    return createSubstyle({\n      style: style,\n      className: className,\n      classNames: classNames\n    }, propsDecorator);\n  }, [style, className, classNames, propsDecorator]);\n  return substyle(modifiers, defaultStyle);\n};\n\nexport default useStyles;", "import PropsDecoratorProvider, { PropsDecoratorContext } from './PropsDecoratorProvider';\nimport createSubstyle from './createSubstyle';\nimport defaultPropsDecorator from './defaultPropsDecorator';\nimport inline from './inline';\nimport useStyles from './useStyles';\nexport { createSubstyle, PropsDecoratorProvider, PropsDecoratorContext, defaultPropsDecorator, inline };\nexport default useStyles;"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAoBA,QAAIA,aAAY,SAAS,WAAW,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,GAAG;AAC5D,UAAI,MAAuC;AACzC,YAAI,WAAW,QAAW;AACxB,gBAAM,IAAI,MAAM,8CAA8C;AAAA,QAChE;AAAA,MACF;AAEA,UAAI,CAAC,WAAW;AACd,YAAI;AACJ,YAAI,WAAW,QAAW;AACxB,kBAAQ,IAAI;AAAA,YACV;AAAA,UAEF;AAAA,QACF,OAAO;AACL,cAAI,OAAO,CAAC,GAAG,GAAG,GAAG,GAAG,GAAG,CAAC;AAC5B,cAAI,WAAW;AACf,kBAAQ,IAAI;AAAA,YACV,OAAO,QAAQ,OAAO,WAAW;AAAE,qBAAO,KAAK,UAAU;AAAA,YAAG,CAAC;AAAA,UAC/D;AACA,gBAAM,OAAO;AAAA,QACf;AAEA,cAAM,cAAc;AACpB,cAAM;AAAA,MACR;AAAA,IACF;AAEA,WAAO,UAAUA;AAAA;AAAA;;;AChDF,SAAR,mBAAoC,KAAK;AAC9C,MAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,aAAS,IAAI,GAAG,OAAO,IAAI,MAAM,IAAI,MAAM,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjE,WAAK,CAAC,IAAI,IAAI,CAAC;AAAA,IACjB;AAEA,WAAO;AAAA,EACT;AACF;;;ACRe,SAAR,iBAAkC,MAAM;AAC7C,MAAI,OAAO,YAAY,OAAO,IAAI,KAAK,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM,qBAAsB,QAAO,MAAM,KAAK,IAAI;AAC9H;;;ACFe,SAAR,qBAAsC;AAC3C,QAAM,IAAI,UAAU,iDAAiD;AACvE;;;ACCe,SAAR,mBAAoC,KAAK;AAC9C,SAAO,mBAAkB,GAAG,KAAK,iBAAgB,GAAG,KAAK,mBAAkB;AAC7E;;;ACLe,SAAR,WAA4B;AACjC,aAAW,OAAO,UAAU,SAAU,QAAQ;AAC5C,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,UAAI,SAAS,UAAU,CAAC;AAExB,eAAS,OAAO,QAAQ;AACtB,YAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AACrD,iBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAEA,SAAO,SAAS,MAAM,MAAM,SAAS;AACvC;;;AChBe,SAAR,gBAAiC,UAAU,aAAa;AAC7D,MAAI,EAAE,oBAAoB,cAAc;AACtC,UAAM,IAAI,UAAU,mCAAmC;AAAA,EACzD;AACF;;;ACJA,SAAS,kBAAkB,QAAQ,OAAO;AACxC,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,QAAI,aAAa,MAAM,CAAC;AACxB,eAAW,aAAa,WAAW,cAAc;AACjD,eAAW,eAAe;AAC1B,QAAI,WAAW,WAAY,YAAW,WAAW;AACjD,WAAO,eAAe,QAAQ,WAAW,KAAK,UAAU;AAAA,EAC1D;AACF;AAEe,SAAR,aAA8B,aAAa,YAAY,aAAa;AACzE,MAAI,WAAY,mBAAkB,YAAY,WAAW,UAAU;AACnE,MAAI,YAAa,mBAAkB,aAAa,WAAW;AAC3D,SAAO;AACT;;;ACde,SAAR,uBAAwC,MAAM;AACnD,MAAI,SAAS,QAAQ;AACnB,UAAM,IAAI,eAAe,2DAA2D;AAAA,EACtF;AAEA,SAAO;AACT;;;ACNe,SAAR,gBAAiC,GAAG,GAAG;AAC5C,oBAAkB,OAAO,kBAAkB,SAASC,iBAAgBC,IAAGC,IAAG;AACxE,IAAAD,GAAE,YAAYC;AACd,WAAOD;AAAA,EACT;AAEA,SAAO,gBAAgB,GAAG,CAAC;AAC7B;;;ACNe,SAAR,UAA2B,UAAU,YAAY;AACtD,MAAI,OAAO,eAAe,cAAc,eAAe,MAAM;AAC3D,UAAM,IAAI,UAAU,oDAAoD;AAAA,EAC1E;AAEA,WAAS,YAAY,OAAO,OAAO,cAAc,WAAW,WAAW;AAAA,IACrE,aAAa;AAAA,MACX,OAAO;AAAA,MACP,UAAU;AAAA,MACV,cAAc;AAAA,IAChB;AAAA,EACF,CAAC;AACD,MAAI,WAAY,iBAAe,UAAU,UAAU;AACrD;;;ACdA,SAAS,SAAS,KAAK;AAAE,MAAI,OAAO,WAAW,cAAc,OAAO,OAAO,aAAa,UAAU;AAAE,eAAW,SAASE,UAASC,MAAK;AAAE,aAAO,OAAOA;AAAA,IAAK;AAAA,EAAG,OAAO;AAAE,eAAW,SAASD,UAASC,MAAK;AAAE,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,OAAOA;AAAA,IAAK;AAAA,EAAG;AAAE,SAAO,SAAS,GAAG;AAAG;AAErV,SAAR,QAAyB,KAAK;AACnC,MAAI,OAAO,WAAW,cAAc,SAAS,OAAO,QAAQ,MAAM,UAAU;AAC1E,cAAU,SAASC,SAAQD,MAAK;AAC9B,aAAO,SAASA,IAAG;AAAA,IACrB;AAAA,EACF,OAAO;AACL,cAAU,SAASC,SAAQD,MAAK;AAC9B,aAAOA,QAAO,OAAO,WAAW,cAAcA,KAAI,gBAAgB,UAAUA,SAAQ,OAAO,YAAY,WAAW,SAASA,IAAG;AAAA,IAChI;AAAA,EACF;AAEA,SAAO,QAAQ,GAAG;AACpB;;;ACZe,SAAR,2BAA4C,MAAM,MAAM;AAC7D,MAAI,SAAS,QAAQ,IAAI,MAAM,YAAY,OAAO,SAAS,aAAa;AACtE,WAAO;AAAA,EACT;AAEA,SAAO,uBAAsB,IAAI;AACnC;;;ACRe,SAAR,gBAAiC,GAAG;AACzC,oBAAkB,OAAO,iBAAiB,OAAO,iBAAiB,SAASE,iBAAgBC,IAAG;AAC5F,WAAOA,GAAE,aAAa,OAAO,eAAeA,EAAC;AAAA,EAC/C;AACA,SAAO,gBAAgB,CAAC;AAC1B;;;ACLe,SAAR,gBAAiC,KAAK,KAAK,OAAO;AACvD,MAAI,OAAO,KAAK;AACd,WAAO,eAAe,KAAK,KAAK;AAAA,MAC9B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,MACd,UAAU;AAAA,IACZ,CAAC;AAAA,EACH,OAAO;AACL,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;;;ACJA,IAAAC,gBAAqD;AACrD,IAAAC,oBAAsB;;;ACVP,SAAR,gBAAiC,KAAK;AAC3C,MAAI,MAAM,QAAQ,GAAG,EAAG,QAAO;AACjC;;;ACFe,SAAR,sBAAuC,KAAK,GAAG;AACpD,MAAI,OAAO,CAAC;AACZ,MAAI,KAAK;AACT,MAAI,KAAK;AACT,MAAI,KAAK;AAET,MAAI;AACF,aAAS,KAAK,IAAI,OAAO,QAAQ,EAAE,GAAG,IAAI,EAAE,MAAM,KAAK,GAAG,KAAK,GAAG,OAAO,KAAK,MAAM;AAClF,WAAK,KAAK,GAAG,KAAK;AAElB,UAAI,KAAK,KAAK,WAAW,EAAG;AAAA,IAC9B;AAAA,EACF,SAAS,KAAK;AACZ,SAAK;AACL,SAAK;AAAA,EACP,UAAE;AACA,QAAI;AACF,UAAI,CAAC,MAAM,GAAG,QAAQ,KAAK,KAAM,IAAG,QAAQ,EAAE;AAAA,IAChD,UAAE;AACA,UAAI,GAAI,OAAM;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT;;;ACxBe,SAAR,mBAAoC;AACzC,QAAM,IAAI,UAAU,sDAAsD;AAC5E;;;ACCe,SAAR,eAAgC,KAAK,GAAG;AAC7C,SAAO,gBAAe,GAAG,KAAK,sBAAqB,KAAK,CAAC,KAAK,iBAAgB;AAChF;;;ACLe,SAAR,8BAA+C,QAAQ,UAAU;AACtE,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,OAAO,KAAK,MAAM;AACnC,MAAI,KAAK;AAET,OAAK,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK;AACtC,UAAM,WAAW,CAAC;AAClB,QAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,WAAO,GAAG,IAAI,OAAO,GAAG;AAAA,EAC1B;AAEA,SAAO;AACT;;;ACZe,SAAR,yBAA0C,QAAQ,UAAU;AACjE,MAAI,UAAU,KAAM,QAAO,CAAC;AAC5B,MAAI,SAAS,8BAA6B,QAAQ,QAAQ;AAC1D,MAAI,KAAK;AAET,MAAI,OAAO,uBAAuB;AAChC,QAAI,mBAAmB,OAAO,sBAAsB,MAAM;AAE1D,SAAK,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK;AAC5C,YAAM,iBAAiB,CAAC;AACxB,UAAI,SAAS,QAAQ,GAAG,KAAK,EAAG;AAChC,UAAI,CAAC,OAAO,UAAU,qBAAqB,KAAK,QAAQ,GAAG,EAAG;AAC9D,aAAO,GAAG,IAAI,OAAO,GAAG;AAAA,IAC1B;AAAA,EACF;AAEA,SAAO;AACT;;;AClBA,mBAA8B;;;ACA9B,SAASC,SAAQ,GAAG;AAClB;AAEA,SAAOA,WAAU,cAAc,OAAO,UAAU,YAAY,OAAO,OAAO,WAAW,SAAUC,IAAG;AAChG,WAAO,OAAOA;AAAA,EAChB,IAAI,SAAUA,IAAG;AACf,WAAOA,MAAK,cAAc,OAAO,UAAUA,GAAE,gBAAgB,UAAUA,OAAM,OAAO,YAAY,WAAW,OAAOA;AAAA,EACpH,GAAGD,SAAQ,CAAC;AACd;;;ACPA,SAAS,YAAY,GAAG,GAAG;AACzB,MAAI,YAAYE,SAAQ,CAAC,KAAK,CAAC,EAAG,QAAO;AACzC,MAAI,IAAI,EAAE,OAAO,WAAW;AAC5B,MAAI,WAAW,GAAG;AAChB,QAAI,IAAI,EAAE,KAAK,GAAG,KAAK,SAAS;AAChC,QAAI,YAAYA,SAAQ,CAAC,EAAG,QAAO;AACnC,UAAM,IAAI,UAAU,8CAA8C;AAAA,EACpE;AACA,UAAQ,aAAa,IAAI,SAAS,QAAQ,CAAC;AAC7C;;;ACRA,SAAS,cAAc,GAAG;AACxB,MAAI,IAAI,YAAY,GAAG,QAAQ;AAC/B,SAAO,YAAYC,SAAQ,CAAC,IAAI,IAAI,IAAI;AAC1C;;;ACJA,SAASC,iBAAgB,GAAG,GAAG,GAAG;AAChC,UAAQ,IAAI,cAAc,CAAC,MAAM,IAAI,OAAO,eAAe,GAAG,GAAG;AAAA,IAC/D,OAAO;AAAA,IACP,YAAY;AAAA,IACZ,cAAc;AAAA,IACd,UAAU;AAAA,EACZ,CAAC,IAAI,EAAE,CAAC,IAAI,GAAG;AACjB;;;ACRA,SAAS,kBAAkB,GAAG,GAAG;AAC/B,GAAC,QAAQ,KAAK,IAAI,EAAE,YAAY,IAAI,EAAE;AACtC,WAAS,IAAI,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,GAAG,IAAK,GAAE,CAAC,IAAI,EAAE,CAAC;AACpD,SAAO;AACT;;;ACHA,SAASC,oBAAmB,GAAG;AAC7B,MAAI,MAAM,QAAQ,CAAC,EAAG,QAAO,kBAAiB,CAAC;AACjD;;;ACHA,SAASC,kBAAiB,GAAG;AAC3B,MAAI,eAAe,OAAO,UAAU,QAAQ,EAAE,OAAO,QAAQ,KAAK,QAAQ,EAAE,YAAY,EAAG,QAAO,MAAM,KAAK,CAAC;AAChH;;;ACDA,SAAS,4BAA4B,GAAG,GAAG;AACzC,MAAI,GAAG;AACL,QAAI,YAAY,OAAO,EAAG,QAAO,kBAAiB,GAAG,CAAC;AACtD,QAAI,IAAI,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE,MAAM,GAAG,EAAE;AACvC,WAAO,aAAa,KAAK,EAAE,gBAAgB,IAAI,EAAE,YAAY,OAAO,UAAU,KAAK,UAAU,IAAI,MAAM,KAAK,CAAC,IAAI,gBAAgB,KAAK,2CAA2C,KAAK,CAAC,IAAI,kBAAiB,GAAG,CAAC,IAAI;AAAA,EACtN;AACF;;;ACPA,SAASC,sBAAqB;AAC5B,QAAM,IAAI,UAAU,sIAAsI;AAC5J;;;ACEA,SAASC,oBAAmB,GAAG;AAC7B,SAAOC,oBAAkB,CAAC,KAAKC,kBAAgB,CAAC,KAAK,4BAA2B,CAAC,KAAKC,oBAAkB;AAC1G;;;ACLO,IAAI,OAAO,SAASC,MAAK,KAAK;AACnC,SAAO,QAAQ,OAAO,GAAG,IAAI,OAAO,KAAK,GAAG,IAAI,CAAC;AACnD;AACO,IAAI,SAAS,SAASC,QAAO,KAAK;AACvC,SAAO,QAAQ,OAAO,GAAG,IAAI,OAAO,OAAO,GAAG,IAAI,CAAC;AACrD;AAEA,SAAS,UAAU,QAAQ,QAAQ;AACjC,MAAI,SAAS,OAAO,OAAO,CAAC,GAAG,MAAM;AAErC,MAAI,cAAc,MAAM,KAAK,cAAc,MAAM,GAAG;AAClD,SAAK,MAAM,EAAE,QAAQ,SAAU,KAAK;AAClC,UAAI,cAAc,OAAO,GAAG,CAAC,GAAG;AAC9B,YAAI,EAAE,OAAO,QAAS,QAAO,OAAO,QAAQC,iBAAgB,CAAC,GAAG,KAAK,OAAO,GAAG,CAAC,CAAC;AAAA,YAAO,QAAO,GAAG,IAAI,UAAU,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;AAAA,MAC1I,OAAO;AACL,eAAO,OAAO,QAAQA,iBAAgB,CAAC,GAAG,KAAK,OAAO,GAAG,CAAC,CAAC;AAAA,MAC7D;AAAA,IACF,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEO,IAAI,QAAQ,SAASC,OAAM,QAAQ;AACxC,WAAS,OAAO,UAAU,QAAQ,UAAU,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC7G,YAAQ,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACpC;AAEA,SAAO,QAAQ,OAAO,SAAU,GAAG,GAAG;AACpC,WAAO,UAAU,GAAG,CAAC;AAAA,EACvB,GAAG,MAAM;AACX;AAIO,IAAI,OAAO,SAASC,MAAK,KAAKC,OAAM;AACzC,MAAI,QAAQ,OAAO,OAAO,CAAC,GAAG,GAAG;AAEjC,MAAIA,OAAM;AACR,aAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ,KAAK;AACpC,aAAO,MAAMA,MAAK,CAAC,CAAC;AAAA,IACtB;AAAA,EACF;AAEA,SAAO;AACT;AACO,IAAI,gBAAgB,SAASC,eAAc,KAAK;AACrD,SAAO,QAAQ,OAAO,GAAG,KAAK,EAAE,eAAe,SAAS,CAAC,MAAM,QAAQ,GAAG;AAC5E;AACO,IAAI,UAAU,SAASC,SAAQ,KAAK;AACzC,UAAQ,OAAO,CAAC,GAAG,OAAO,OAAO;AACnC;;;ACpDO,IAAI,aAAa,SAASC,YAAW,KAAK;AAC/C,SAAO,IAAI,CAAC,MAAM;AACpB;AACO,IAAI,YAAY,SAASC,WAAU,KAAK;AAC7C,SAAO,CAAC,WAAW,GAAG;AACxB;;;ACDA,IAAI,WAAW,SAASC,UAAS,KAAK;AACpC,SAAO,IAAI,QAAQ,UAAU,SAAU,GAAG,GAAG;AAC3C,WAAO,EAAE,YAAY;AAAA,EACvB,CAAC;AACH;AAEO,IAAI,mBAAmB,SAASC,kBAAiB,OAAO;AAC7D,MAAI,4BAA4B,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACrG,MAAI,YAAY,KAAK,KAAK;AAC1B,MAAI,SAAS,CAAC;AAEd,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK,GAAG;AACnD,QAAI,MAAM,UAAU,CAAC;AACrB,QAAI,WAAW,OAAO,UAAU,SAAS,KAAK,MAAM,GAAG,CAAC,MAAM;AAAA,IAC9D,IAAI,CAAC,MAAM;AAAA,IACX,IAAI,CAAC,MAAM;AAAA,IACX,0BAA0B,QAAQ,GAAG,KAAK;AAE1C,QAAI,UAAU;AACZ,aAAO,GAAG,IAAI,MAAM,GAAG;AAAA,IACzB;AAAA,EACF;AAEA,SAAO;AACT;AACO,IAAI,mBAAmB,SAASC,kBAAiB,OAAO,YAAY;AACzE,MAAI,sBAAsB,WAAW,IAAI,QAAQ;AACjD,MAAI,YAAY,KAAK,KAAK;AAC1B,MAAI,SAAS,CAAC;AAEd,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK,GAAG;AACnD,QAAI,MAAM,UAAU,CAAC;AAErB,QAAI,WAAW,QAAQ,GAAG,KAAK,KAAK,oBAAoB,QAAQ,SAAS,GAAG,CAAC,KAAK,GAAG;AACnF,aAAO,GAAG,IAAI,MAAM,GAAG;AAAA,IACzB;AAAA,EACF;AAEA,SAAO;AACT;AAGO,IAAI,+BAA+B,SAASC,8BAA6B,OAAO,oBAAoB;AAEzG,MAAI,SAAS,MAAM,MAAM,QAAQ,CAAC,CAAC,GAAG,KAAK,OAAO,kBAAkB,CAAC,EAAE,OAAOC,oBAAmB,OAAO,iBAAiB,OAAO,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAEtJ,MAAI,eAAe,KAAK,MAAM,EAAE,OAAO,UAAU;AAEjD,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,IAAI,GAAG,KAAK,GAAG;AACtD,QAAI,MAAM,aAAa,CAAC;AACxB,QAAI,YAAYD,8BAA6B,OAAO,GAAG,GAAG,kBAAkB;AAE5E,QAAI,mBAAmB,QAAQ,GAAG,KAAK,GAAG;AAExC,aAAO,OAAO,GAAG;AACjB,eAAS,MAAM,CAAC,GAAG,QAAQ,SAAS;AAAA,IACtC,OAAO;AAEL,aAAO,GAAG,IAAI;AAAA,IAChB;AAAA,EACF;AAEA,SAAO;AACT;;;ACjEA,SAAS,QAAQ,QAAQ,gBAAgB;AAAE,MAAIE,QAAO,OAAO,KAAK,MAAM;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,QAAI,gBAAgB;AAAE,gBAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MAAY,CAAC;AAAA,IAAG;AAAE,IAAAA,MAAK,KAAK,MAAMA,OAAM,OAAO;AAAA,EAAG;AAAE,SAAOA;AAAM;AAExV,SAAS,cAAc,QAAQ;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAI,GAAG;AAAE,cAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AAAE,QAAAC,iBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA,IAAG,WAAW,OAAO,2BAA2B;AAAE,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAAG,OAAO;AAAE,cAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAE;AAAE,SAAO;AAAQ;AAIrhB,IAAI,8BAA8B,CAAC,eAAe;AAElD,IAAI,wBAAwB,SAASC,uBAAsB,MAAM;AAC/D,MAAI,QAAQ,KAAK,OACb,YAAY,KAAK;AACrB,SAAO,cAAc,cAAc,CAAC,GAAG,QAAQ;AAAA,IAC7C,OAAO,iBAAiB,OAAO,2BAA2B;AAAA,EAC5D,IAAI,CAAC,CAAC,GAAG,YAAY;AAAA,IACnB;AAAA,EACF,IAAI,CAAC,CAAC;AACR;AAEA,IAAO,gCAAQ;;;AdlBR,IAAI,4BAAqC,4BAAc,6BAAqB;AACnF,IAAO,iCAAQ,sBAAsB;;;AeIrC,uBAAsB;;;ACLtB,IAAI,kBAAkB,SAASC,iBAAgB,QAAQ;AACrD,MAAI,CAAC,QAAQ;AACX,WAAO,CAAC;AAAA,EACV,WAAW,OAAO,WAAW,UAAU;AACrC,WAAO,CAAC,MAAM;AAAA,EAChB,WAAW,CAAC,MAAM,QAAQ,MAAM,GAAG;AACjC,QAAI,YAAY;AAEhB,WAAO,KAAK,MAAM,EAAE,OAAO,SAAU,KAAK,KAAK;AAC7C,aAAO,IAAI,OAAO,UAAU,GAAG,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;AAAA,IAC/C,GAAG,CAAC,CAAC;AAAA,EACP;AAEA,SAAO;AACT;AAEA,IAAO,0BAAQ;;;ACjBf,IAAI,QAAQ,CAAC;AAEb,IAAI,UAAU,SAASC,SAAQ,UAAU;AACvC,SAAO,SAAU,QAAQC,eAAc;AACrC,QAAI,WAAWA,iBAAgB;AAC/B,aAAS,UAAU,SAAS,WAAW,oBAAI,QAAQ;AACnD,QAAI;AAEJ,QAAI,CAAC,SAAS,QAAQ,IAAI,QAAQ,GAAG;AACnC,iBAAW,CAAC;AACZ,eAAS,QAAQ,IAAI,UAAU,QAAQ;AAAA,IACzC,OAAO;AACL,iBAAW,SAAS,QAAQ,IAAI,QAAQ;AAAA,IAC1C;AAEA,QAAI,aAAa,wBAAgB,MAAM,EAAE,KAAK,GAAG;AACjD,WAAO,cAAc,WAAW,SAAS,UAAU,IAAI,SAAS,UAAU,IAAI,SAAS,UAAU,CAAC,GAAGA,aAAY;AAAA,EACnH;AACF;AAEA,IAAO,kBAAQ;;;AFlBf,SAASC,SAAQ,QAAQ,gBAAgB;AAAE,MAAIC,QAAO,OAAO,KAAK,MAAM;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,QAAI,gBAAgB;AAAE,gBAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MAAY,CAAC;AAAA,IAAG;AAAE,IAAAA,MAAK,KAAK,MAAMA,OAAM,OAAO;AAAA,EAAG;AAAE,SAAOA;AAAM;AAExV,SAASC,eAAc,QAAQ;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAI,GAAG;AAAE,MAAAF,SAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AAAE,QAAAG,iBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA,IAAG,WAAW,OAAO,2BAA2B;AAAE,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAAG,OAAO;AAAE,MAAAH,SAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAE;AAAE,SAAO;AAAQ;AAUrhB,IAAI,qBAAqB,SAASI,oBAAmB,YAAY;AAG/D,MAAI,WAAW,cAAc,KAAK,UAAU,EAAE,CAAC;AAC/C,SAAO,YAAY,SAAS,MAAM,IAAI,EAAE,CAAC,EAAE,MAAM,IAAI,EAAE,CAAC;AAC1D;AAEA,IAAI,mBAAmB,SAASC,kBAAiB,WAAW,aAAa,cAAc;AAErF,MAAI,CAAC,WAAW;AACd,WAAO;AAAA,EACT;AAGA,MAAI,iBAAiB,UAAU,MAAM,GAAG,EAAE,CAAC;AAC3C,MAAI,oBAAoB,CAAC,EAAE,OAAOC,oBAAmB,YAAY,WAAW,IAAI,aAAa,IAAI,SAAU,KAAK;AAC9G,WAAO,GAAG,OAAO,gBAAgB,IAAI,EAAE,OAAO,IAAI,UAAU,CAAC,CAAC;AAAA,EAChE,CAAC,IAAI,CAAC,CAAC,GAAGA,oBAAmB,YAAY,IAAI,SAAU,KAAK;AAC1D,WAAO,GAAG,OAAO,gBAAgB,IAAI,EAAE,OAAO,GAAG;AAAA,EACnD,CAAC,CAAC,CAAC;AAEH,SAAO,YAAY,WAAW,IAAI,CAAC,SAAS,EAAE,OAAOA,oBAAmB,iBAAiB,CAAC,IAAI;AAChG;AAEA,SAAS,eAAe,MAAM;AAC5B,MAAI,QAAQ,KAAK,OACb,YAAY,KAAK,WACjB,aAAa,KAAK;AACtB,MAAI,iBAAiB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACzF,MAAI,gBAAgB,aAAa,mBAAmB,UAAU,MAAM,UAAU,QAAQ,UAAU,SAAS,SAAS,MAAM;AACxH,MAAI,WAAW,OAAO,UAAU,aAAa,QAAQ,gBAAQ,SAAU,QAAQC,eAAc;AAC3F,QAAI,eAAe,wBAAgB,MAAM;AACzC,yBAAAC,SAAU,MAAM,QAAQ,YAAY,GAAG,8GAAmH;AAC1J,yBAAAA,SAAU,CAACD,iBAAgB,cAAcA,aAAY,GAAG,mDAAmD;AAC3G,QAAI,eAAe,aAAa,OAAO,UAAU;AACjD,QAAI,cAAc,aAAa,OAAO,SAAS;AAC/C,QAAI,uBAAuB,YAAY,SAAS,IAAI,SAAU,WAAW;AACvE,aAAO,OAAO,iBAAiB,WAAW,WAAW,CAAC;AAAA,IACxD,IAAI,SAAU,WAAW;AACvB,aAAO,CAAC,SAAS;AAAA,IACnB;AAEA,QAAI,wBAAwB,SAASE,yBAAwB;AAC3D,UAAI,YAAY,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AACrF,aAAO,qBAAqB,6BAA6B,WAAW,YAAY,CAAC;AAAA,IACnF;AAEA,QAAI,oBAAoB,iBAAiB,eAAe,aAAa,YAAY;AACjF,WAAO,eAAeP,eAAcA,eAAcA,eAAc,CAAC,IAAI,SAASK,kBAAiB;AAAA,MAC7F,OAAO,MAAM,MAAM,QAAQ,CAAC,CAAC,CAAC,EAAE,OAAOD,oBAAmB,sBAAsBC,aAAY,CAAC,GAAGD,oBAAmB,sBAAsB,KAAK,CAAC,CAAC,CAAC;AAAA,IACnJ,CAAC,GAAG,qBAAqB;AAAA,MACvB,WAAW,kBAAkB,KAAK,GAAG;AAAA,IACvC,CAAC,GAAG,cAAc;AAAA,MAChB;AAAA,IACF,CAAC,GAAG,cAAc;AAAA,EACpB,CAAC;AAED,MAAI,aAAaJ,eAAc,CAAC,GAAG,OAAO,UAAU,aAAa,QAAQ;AAAA,IACvE;AAAA,EACF,CAAC;AAED,MAAI,iBAAiBI,oBAAmB,IAAI,IAAI,CAAC,EAAE,OAAOA,oBAAmB,WAAW,YAAY,WAAW,UAAU,MAAM,GAAG,IAAI,CAAC,CAAC,GAAGA,oBAAmB,gBAAgB,cAAc,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;AAE9M,MAAI,mBAAmB,aAAa,QAAQ,eAAe,IAAI,SAAU,iBAAiB;AACxF,WAAO,WAAW,eAAe;AAAA,EACnC,CAAC,CAAC,IAAI;AACN,MAAI,iBAAiB,eAAeJ,eAAcA,eAAc,CAAC,GAAG,UAAU,GAAG,iBAAiB,SAAS,IAAI;AAAA,IAC7G,WAAW,iBAAiB,KAAK,GAAG;AAAA,EACtC,IAAI,CAAC,CAAC,CAAC;AAEP,SAAO,OAAO,UAAU,cAAc;AACtC,SAAO;AACT;AAEA,IAAO,yBAAQ;;;AGvFf,SAASQ,SAAQ,QAAQ,gBAAgB;AAAE,MAAIC,QAAO,OAAO,KAAK,MAAM;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,QAAI,gBAAgB;AAAE,gBAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,eAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,MAAY,CAAC;AAAA,IAAG;AAAE,IAAAA,MAAK,KAAK,MAAMA,OAAM,OAAO;AAAA,EAAG;AAAE,SAAOA;AAAM;AAExV,SAASC,eAAc,QAAQ;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,SAAS,UAAU,CAAC,KAAK,OAAO,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAI,GAAG;AAAE,MAAAF,SAAQ,OAAO,MAAM,GAAG,IAAI,EAAE,QAAQ,SAAU,KAAK;AAAE,QAAAG,iBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA,IAAG,WAAW,OAAO,2BAA2B;AAAE,aAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC;AAAA,IAAG,OAAO;AAAE,MAAAH,SAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,eAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,MAAG,CAAC;AAAA,IAAG;AAAA,EAAE;AAAE,SAAO;AAAQ;AAErhB,IAAI,SAAS,SAASI,UAAS;AAC7B,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,SAAK,IAAI,IAAI,UAAU,IAAI;AAAA,EAC7B;AAEA,SAAO,KAAK,OAAO,SAAU,QAAQ,KAAK;AACxC,WAAOF,eAAcA,eAAcA,eAAc,CAAC,GAAG,MAAM,GAAG,OAAO,QAAQ,aAAa,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG;AAAA,MACvG,OAAOA,eAAcA,eAAc,CAAC,GAAG,OAAO,KAAK,GAAG,OAAO,QAAQ,aAAa,IAAI,QAAQ,GAAG;AAAA,IACnG,CAAC;AAAA,EACH,GAAG,CAAC,CAAC;AACP;AAEA,IAAO,iBAAQ;;;AClBf,IAAAG,gBAAoC;AAIpC,IAAI,YAAY,SAASC,WAAUC,eAAc,MAAM,WAAW;AAChE,MAAI,QAAQ,KAAK,OACb,YAAY,KAAK,WACjB,aAAa,KAAK;AACtB,MAAI,qBAAiB,0BAAW,qBAAqB;AACrD,MAAI,eAAW,uBAAQ,WAAY;AACjC,WAAO,uBAAe;AAAA,MACpB;AAAA,MACA;AAAA,MACA;AAAA,IACF,GAAG,cAAc;AAAA,EACnB,GAAG,CAAC,OAAO,WAAW,YAAY,cAAc,CAAC;AACjD,SAAO,SAAS,WAAWA,aAAY;AACzC;AAEA,IAAO,oBAAQ;;;ACbf,IAAO,cAAQ;;;A3BQf,wBAAsB;AACtB,uBAAqB;AAGrB,IAAI,cAAc,SAASC,aAAY,KAAK;AAC1C,SAAO,IAAI,QAAQ,4BAA4B,MAAM;AACvD;AAEA,IAAI,eAAe;AAAA,EACjB,IAAI;AAAA,EACJ,SAAS;AACX;AAEA,IAAI,+BAA+B,SAASC,8BAA6B,QAAQ,eAAe;AAC9F,wBAAAC,SAAU,kBAAkB,QAAQ,kBAAkB,WAAW,sDAA2D,OAAO,eAAe,GAAI,CAAC;AAEvJ,MAAI,eAAe,OAAO,QAAQ,aAAa,OAAO;AACtD,MAAI,UAAU,OAAO,QAAQ,aAAa,EAAE;AAE5C,MAAI,eAAe,EAAG,gBAAe;AACrC,MAAI,UAAU,EAAG,WAAU;AAE3B,wBAAAA,SAAU,iBAAiB,QAAQ,YAAY,MAAM,eAAe,OAAO,QAAQ,yEAAyE,CAAC;AAE7J,MAAI,iBAAiB,QAAQ,YAAY,MAAM;AAE7C,WAAO,kBAAkB,QAAQ,WAAW,gBAAgB,kBAAkB,aAAa,gBAAgB,UAAU,IAAI;AAAA,EAC3H;AAGA,SAAO;AACT;AAEA,IAAI,iBAAiB,SAASC,gBAAe,SAAS;AACpD,MAAI,wBAAwB;AAC5B,SAAO,IAAI,OAAO,QAAQ,IAAI,SAAU,OAAO;AAC7C,QAAI,wBAAwB,sBAAsB,KAAK,MAAM,SAAS,CAAC,GACnE,yBAAyB,eAAe,uBAAuB,CAAC,GAChE,cAAc,uBAAuB,CAAC,GACtC,aAAa,uBAAuB,CAAC;AAEzC,0BAAAD,SAAU,CAAC,YAAY,2CAA2C,OAAO,aAAa,GAAG,EAAE,OAAO,YAAY,SAAS,EAAE,OAAO,aAAa,GAAG,CAAC;AACjJ,WAAO,IAAI,OAAO,aAAa,GAAG;AAAA,EACpC,CAAC,EAAE,KAAK,GAAG,GAAG,GAAG;AACnB;AAEA,IAAI,oBAAoB,SAASE,mBAAkB,QAAQ;AACzD,MAAI,QAAQ;AACZ,MAAI,OAAO,QAAQ,QAAQ,KAAK,EAAG;AACnC,MAAI,OAAO,QAAQ,aAAa,KAAK,EAAG;AACxC,SAAO;AACT;AAEA,IAAI,UAAU,SAASC,WAAU;AAAC;AAIlC,IAAI,wBAAwB,SAASC,uBAAsB,OAAO,QAAQ,gBAAgB;AACxF,MAAI,eAAe,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AACvF,MAAI,QAAQ,eAAe,OAAO,IAAI,SAAU,GAAG;AACjD,WAAO,EAAE;AAAA,EACX,CAAC,CAAC;AACF,MAAI,YAAY;AAEhB,MAAI,sBAAsB,OAAO,IAAI,SAAU,MAAM;AACnD,QAAIC,UAAS,KAAK;AAClB,QAAI,SAAS;AAEb,iBAAa,kBAAkBA,OAAM,IAAI;AACzC,WAAO;AAAA,EACT,CAAC;AACD,MAAI;AACJ,MAAI,QAAQ;AACZ,MAAI,wBAAwB;AAE5B,UAAQ,QAAQ,MAAM,KAAK,KAAK,OAAO,MAAM;AAC3C,QAAI,SAAS,oBAAoB,KAAK,SAAU,GAAG;AACjD,aAAO,CAAC,CAAC,MAAM,CAAC;AAAA,IAClB,CAAC;AAED,QAAI,oBAAoB,oBAAoB,QAAQ,MAAM;AAC1D,QAAI,wBAAwB,OAAO,iBAAiB,GAChD,SAAS,sBAAsB,QAC/BC,oBAAmB,sBAAsB;AAC7C,QAAI,QAAQ,SAAS,6BAA6B,QAAQ,IAAI;AAC9D,QAAI,aAAa,SAAS,6BAA6B,QAAQ,SAAS;AACxE,QAAI,KAAK,MAAM,KAAK;AACpB,QAAI,UAAUA,kBAAiB,IAAI,MAAM,UAAU,CAAC;AACpD,QAAI,SAAS,MAAM,UAAU,OAAO,MAAM,KAAK;AAC/C,iBAAa,QAAQ,OAAO,qBAAqB;AACjD,6BAAyB,OAAO;AAChC,mBAAe,MAAM,CAAC,GAAG,MAAM,OAAO,uBAAuB,IAAI,SAAS,mBAAmB,KAAK;AAClG,6BAAyB,QAAQ;AACjC,YAAQ,MAAM;AAAA,EAChB;AAEA,MAAI,QAAQ,MAAM,QAAQ;AACxB,iBAAa,MAAM,UAAU,KAAK,GAAG,OAAO,qBAAqB;AAAA,EACnE;AACF;AAEA,IAAI,eAAe,SAASC,cAAa,OAAO,QAAQ;AACtD,MAAI,SAAS;AACb,wBAAsB,OAAO,QAAQ,SAAU,OAAO,OAAO,gBAAgB,IAAI,SAAS;AACxF,cAAU;AAAA,EACZ,GAAG,SAAU,WAAW;AACtB,cAAU;AAAA,EACZ,CAAC;AACD,SAAO;AACT;AASA,IAAI,oBAAoB,SAASC,mBAAkB,OAAO,QAAQ,kBAAkB;AAClF,MAAI,qBAAqB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAE7F,MAAI,OAAO,qBAAqB,UAAU;AACxC,WAAO;AAAA,EACT;AAEA,MAAI;AAEJ,MAAI,eAAe,SAASC,cAAa,QAAQ,OAAO,sBAAsB;AAC5E,QAAI,WAAW,OAAW;AAE1B,QAAI,uBAAuB,OAAO,UAAU,kBAAkB;AAE5D,eAAS,QAAQ,mBAAmB;AAAA,IACtC;AAAA,EACF;AAEA,MAAI,iBAAiB,SAASC,gBAAe,QAAQ,OAAO,uBAAuB,IAAI,SAAS,YAAY,qBAAqB;AAC/H,QAAI,WAAW,OAAW;AAE1B,QAAI,wBAAwB,QAAQ,SAAS,kBAAkB;AAI7D,UAAI,uBAAuB,QAAQ;AACjC,iBAAS;AAAA,MACX,OAAO;AACL,iBAAS,SAAS,uBAAuB,QAAQ,OAAO,SAAS;AAAA,MACnE;AAAA,IACF;AAAA,EACF;AAEA,wBAAsB,OAAO,QAAQ,gBAAgB,YAAY;AAGjE,SAAO,WAAW,SAAY,MAAM,SAAS;AAC/C;AAEA,IAAI,eAAe,SAASC,cAAa,KAAK,OAAO,KAAK,QAAQ;AAChE,SAAO,IAAI,UAAU,GAAG,KAAK,IAAI,SAAS,IAAI,UAAU,GAAG;AAC7D;AAIA,IAAI,qBAAqB,SAASC,oBAAmB,OAAO,gBAAgB,MAAM,QAAQ;AACxF,MAAI,uBAAuB,KAAK,sBAC5B,qBAAqB,KAAK,oBAC1B,oBAAoB,KAAK;AAC7B,MAAI,oBAAoB,aAAa,OAAO,MAAM;AAClD,MAAI,cAAc,kBAAkB,SAAS,eAAe;AAE5D,MAAI,yBAAyB,aAAa;AACxC,2BAAuB,oBAAoB;AAAA,EAC7C;AAEA,MAAI,uBAAuB,aAAa;AACtC,yBAAqB;AAAA,EACvB;AAGA,MAAI,yBAAyB,sBAAsB,uBAAuB,qBAAqB,kBAAkB,WAAW,eAAe,QAAQ;AACjJ,2BAAuB,uBAAuB;AAAA,EAChD;AAGA,MAAI,SAAS,eAAe,MAAM,sBAAsB,iBAAiB;AAEzE,MAAI,cAAc,KAAK,IAAI,sBAAsB,iBAAiB;AAClE,MAAI,YAAY;AAEhB,MAAI,yBAAyB,mBAAmB;AAE9C,gBAAY,KAAK,IAAI,oBAAoB,uBAAuB,WAAW;AAAA,EAC7E;AAEA,MAAI,oBAAoB,kBAAkB,OAAO,QAAQ,aAAa,OAAO;AAC7E,MAAI,kBAAkB,kBAAkB,OAAO,QAAQ,WAAW,KAAK;AACvE,MAAI,qBAAqB,kBAAkB,OAAO,QAAQ,aAAa,MAAM;AAC7E,MAAI,mBAAmB,kBAAkB,OAAO,QAAQ,WAAW,MAAM;AACzE,MAAI,oBAAoB,uBAAuB,QAAQ,qBAAqB;AAC5E,MAAI,WAAW,aAAa,OAAO,mBAAmB,iBAAiB,MAAM;AAE7E,MAAI,CAAC,mBAAmB;AAEtB,QAAI,wBAAwB,aAAa,UAAU,MAAM;AAEzD,QAAI,0BAA0B,gBAAgB;AAG5C,oBAAc;AAEd,aAAO,eAAe,WAAW,MAAM,sBAAsB,WAAW,GAAG;AACzE;AAAA,MACF;AAGA,eAAS,eAAe,MAAM,aAAa,iBAAiB;AAE5D,kBAAY,kBAAkB,YAAY,eAAe,UAAU,iBAAiB,CAAC;AAErF,0BAAoB,kBAAkB,OAAO,QAAQ,aAAa,OAAO;AACzE,wBAAkB,kBAAkB,OAAO,QAAQ,WAAW,KAAK;AACnE,iBAAW,aAAa,OAAO,mBAAmB,iBAAiB,MAAM;AAAA,IAC3E;AAAA,EACF;AAEA,SAAO;AACT;AAKA,IAAI,gCAAgC,SAASC,+BAA8B,OAAO,QAAQ,kBAAkB;AAC1G,MAAI,SAAS;AACb,MAAI,eAAe;AAEnB,MAAI,iBAAiB,SAASH,gBAAe,QAAQ,OAAO,uBAAuB,IAAI,SAAS,YAAY,qBAAqB;AAC/H,QAAI,yBAAyB,oBAAoB,wBAAwB,QAAQ,SAAS,kBAAkB;AAC1G,eAAS;AACT,qBAAe;AAAA,IACjB;AAAA,EACF;AAEA,wBAAsB,OAAO,QAAQ,cAAc;AAEnD,MAAI,cAAc;AAChB,WAAO;AAAA,EACT;AACF;AAEA,IAAI,cAAc,SAASI,aAAY,OAAO,QAAQ;AACpD,MAAI,WAAW,CAAC;AAChB,wBAAsB,OAAO,QAAQ,SAAU,OAAO,OAAO,gBAAgB,IAAI,SAAS,YAAY,OAAO;AAC3G,aAAS,KAAK;AAAA,MACZ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,CAAC;AAAA,EACH,CAAC;AACD,SAAO;AACT;AAEA,IAAI,sBAAsB,SAASC,qBAAoB,QAAQ,IAAI;AACjE,SAAO,GAAG,OAAO,QAAQ,GAAG,EAAE,OAAO,EAAE;AACzC;AAEA,IAAI,mBAAmB,SAASC,kBAAiB,aAAa;AAC5D,SAAO,OAAO,OAAO,WAAW,EAAE,OAAO,SAAU,KAAK,MAAM;AAC5D,QAAI,UAAU,KAAK;AACnB,WAAO,MAAM,QAAQ;AAAA,EACvB,GAAG,CAAC;AACN;AAEA,IAAI,sBAAsB,SAASC,qBAAoB,OAAO,QAAQ;AACpE,MAAI,WAAW,YAAY,OAAO,MAAM;AACxC,MAAI,cAAc,SAAS,SAAS,SAAS,CAAC;AAC9C,SAAO,cAAc,YAAY,iBAAiB,YAAY,QAAQ,SAAS;AACjF;AAEA,IAAI,gBAAgB,SAASC,eAAc,QAAQ;AACjD,MAAI,gBAAgB,YAAY,MAAM;AACtC,MAAI,mBAAmB,OAAO,OAAO,QAAQ,aAAa,OAAO,IAAI,aAAa,QAAQ,MAAM;AAChG,MAAI,cAAc,OAAO,OAAO,QAAQ,aAAa,EAAE,IAAI,aAAa,GAAG,MAAM;AACjF,SAAO,IAAI,OAAO,cAAc,QAAQ,aAAa,SAAS,MAAM,OAAO,YAAY,oBAAoB,EAAE,GAAG,MAAM,CAAC,EAAE,QAAQ,aAAa,IAAI,MAAM,OAAO,YAAY,eAAe,EAAE,GAAG,MAAM,CAAC,CAAC;AACzM;AAEA,IAAI,yBAAyB,SAASC,wBAAuB,UAAU;AACrE,SAAO,uBAAS,QAAQ,QAAQ,EAAE,IAAI,SAAU,MAAM;AACpD,QAAI,aAAa,KAAK,OAClB,SAAS,WAAW,QACpB,QAAQ,WAAW,OACnBb,oBAAmB,WAAW;AAClC,WAAO;AAAA,MACL;AAAA,MACA,OAAO,QAAQ,sBAAsB,OAAO,MAAM,IAAI,cAAc,MAAM;AAAA,MAC1E,kBAAkBA,qBAAoB,SAAU,IAAI,SAAS;AAC3D,eAAO,WAAW;AAAA,MACpB;AAAA,IACF;AAAA,EACF,CAAC;AACH;AAGA,IAAI,wBAAwB,SAASc,uBAAsB,OAAO,QAAQ;AACxE,MAAI,iBAAiB,IAAI,OAAO,MAAM,SAAS,IAAI,GAAG,EAAE,KAAK,EAAE,EAAE,SAAS;AAC1E,MAAI,uBAAuB,kBAAkB,MAAM;AACnD,wBAAApB,SAAU,mBAAmB,sBAAsB,wCAAwC,OAAO,MAAM,SAAS,GAAG,IAAI,EAAE,OAAO,gBAAgB,6DAA6D,EAAE,OAAO,QAAQ,KAAK,EAAE,OAAO,sBAAsB,GAAG,CAAC;AACvQ,SAAO;AACT;AAEA,IAAI,qBAAqB,SAASqB,oBAAmB,QAAQ,IAAI,SAAS;AACxE,SAAO,OAAO,QAAQ,aAAa,IAAI,EAAE,EAAE,QAAQ,aAAa,SAAS,OAAO;AAClF;AAIA,IAAI,oBAAoB,CAAC;AAAA,EACvB,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,GAAG;AAAA,EACD,MAAM;AAAA,EACN,SAAS;AACX,CAAC;AAED,IAAI,gBAAgB,SAASC,eAAc,KAAK;AAC9C,MAAI,eAAe;AACnB,oBAAkB,QAAQ,SAAU,kBAAkB;AACpD,mBAAe,aAAa,QAAQ,iBAAiB,SAAS,iBAAiB,IAAI;AAAA,EACrF,CAAC;AACD,SAAO;AACT;AAEA,IAAI,kBAAkB,SAASC,iBAAgB,KAAK;AAClD,SAAO,cAAc,GAAG,EAAE,YAAY;AACxC;AAEA,IAAI,oBAAoB,SAASC,mBAAkB,KAAK,QAAQ,eAAe;AAC7E,MAAI,CAAC,eAAe;AAClB,WAAO,IAAI,YAAY,EAAE,QAAQ,OAAO,YAAY,CAAC;AAAA,EACvD;AAEA,SAAO,gBAAgB,GAAG,EAAE,QAAQ,gBAAgB,MAAM,CAAC;AAC7D;AAEA,IAAI,OAAO,SAASC,QAAO;AACzB,SAAO,CAAC,CAAC,SAAS;AACpB;AAEA,IAAI,WAAW,SAASC,UAAS,KAAK;AACpC,SAAO,OAAO,QAAQ;AACxB;AAEA,IAAIC,QAAO,SAASA,MAAK,KAAK;AAC5B,SAAO,QAAQ,OAAO,GAAG,IAAI,OAAO,KAAK,GAAG,IAAI,CAAC;AACnD;AAEA,IAAIC,QAAO,SAASA,MAAK,KAAK;AAC5B,MAAI;AAEJ,WAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,SAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACjC;AAEA,MAAID,SAAQ,OAAO,CAAC,GAAG,OAAO,MAAM,MAAM,IAAI;AAE9C,SAAO,OAAO,KAAK,GAAG,EAAE,OAAO,SAAU,KAAK,GAAG;AAC/C,QAAI,IAAI,eAAe,CAAC,KAAK,CAACA,MAAK,SAAS,CAAC,KAAK,IAAI,CAAC,MAAM,QAAW;AACtE,UAAI,CAAC,IAAI,IAAI,CAAC;AAAA,IAChB;AAEA,WAAO;AAAA,EACT,GAAG,CAAC,CAAC;AACP;AAEA,IAAI,YAAY,CAAC,SAAS,aAAa,YAAY;AAEnD,SAASE,SAAQ,QAAQ,gBAAgB;AAAE,MAAIF,QAAO,OAAO,KAAK,MAAM;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,uBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IAAY,CAAC,IAAIA,MAAK,KAAK,MAAMA,OAAM,OAAO;AAAA,EAAG;AAAE,SAAOA;AAAM;AAEpV,SAASG,eAAc,QAAQ;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAID,SAAQ,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AAAE,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAIA,SAAQ,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAQ;AAEzf,SAAS,mBAAmBE,eAAc,cAAc;AACtD,MAAI,UAAU,SAASC,SAAQ,iBAAiB;AAC9C,QAAI,uBAAuB,SAASC,sBAAqB,MAAM;AAC7D,UAAI,QAAQ,KAAK,OACb,YAAY,KAAK,WACjB,aAAa,KAAK,YAClB,OAAO,yBAAyB,MAAM,SAAS;AAEnD,UAAI,YAAY,eAAe,aAAa,IAAI,IAAI;AACpD,UAAI,SAAS,YAAUF,eAAc;AAAA,QACnC;AAAA,QACA;AAAA,QACA;AAAA,MACF,GAAG,SAAS;AACZ,aAAoB,cAAAG,QAAM,cAAc,iBAAiB,SAAS,CAAC,GAAG,MAAM;AAAA,QAC1E,OAAO;AAAA,MACT,CAAC,CAAC;AAAA,IACJ;AAEA,QAAI,cAAc,gBAAgB,eAAe,gBAAgB,QAAQ;AACzE,yBAAqB,cAAc,gBAAgB,OAAO,aAAa,GAAG;AAE1E,WAAoB,cAAAA,QAAM,WAAW,SAAU,OAAO,KAAK;AACzD,aAAO,qBAAqBJ,eAAcA,eAAc,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;AAAA,QACtE;AAAA,MACF,CAAC,CAAC;AAAA,IACJ,CAAC;AAAA,EACH;AAEA,SAAO;AACT;AAEA,IAAI,wBAAwB,SAASK,uBAAsB,UAAU,IAAI;AACvE,MAAI,CAAC,SAAS,eAAe,EAAE,GAAG;AAChC,aAAS,EAAE,IAAI;AAAA,EACjB,OAAO;AACL,aAAS,EAAE;AAAA,EACb;AAEA,SAAO,KAAK,MAAM,SAAS,EAAE;AAC/B;AAEA,SAAS,YAAY,MAAM;AACzB,MAAI,iBAAiB,KAAK,gBACtB,eAAe,KAAK,cACpB,aAAa,KAAK,OAClB,QAAQ,eAAe,SAAS,KAAK,YACrC,wBAAwB,KAAK,uBAC7B,eAAe,KAAK,cACpB,WAAW,KAAK,UAChB,aAAa,KAAK,YAClB,QAAQ,KAAK;AAEjB,MAAI,gBAAY,wBAAS;AAAA,IACvB,MAAM;AAAA,IACN,KAAK;AAAA,EACP,CAAC,GACG,aAAa,eAAe,WAAW,CAAC,GACxC,WAAW,WAAW,CAAC,GACvB,cAAc,WAAW,CAAC;AAE9B,MAAI,iBAAa,wBAAS,GACtB,aAAa,eAAe,YAAY,CAAC,GACzC,eAAe,WAAW,CAAC,GAC3B,kBAAkB,WAAW,CAAC;AAElC,+BAAU,WAAY;AACpB,wBAAoB;AAAA,EACtB,CAAC;AAED,MAAI,sBAAsB,SAASC,uBAAsB;AACvD,QAAI,CAAC,cAAc;AACjB;AAAA,IACF;AAEA,QAAI,aAAa,aAAa,YAC1B,YAAY,aAAa;AAE7B,QAAI,SAAS,SAAS,cAAc,SAAS,QAAQ,WAAW;AAC9D;AAAA,IACF;AAEA,QAAI,cAAc;AAAA,MAChB,MAAM;AAAA,MACN,KAAK;AAAA,IACP;AACA,gBAAY,WAAW;AACvB,0BAAsB,WAAW;AAAA,EACnC;AAEA,MAAI,SAAS,uBAAuB,QAAQ;AAC5C,MAAI;AAEJ,MAAI,iBAAiB,gBAAgB;AACnC,4BAAwB,kBAAkB,OAAO,QAAQ,gBAAgB,OAAO;AAAA,EAClF;AAEA,MAAI,mBAAmB,CAAC;AACxB,MAAI,gBAAgB,CAAC;AACrB,MAAI,aAAa;AACjB,MAAI,wBAAwB;AAE5B,MAAI,eAAe,SAAS3B,cAAa,QAAQ,OAAO,kBAAkB;AAExE,QAAI,SAAS,qBAAqB,KAAK,yBAAyB,SAAS,yBAAyB,QAAQ,OAAO,QAAQ;AAEvH,UAAI,aAAa,wBAAwB;AACzC,iBAAW,KAAK,gBAAgB,OAAO,UAAU,GAAG,UAAU,GAAG,qBAAqB,CAAC;AAEvF,mBAAa,CAAC,gBAAgB,OAAO,UAAU,UAAU,GAAG,qBAAqB,CAAC;AAAA,IACpF,OAAO;AACL,iBAAW,KAAK,gBAAgB,QAAQ,qBAAqB,CAAC;AAAA,IAChE;AAEA;AAAA,EACF;AAEA,MAAI,kBAAkB,SAAS4B,iBAAgB,QAAQ,OAAO,kBAAkB,IAAI,SAAS,mBAAmB,qBAAqB;AACnI,QAAI,MAAM,sBAAsB,eAAe,EAAE;AAEjD,eAAW,KAAK,4BAA4B,IAAI,SAAS,mBAAmB,GAAG,CAAC;AAAA,EAClF;AAEA,MAAI,kBAAkB,SAASC,iBAAgB,QAAQ,KAAK;AAE1D,WAAoB,cAAAJ,QAAM,cAAc,QAAQ,SAAS,CAAC,GAAG,MAAM,WAAW,GAAG;AAAA,MAC/E;AAAA,IACF,CAAC,GAAG,MAAM;AAAA,EACZ;AAEA,MAAI,8BAA8B,SAASK,6BAA4B,IAAI,SAAS,mBAAmB,KAAK;AAC1G,QAAI,QAAQ;AAAA,MACV;AAAA,MACA;AAAA,MACA;AAAA,IACF;AACA,QAAI,QAAQ,uBAAS,QAAQ,QAAQ,EAAE,iBAAiB;AACxD,WAAoB,cAAAL,QAAM,aAAa,OAAO,KAAK;AAAA,EACrD;AAEA,MAAI,yBAAyB,SAASM,wBAAuBC,WAAU;AACrE,WAAoB,cAAAP,QAAM,cAAc,QAAQ,SAAS,CAAC,GAAG,MAAM,OAAO,GAAG;AAAA,MAC3E,KAAK;AAAA,MACL,KAAK;AAAA,IACP,CAAC,GAAGO,SAAQ;AAAA,EACd;AAEA,wBAAsB,OAAO,QAAQ,iBAAiB,YAAY;AAElE,aAAW,KAAK,GAAG;AAEnB,MAAI,eAAe,kBAAkB;AAEnC,qBAAiB,KAAK,uBAAuB,UAAU,CAAC;AAAA,EAC1D;AAEA,SAAoB,cAAAP,QAAM,cAAc,OAAO,SAAS,CAAC,GAAG,OAAO;AAAA,IACjE,KAAK;AAAA,EACP,CAAC,GAAG,gBAAgB;AACtB;AAEA,YAAY,YAAY;AAAA,EACtB,gBAAgB,kBAAAQ,QAAU;AAAA,EAC1B,cAAc,kBAAAA,QAAU;AAAA,EACxB,OAAO,kBAAAA,QAAU,OAAO;AAAA,EACxB,uBAAuB,kBAAAA,QAAU,KAAK;AAAA,EACtC,cAAc,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM;AAAA,IACjE,SAAS,OAAO,YAAY,cAAc,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,WAAW,OAAO;AAAA,EACxF,CAAC,CAAC,CAAC;AAAA,EACH,UAAU,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,SAAS,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,OAAO,CAAC,CAAC,EAAE;AAC3F;AACA,IAAI,SAAS,mBAAmB;AAAA,EAC9B,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,EACP,OAAO;AAAA,EACP,UAAU;AAAA,EACV,YAAY;AAAA,EACZ,UAAU;AAAA,EACV,QAAQ;AAAA,EACR,WAAW;AAAA,EACX,eAAe;AAAA,IACb,YAAY;AAAA,IACZ,UAAU;AAAA,EACZ;AAAA,EACA,WAAW;AAAA,IACT,YAAY;AAAA,EACd;AACF,GAAG,SAAU,OAAO;AAClB,SAAO;AAAA,IACL,eAAe,MAAM;AAAA,EACvB;AACF,CAAC;AACD,IAAI,gBAAgB,OAAO,WAAW;AAEtC,SAAS,WAAW,MAAM;AACxB,MAAI,KAAK,KAAK,IACV,UAAU,KAAK,SACf,gBAAgB,KAAK,eACrB,QAAQ,KAAK,OACb,UAAU,KAAK,SACf,eAAe,KAAK,cACpB,QAAQ,KAAK,OACb,mBAAmB,KAAK,kBACxB,aAAa,KAAK,YAClB,QAAQ,KAAK,OACb,YAAY,KAAK,WACjB,aAAa,KAAK;AACtB,MAAI,OAAO;AAAA,IACT;AAAA,IACA;AAAA,EACF;AAEA,MAAI,gBAAgB,SAASC,iBAAgB;AAC3C,QAAI,UAAU,WAAW;AACzB,QAAI,qBAAqB,yBAAyB,OAAO;AAEzD,QAAI,kBAAkB;AACpB,aAAO,iBAAiB,YAAY,OAAO,oBAAoB,OAAO,OAAO;AAAA,IAC/E;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,aAAa,SAASC,cAAa;AACrC,QAAI,OAAO,eAAe,UAAU;AAClC,aAAO;AAAA,IACT;AAEA,QAAIC,MAAK,WAAW,IAChB,UAAU,WAAW;AAEzB,QAAIA,QAAO,UAAa,CAAC,SAAS;AAChC,aAAOA;AAAA,IACT;AAEA,WAAO;AAAA,EACT;AAEA,MAAI,2BAA2B,SAASC,0BAAyB,SAAS;AACxE,QAAI,IAAI,kBAAkB,SAAS,OAAO,aAAa;AAEvD,QAAI,MAAM,IAAI;AACZ,aAAoB,cAAAZ,QAAM,cAAc,QAAQ,MAAM,SAAS,GAAG,OAAO;AAAA,IAC3E;AAEA,WAAoB,cAAAA,QAAM,cAAc,QAAQ,MAAM,SAAS,GAAG,QAAQ,UAAU,GAAG,CAAC,GAAgB,cAAAA,QAAM,cAAc,KAAK,MAAM,WAAW,GAAG,QAAQ,UAAU,GAAG,IAAI,MAAM,MAAM,CAAC,GAAG,QAAQ,UAAU,IAAI,MAAM,MAAM,CAAC;AAAA,EACnO;AAEA,SAAoB,cAAAA,QAAM,cAAc,MAAM,SAAS;AAAA,IACrD;AAAA,IACA,MAAM;AAAA,IACN,iBAAiB;AAAA,EACnB,GAAG,MAAM,KAAK,GAAG,cAAc,CAAC;AAClC;AAEA,WAAW,YAAY;AAAA,EACrB,IAAI,kBAAAQ,QAAU,OAAO;AAAA,EACrB,OAAO,kBAAAA,QAAU,OAAO;AAAA,EACxB,OAAO,kBAAAA,QAAU,OAAO;AAAA,EACxB,eAAe,kBAAAA,QAAU;AAAA,EACzB,YAAY,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM;AAAA,IACjE,IAAI,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,CAAC,EAAE;AAAA,IAC9D,SAAS,kBAAAA,QAAU;AAAA,EACrB,CAAC,CAAC,CAAC,EAAE;AAAA,EACL,kBAAkB,kBAAAA,QAAU;AAAA,EAC5B,SAAS,kBAAAA,QAAU;AACrB;AACA,IAAI,WAAW,mBAAmB;AAAA,EAChC,QAAQ;AACV,GAAG,SAAU,OAAO;AAClB,SAAO;AAAA,IACL,YAAY,MAAM;AAAA,EACpB;AACF,CAAC;AACD,IAAI,eAAe,SAAS,UAAU;AAEtC,SAAS,iBAAiB,MAAM;AAC9B,MAAI,QAAQ,KAAK,OACb,YAAY,KAAK,WACjB,aAAa,KAAK;AACtB,MAAI,SAAS,YAAU,cAAc;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,gBAAgB,OAAO,SAAS;AACpC,SAAoB,cAAAR,QAAM,cAAc,OAAO,QAAqB,cAAAA,QAAM,cAAc,OAAO,eAA4B,cAAAA,QAAM,cAAc,OAAO,cAAc,CAAC,WAAW,UAAU,CAAC,CAAC,GAAgB,cAAAA,QAAM,cAAc,OAAO,cAAc,CAAC,WAAW,UAAU,CAAC,CAAC,GAAgB,cAAAA,QAAM,cAAc,OAAO,cAAc,CAAC,WAAW,UAAU,CAAC,CAAC,GAAgB,cAAAA,QAAM,cAAc,OAAO,cAAc,CAAC,WAAW,UAAU,CAAC,CAAC,GAAgB,cAAAA,QAAM,cAAc,OAAO,cAAc,CAAC,WAAW,UAAU,CAAC,CAAC,CAAC,CAAC;AACpgB;AAEA,IAAI,eAAe,CAAC;AAEpB,SAAS,mBAAmB,MAAM;AAChC,MAAI,KAAK,KAAK,IACV,mBAAmB,KAAK,aACxB,cAAc,qBAAqB,SAAS,CAAC,IAAI,kBACjD,2BAA2B,KAAK,0BAChC,aAAa,KAAK,YAClB,WAAW,KAAK,UAChB,OAAO,KAAK,MACZ,QAAQ,KAAK,OACb,MAAM,KAAK,KACX,wBAAwB,KAAK,uBAC7B,YAAY,KAAK,WACjB,WAAW,KAAK,UAChB,gBAAgB,KAAK,UACrBa,YAAW,kBAAkB,SAAS,WAAY;AACpD,WAAO;AAAA,EACT,IAAI,eACA,gBAAgB,KAAK,eACrB,eAAe,KAAK,cACpB,WAAW,KAAK,UAChB,QAAQ,KAAK,OACb,6BAA6B,KAAK,4BAClC,cAAc,KAAK,aACnB,eAAe,KAAK;AAExB,MAAI,gBAAY,wBAAS,MAAS,GAC9B,aAAa,eAAe,WAAW,CAAC,GACxC,YAAY,WAAW,CAAC,GACxB,eAAe,WAAW,CAAC;AAE/B,+BAAU,WAAY;AACpB,QAAI,CAAC,aAAa,UAAU,gBAAgB,UAAU,gBAAgB,CAAC,uBAAuB;AAC5F;AAAA,IACF;AAEA,QAAI,YAAY,UAAU;AAE1B,QAAI,wBAAwB,UAAU,SAAS,UAAU,EAAE,sBAAsB,GAC7EC,OAAM,sBAAsB,KAC5B,SAAS,sBAAsB;AAEnC,QAAI,wBAAwB,UAAU,sBAAsB,GACxD,eAAe,sBAAsB;AAEzC,IAAAA,OAAMA,OAAM,eAAe;AAC3B,aAAS,SAAS,eAAe;AAEjC,QAAIA,OAAM,WAAW;AACnB,gBAAU,YAAYA;AAAA,IACxB,WAAW,SAAS,UAAU,cAAc;AAC1C,gBAAU,YAAY,SAAS,UAAU;AAAA,IAC3C;AAAA,EACF,GAAG,CAAC,YAAY,uBAAuB,SAAS,CAAC;AAEjD,MAAI,oBAAoB,SAASC,qBAAoB;AACnD,QAAI,sBAAmC,cAAAf,QAAM,cAAc,MAAM,SAAS;AAAA,MACxE,KAAK;AAAA,MACL;AAAA,MACA,MAAM;AAAA,MACN,cAAc;AAAA,IAChB,GAAG,MAAM,MAAM,CAAC,GAAG,OAAO,OAAO,WAAW,EAAE,OAAO,SAAU,YAAY,OAAO;AAChF,UAAI,UAAU,MAAM,SAChB,YAAY,MAAM;AACtB,aAAO,CAAC,EAAE,OAAO,mBAAmB,UAAU,GAAG,mBAAmB,QAAQ,IAAI,SAAU,QAAQ,OAAO;AACvG,eAAO,iBAAiB,QAAQ,WAAW,WAAW,SAAS,KAAK;AAAA,MACtE,CAAC,CAAC,CAAC;AAAA,IACL,GAAG,CAAC,CAAC,CAAC;AACN,QAAI,2BAA4B,QAAO,2BAA2B,mBAAmB;AACrF,WAAO;AAAA,EACT;AAEA,MAAI,mBAAmB,SAASgB,kBAAiB,QAAQ,WAAW,OAAO;AACzE,QAAI,YAAY,UAAU;AAC1B,QAAI,aAAa,UAAU,YACvB,QAAQ,UAAU;AACtB,QAAIA,oBAAmB,uBAAS,QAAQ,QAAQ,EAAE,UAAU,EAAE,MAAM;AACpE,WAAoB,cAAAhB,QAAM,cAAc,cAAc;AAAA,MACpD,OAAO,MAAM,MAAM;AAAA,MACnB,KAAK,GAAG,OAAO,YAAY,GAAG,EAAE,OAAO,MAAM,MAAM,CAAC;AAAA,MACpD,IAAI,oBAAoB,IAAI,KAAK;AAAA,MACjC;AAAA,MACA;AAAA,MACA;AAAA,MACA,kBAAkBgB;AAAA,MAClB,YAAY;AAAA,MACZ,SAAS;AAAA,MACT,SAAS,SAAS,UAAU;AAC1B,eAAO,OAAO,QAAQ,SAAS;AAAA,MACjC;AAAA,MACA,cAAc,SAASC,gBAAe;AACpC,eAAO,iBAAiB,KAAK;AAAA,MAC/B;AAAA,IACF,CAAC;AAAA,EACH;AAEA,MAAI,yBAAyB,SAASC,0BAAyB;AAC7D,QAAI,CAAC,WAAW;AACd;AAAA,IACF;AAEA,WAAoB,cAAAlB,QAAM,cAAc,kBAAkB;AAAA,MACxD,OAAO,MAAM,kBAAkB;AAAA,IACjC,CAAC;AAAA,EACH;AAEA,MAAI,mBAAmB,SAASmB,kBAAiB,OAAO,IAAI;AAC1D,QAAI,cAAc;AAChB,mBAAa,KAAK;AAAA,IACpB;AAAA,EACF;AAEA,MAAI,SAAS,SAASC,QAAO,YAAY,WAAW;AAClD,IAAAP,UAAS,YAAY,SAAS;AAAA,EAChC;AAEA,MAAI,QAAQ,SAASQ,OAAM,YAAY;AACrC,QAAI,OAAO,eAAe,UAAU;AAClC,aAAO;AAAA,IACT;AAEA,WAAO,WAAW;AAAA,EACpB;AAEA,MAAI,CAAC,UAAU;AACb,WAAO;AAAA,EACT;AAEA,SAAoB,cAAArB,QAAM,cAAc,OAAO,SAAS,CAAC,GAAG,eAAO;AAAA,IACjE,UAAU,YAAY;AAAA,IACtB;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG,KAAK,GAAG;AAAA,IACT;AAAA,IACA,KAAK;AAAA,EACP,CAAC,GAAG,kBAAkB,GAAG,uBAAuB,CAAC;AACnD;AAEA,mBAAmB,YAAY;AAAA,EAC7B,IAAI,kBAAAQ,QAAU,OAAO;AAAA,EACrB,aAAa,kBAAAA,QAAU,OAAO;AAAA,EAC9B,0BAA0B,kBAAAA,QAAU;AAAA,EACpC,YAAY,kBAAAA,QAAU;AAAA,EACtB,UAAU,kBAAAA,QAAU;AAAA,EACpB,MAAM,kBAAAA,QAAU;AAAA,EAChB,OAAO,kBAAAA,QAAU;AAAA,EACjB,KAAK,kBAAAA,QAAU;AAAA,EACf,uBAAuB,kBAAAA,QAAU;AAAA,EACjC,WAAW,kBAAAA,QAAU;AAAA,EACrB,UAAU,kBAAAA,QAAU,KAAK;AAAA,EACzB,UAAU,kBAAAA,QAAU;AAAA,EACpB,eAAe,kBAAAA,QAAU;AAAA,EACzB,4BAA4B,kBAAAA,QAAU;AAAA,EACtC,cAAc,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM;AAAA,IACjE,SAAS,OAAO,YAAY,cAAc,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,WAAW,OAAO;AAAA,EACxF,CAAC,CAAC,CAAC;AACL;AACA,IAAI,WAAW,mBAAmB;AAAA,EAChC,QAAQ;AAAA,EACR,iBAAiB;AAAA,EACjB,WAAW;AAAA,EACX,UAAU;AAAA,EACV,MAAM;AAAA,IACJ,QAAQ;AAAA,IACR,SAAS;AAAA,IACT,eAAe;AAAA,EACjB;AACF,CAAC;AACD,IAAI,uBAAuB,SAAS,kBAAkB;AAEtD,SAAS,UAAU,QAAQ,gBAAgB;AAAE,MAAIf,QAAO,OAAO,KAAK,MAAM;AAAG,MAAI,OAAO,uBAAuB;AAAE,QAAI,UAAU,OAAO,sBAAsB,MAAM;AAAG,uBAAmB,UAAU,QAAQ,OAAO,SAAU,KAAK;AAAE,aAAO,OAAO,yBAAyB,QAAQ,GAAG,EAAE;AAAA,IAAY,CAAC,IAAIA,MAAK,KAAK,MAAMA,OAAM,OAAO;AAAA,EAAG;AAAE,SAAOA;AAAM;AAEtV,SAAS,gBAAgB,QAAQ;AAAE,WAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,QAAI,SAAS,QAAQ,UAAU,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC;AAAG,QAAI,IAAI,UAAU,OAAO,MAAM,GAAG,IAAE,EAAE,QAAQ,SAAU,KAAK;AAAE,sBAAgB,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,IAAG,CAAC,IAAI,OAAO,4BAA4B,OAAO,iBAAiB,QAAQ,OAAO,0BAA0B,MAAM,CAAC,IAAI,UAAU,OAAO,MAAM,CAAC,EAAE,QAAQ,SAAU,KAAK;AAAE,aAAO,eAAe,QAAQ,KAAK,OAAO,yBAAyB,QAAQ,GAAG,CAAC;AAAA,IAAG,CAAC;AAAA,EAAG;AAAE,SAAO;AAAQ;AAE/f,SAAS,aAAa,SAAS;AAAE,MAAI,4BAA4B,0BAA0B;AAAG,SAAO,SAAS,uBAAuB;AAAE,QAAI,QAAQ,gBAAgB,OAAO,GAAG;AAAQ,QAAI,2BAA2B;AAAE,UAAI,YAAY,gBAAgB,IAAI,EAAE;AAAa,eAAS,QAAQ,UAAU,OAAO,WAAW,SAAS;AAAA,IAAG,OAAO;AAAE,eAAS,MAAM,MAAM,MAAM,SAAS;AAAA,IAAG;AAAE,WAAO,2BAA2B,MAAM,MAAM;AAAA,EAAG;AAAG;AAExa,SAAS,4BAA4B;AAAE,MAAI,OAAO,YAAY,eAAe,CAAC,QAAQ,UAAW,QAAO;AAAO,MAAI,QAAQ,UAAU,KAAM,QAAO;AAAO,MAAI,OAAO,UAAU,WAAY,QAAO;AAAM,MAAI;AAAE,YAAQ,UAAU,QAAQ,KAAK,QAAQ,UAAU,SAAS,CAAC,GAAG,WAAY;AAAA,IAAC,CAAC,CAAC;AAAG,WAAO;AAAA,EAAM,SAAS,GAAG;AAAE,WAAO;AAAA,EAAO;AAAE;AACxU,IAAI,mBAAmB,SAAS6B,kBAAiB,SAAS;AACxD,MAAI,UAAU,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI,CAAC;AAEnF,MAAI,mBAAmB,QAAQ;AAC7B,WAAO;AAAA,EACT,OAAO;AACL,QAAI,oBAAoB,QAAQ;AAChC,QAAI,qBAAqB,YAAY,OAAO;AAG5C,WAAO,IAAI,OAAO,aAAa,OAAO,oBAAoB,KAAK,EAAE,OAAO,oBAAoB,KAAK,KAAK,EAAE,OAAO,oBAAoB,OAAO,CAAC;AAAA,EAC7I;AACF;AAEA,IAAI,kBAAkB,SAASC,iBAAgB,MAAM,eAAe;AAClE,MAAI,gBAAgB,OAAO;AAEzB,WAAO,SAAU,OAAO,UAAU;AAChC,UAAI,UAAU,CAAC;AAEf,eAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,IAAI,GAAG,EAAE,GAAG;AAC3C,YAAI,UAAU,KAAK,CAAC,EAAE,WAAW,KAAK,CAAC,EAAE;AAEzC,YAAI,kBAAkB,SAAS,OAAO,aAAa,KAAK,GAAG;AACzD,kBAAQ,KAAK,KAAK,CAAC,CAAC;AAAA,QACtB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AAEL,WAAO;AAAA,EACT;AACF;AAEA,IAAI,MAAM;AAAA,EACR,KAAK;AAAA,EACL,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,IAAI;AAAA,EACJ,MAAM;AACR;AACA,IAAI,cAAc;AAClB,IAAI,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKd,YAAY,kBAAAf,QAAU;AAAA,EACtB,mBAAmB,kBAAAA,QAAU;AAAA,EAC7B,6BAA6B,kBAAAA,QAAU;AAAA,EACvC,6BAA6B,kBAAAA,QAAU;AAAA,EACvC,eAAe,kBAAAA,QAAU;AAAA,EACzB,0BAA0B,kBAAAA,QAAU;AAAA,EACpC,OAAO,kBAAAA,QAAU;AAAA,EACjB,WAAW,kBAAAA,QAAU;AAAA,EACrB,4BAA4B,kBAAAA,QAAU;AAAA,EACtC,UAAU,kBAAAA,QAAU;AAAA,EACpB,QAAQ,kBAAAA,QAAU;AAAA,EAClB,UAAU,kBAAAA,QAAU;AAAA,EACpB,uBAAuB,OAAO,YAAY,cAAc,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,UAAU,WAAW,OAAO;AAAA,EAC9G,UAAU,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM;AAAA,IAC7D,SAAS,OAAO,YAAY,cAAc,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,WAAW,OAAO;AAAA,EACxF,CAAC,CAAC,CAAC;AAAA,EACH,UAAU,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,SAAS,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,OAAO,CAAC,CAAC,EAAE;AAC3F;AAEA,IAAI,gBAA6B,SAAU,kBAAkB;AAC3D,YAAUgB,gBAAe,gBAAgB;AAEzC,MAAI,SAAS,aAAaA,cAAa;AAEvC,WAASA,eAAc,QAAQ;AAC7B,QAAI;AAEJ,oBAAgB,MAAMA,cAAa;AAEnC,YAAQ,OAAO,KAAK,MAAM,MAAM;AAEhC,oBAAgB,uBAAuB,KAAK,GAAG,uBAAuB,SAAU,IAAI;AAClF,YAAM,mBAAmB;AAAA,IAC3B,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,WAAY;AAC1E,UAAI,cAAc,MAAM,OACpB,WAAW,YAAY,UACvB,WAAW,YAAY,UACvB,QAAQ,YAAY;AAExB,UAAI,QAAQ9B;AAAA,QAAK,MAAM;AAAA,QAAO,CAAC,SAAS,cAAc,WAAW;AAAA;AAAA,QACjED,MAAK,SAAS;AAAA,MAAC;AACf,aAAO,gBAAgB,gBAAgB,gBAAgB,gBAAgB,CAAC,GAAG,KAAK,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,GAAG;AAAA,QACtG,OAAO,MAAM,aAAa;AAAA,QAC1B,UAAU,MAAM;AAAA,MAClB,GAAG,CAAC,YAAY,CAAC,YAAY;AAAA,QAC3B,UAAU,MAAM;AAAA,QAChB,UAAU,MAAM;AAAA,QAChB,WAAW,MAAM;AAAA,QACjB,QAAQ,MAAM;AAAA,QACd,oBAAoB,MAAM;AAAA,QAC1B,kBAAkB,MAAM;AAAA,MAC1B,CAAC,GAAG,MAAM,SAAS,KAAK;AAAA,QACtB,MAAM;AAAA,QACN,iBAAiB,MAAM;AAAA,QACvB,iBAAiB;AAAA,QACjB,qBAAqB;AAAA,QACrB,iBAAiB;AAAA,QACjB,yBAAyB,oBAAoB,MAAM,wBAAwB,MAAM,MAAM,UAAU;AAAA,MACnG,CAAC;AAAA,IACH,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,WAAY;AAC1E,UAAI,eAAe,MAAM,OACrB,aAAa,aAAa,YAC1B,QAAQ,aAAa;AAEzB,UAAI,aAAa,MAAM,cAAc;AAErC,aAAoB,cAAAO,QAAM,cAAc,OAAO,MAAM,SAAS,GAAG,MAAM,kBAAkB,GAAG,aAAa,MAAM,YAAY,UAAU,IAAI,MAAM,eAAe,UAAU,CAAC;AAAA,IAC3K,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,eAAe,SAAU,OAAO;AAC7E,aAAoB,cAAAA,QAAM,cAAc,SAAS,SAAS;AAAA,QACxD,MAAM;AAAA,QACN,KAAK,MAAM;AAAA,MACb,GAAG,KAAK,CAAC;AAAA,IACX,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,kBAAkB,SAAU,OAAO;AAChF,aAAoB,cAAAA,QAAM,cAAc,YAAY,SAAS;AAAA,QAC3D,KAAK,MAAM;AAAA,MACb,GAAG,KAAK,CAAC;AAAA,IACX,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,eAAe,SAAU,IAAI;AAC1E,YAAM,eAAe;AACrB,UAAI,WAAW,MAAM,MAAM;AAE3B,UAAI,OAAO,aAAa,YAAY;AAClC,iBAAS,EAAE;AAAA,MACb,WAAW,UAAU;AACnB,iBAAS,UAAU;AAAA,MACrB;AAAA,IACF,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,yBAAyB,SAAU,IAAI;AACpF,YAAM,qBAAqB;AAAA,IAC7B,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,4BAA4B,WAAY;AACrF,UAAI,CAAC,SAAS,MAAM,MAAM,cAAc,GAAG;AAEzC,eAAO;AAAA,MACT;AAEA,UAAI,wBAAwB,MAAM,MAAM,qBACpC,WAAW,sBAAsB,UACjC,OAAO,sBAAsB,MAC7B,MAAM,sBAAsB,KAC5B,QAAQ,sBAAsB;AAClC,UAAI,kBAA+B,cAAAA,QAAM,cAAc,sBAAsB;AAAA,QAC3E,IAAI,MAAM;AAAA,QACV,OAAO,MAAM,MAAM,MAAM,aAAa;AAAA,QACtC;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAY,MAAM,MAAM;AAAA,QACxB,uBAAuB,MAAM,MAAM;AAAA,QACnC,cAAc,MAAM;AAAA,QACpB,aAAa,MAAM,MAAM;AAAA,QACzB,4BAA4B,MAAM,MAAM;AAAA,QACxC,UAAU,MAAM;AAAA,QAChB,aAAa,MAAM;AAAA,QACnB,cAAc,MAAM;AAAA,QACpB,WAAW,MAAM,UAAU;AAAA,QAC3B,UAAU,MAAM,SAAS;AAAA,QACzB,eAAe,MAAM,MAAM;AAAA,QAC3B,0BAA0B,MAAM,MAAM;AAAA,MACxC,GAAG,MAAM,MAAM,QAAQ;AAEvB,UAAI,MAAM,MAAM,uBAAuB;AACrC,eAAoB,iBAAAyB,QAAS,aAAa,iBAAiB,MAAM,MAAM,qBAAqB;AAAA,MAC9F,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,qBAAqB,WAAY;AAC9E,UAAI,cAAc,MAAM,OACpB,iBAAiB,YAAY,gBAC7B,eAAe,YAAY;AAC/B,UAAI,eAAe,MAAM,OACrB,aAAa,aAAa,YAC1B,WAAW,aAAa,UACxB,QAAQ,aAAa,OACrB,QAAQ,aAAa;AACzB,aAAoB,cAAAzB,QAAM,cAAc,eAAe;AAAA,QACrD,cAAc,MAAM;AAAA,QACpB,OAAO,MAAM,aAAa;AAAA,QAC1B;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,uBAAuB,MAAM;AAAA,MAC/B,GAAG,QAAQ;AAAA,IACb,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,yBAAyB,SAAU,IAAI;AACpF,YAAM,qBAAqB;AAAA,IAC7B,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,6BAA6B,SAAU,UAAU;AAC9F,YAAM,SAAS;AAAA,QACb,eAAe;AAAA,MACjB,CAAC;AAAA,IACH,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,gBAAgB,WAAY;AACzE,aAAO,aAAa,MAAM,MAAM,SAAS,IAAI,uBAAuB,MAAM,MAAM,QAAQ,CAAC;AAAA,IAC3F,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,mBAAmB,SAAU,OAAO;AACjF,eAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC1G,aAAK,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,MACjC;AAEA,UAAI,MAAM,MAAM,UAAU;AACxB,YAAI;AAEJ,gBAAQ,eAAe,MAAM,OAAO,SAAS,MAAM,cAAc,CAAC,KAAK,EAAE,OAAO,IAAI,CAAC;AAAA,MACvF;AAEA,UAAI,MAAM,MAAM,WAAW;AACzB,YAAI;AAEJ,gBAAQ,wBAAwB,MAAM,MAAM,WAAW,cAAc,MAAM,uBAAuB,CAAC,MAAM,OAAO,KAAK,EAAE,OAAO,IAAI,CAAC;AAAA,MACrI;AAAA,IACF,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,gBAAgB,SAAU,IAAI;AAC3E,oBAAc;AAEd,UAAI,KAAK,GAAG;AAEV,YAAI,kBAAkB,SAAS,iBAAiB,SAAS,cAAc,mBAAmB;AAE1F,YAAI,gBAAgB,kBAAkB,GAAG,QAAQ;AAE/C;AAAA,QACF;AAAA,MACF;AAEA,UAAI,QAAQ,MAAM,MAAM,SAAS;AACjC,UAAI,SAAS,uBAAuB,MAAM,MAAM,QAAQ;AACxD,UAAI,oBAAoB,GAAG,OAAO;AAClC,UAAI,uBAAuB,MAAM,MAAM;AAEvC,UAAI,wBAAwB,MAAM;AAChC,+BAAuB,GAAG,OAAO;AAAA,MACnC;AAEA,UAAI,qBAAqB,MAAM,MAAM;AAErC,UAAI,sBAAsB,MAAM;AAC9B,6BAAqB,GAAG,OAAO;AAAA,MACjC;AAGA,UAAI,WAAW,mBAAmB,OAAO,mBAAmB;AAAA,QAC1D;AAAA,QACA;AAAA,QACA,mBAAmB,GAAG,OAAO;AAAA,MAC/B,GAAG,MAAM;AAET,0BAAoB,aAAa,UAAU,MAAM;AAEjD,UAAI,iBAAiB,GAAG,OAAO;AAC/B,UAAI,eAAe,GAAG,OAAO;AAC7B,UAAI,iCAAiC;AAGrC,UAAI,iBAAiB,8BAA8B,OAAO,QAAQ,cAAc;AAEhF,UAAI,mBAAmB,UAAa,MAAM,MAAM,eAAe,gBAAgB;AAE7E,yBAAiB,kBAAkB,GAAG,YAAY,OAAO,GAAG,YAAY,KAAK,SAAS;AACtF,uBAAe;AACf,yCAAiC;AAAA,MACnC;AAEA,YAAM,SAAS;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AAED,UAAI,WAAW,YAAY,UAAU,MAAM;AAE3C,UAAI,GAAG,YAAY,eAAe,mBAAmB,cAAc;AACjE,cAAM,sBAAsB,MAAM,aAAa,OAAO,cAAc;AAAA,MACtE;AAIA,UAAI,YAAY;AAAA,QACd,QAAQ;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AAEA,YAAM,gBAAgB,WAAW,UAAU,mBAAmB,QAAQ;AAAA,IACxE,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,gBAAgB,SAAU,IAAI;AAE3E,YAAM,SAAS;AAAA,QACb,gBAAgB,GAAG,OAAO;AAAA,QAC1B,cAAc,GAAG,OAAO;AAAA,MAC1B,CAAC;AAGD,UAAI,YAAa;AAEjB,UAAI,KAAK,MAAM;AAEf,UAAI,GAAG,OAAO,mBAAmB,GAAG,OAAO,cAAc;AACvD,cAAM,sBAAsB,GAAG,OAAO,GAAG,OAAO,cAAc;AAAA,MAChE,OAAO;AACL,cAAM,iBAAiB;AAAA,MACzB;AAGA,YAAM,wBAAwB;AAE9B,YAAM,MAAM,SAAS,EAAE;AAAA,IACzB,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,SAAU,IAAI;AAE5E,UAAI,mBAAmB,iBAAiB,MAAM,MAAM,WAAW;AAE/D,UAAI,qBAAqB,KAAK,CAAC,MAAM,oBAAoB;AACvD,cAAM,MAAM,UAAU,EAAE;AAExB;AAAA,MACF;AAEA,UAAI,OAAO,OAAO,GAAG,EAAE,QAAQ,GAAG,OAAO,KAAK,GAAG;AAC/C,WAAG,eAAe;AAClB,WAAG,gBAAgB;AAAA,MACrB;AAEA,cAAQ,GAAG,SAAS;AAAA,QAClB,KAAK,IAAI,KACP;AACE,gBAAM,iBAAiB;AAEvB;AAAA,QACF;AAAA,QAEF,KAAK,IAAI,MACP;AACE,gBAAM,WAAW,CAAE;AAEnB;AAAA,QACF;AAAA,QAEF,KAAK,IAAI,IACP;AACE,gBAAM,WAAW,EAAE;AAEnB;AAAA,QACF;AAAA,QAEF,KAAK,IAAI,QACP;AACE,gBAAM,cAAc;AAEpB;AAAA,QACF;AAAA,QAEF,KAAK,IAAI,KACP;AACE,gBAAM,cAAc;AAEpB;AAAA,QACF;AAAA,QAEF,SACE;AACE;AAAA,QACF;AAAA,MACJ;AAAA,IACF,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,cAAc,SAAU,OAAO;AAC5E,UAAI,mBAAmB,iBAAiB,MAAM,MAAM,WAAW;AAE/D,YAAM,SAAS;AAAA,QACb,aAAa,mBAAmB,MAAM,MAAM,aAAa,SAAS;AAAA,QAClE,uBAAuB;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,iBAAiB,WAAY;AAC1E,UAAI,eAAe,MAAM,OACrB,cAAc,aAAa,aAC3B,aAAa,aAAa;AAC9B,UAAI,wBAAwB,OAAO,OAAO,WAAW,EAAE,OAAO,SAAU,KAAK,MAAM;AACjF,YAAI,UAAU,KAAK,SACf0B,aAAY,KAAK;AACrB,eAAO,CAAC,EAAE,OAAO,mBAAmB,GAAG,GAAG,mBAAmB,QAAQ,IAAI,SAAUC,SAAQ;AACzF,iBAAO;AAAA,YACL,QAAQA;AAAA,YACR,WAAWD;AAAA,UACb;AAAA,QACF,CAAC,CAAC,CAAC;AAAA,MACL,GAAG,CAAC,CAAC,EAAE,UAAU,GACb,SAAS,sBAAsB,QAC/B,YAAY,sBAAsB;AAEtC,YAAM,WAAW,QAAQ,SAAS;AAElC,YAAM,SAAS;AAAA,QACb,YAAY;AAAA,MACd,CAAC;AAAA,IACH,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,cAAc,SAAU,IAAI;AACzE,UAAI,oBAAoB,MAAM;AAC9B,YAAM,wBAAwB;AAG9B,UAAI,CAAC,mBAAmB;AACtB,cAAM,SAAS;AAAA,UACb,gBAAgB;AAAA,UAChB,cAAc;AAAA,QAChB,CAAC;AAAA,MACH;AAEA,aAAO,WAAW,WAAY;AAC5B,cAAM,wBAAwB;AAAA,MAChC,GAAG,CAAC;AAEJ,YAAM,MAAM,OAAO,IAAI,iBAAiB;AAAA,IAC1C,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,8BAA8B,SAAU,IAAI;AACzF,YAAM,wBAAwB;AAAA,IAChC,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,+BAA+B,SAAU,YAAY;AAClG,YAAM,SAAS;AAAA,QACb;AAAA,QACA,uBAAuB;AAAA,MACzB,CAAC;AAAA,IACH,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,6BAA6B,WAAY;AACtF,UAAI,gBAAgB,MAAM,MAAM;AAChC,UAAI,eAAe,MAAM,OACrB,wBAAwB,aAAa,uBACrC,8BAA8B,aAAa,6BAC3C,8BAA8B,aAAa;AAE/C,UAAI,CAAC,iBAAiB,CAAC,MAAM,oBAAoB;AAC/C;AAAA,MACF;AAEA,UAAI,cAAc,MAAM;AACxB,UAAI,cAAc,MAAM;AAExB,UAAI,wBAAwB,YAAY,sBAAsB;AAC9D,UAAI,cAAc,2BAA2B,aAAa,WAAW;AACrE,UAAI,mBAAmB;AAAA,QACrB,MAAM,sBAAsB,OAAO,cAAc;AAAA,QACjD,KAAK,sBAAsB,MAAM,cAAc,MAAM;AAAA,MACvD;AACA,UAAI,iBAAiB,KAAK,IAAI,SAAS,gBAAgB,cAAc,OAAO,eAAe,CAAC;AAE5F,UAAI,CAAC,aAAa;AAChB;AAAA,MACF;AAEA,UAAI,WAAW,CAAC;AAEhB,UAAI,uBAAuB;AACzB,iBAAS,WAAW;AACpB,YAAI,OAAO,iBAAiB;AAC5B,YAAI,MAAM,iBAAiB;AAE3B,gBAAQ,2BAA2B,aAAa,aAAa;AAC7D,eAAO,2BAA2B,aAAa,YAAY;AAE3D,gBAAQ,YAAY;AACpB,eAAO,YAAY;AAEnB,YAAI,gBAAgB,KAAK,IAAI,SAAS,gBAAgB,aAAa,OAAO,cAAc,CAAC;AAEzF,YAAI,OAAO,YAAY,cAAc,eAAe;AAClD,mBAAS,OAAO,KAAK,IAAI,GAAG,gBAAgB,YAAY,WAAW;AAAA,QACrE,OAAO;AACL,mBAAS,OAAO;AAAA,QAClB;AAKA,YAAI,+BAA+B,MAAM,YAAY,eAAe,kBAAkB,YAAY,eAAe,MAAM,eAAe,6BAA6B;AACjK,mBAAS,MAAM,KAAK,IAAI,GAAG,MAAM,YAAY,eAAe,WAAW;AAAA,QACzE,OAAO;AACL,mBAAS,MAAM;AAAA,QACjB;AAAA,MACF,OAAO;AACL,YAAI,QAAQ,cAAc,OAAO,YAAY;AAE7C,YAAI,OAAO,cAAc,MAAM,YAAY;AAG3C,YAAI,QAAQ,YAAY,cAAc,MAAM,iBAAiB,aAAa;AACxE,mBAAS,QAAQ;AAAA,QACnB,OAAO;AACL,mBAAS,OAAO;AAAA,QAClB;AAKA,YAAI,+BAA+B,iBAAiB,MAAM,YAAY,YAAY,YAAY,eAAe,kBAAkB,YAAY,eAAe,sBAAsB,MAAM,cAAc,YAAY,aAAa,6BAA6B;AACxP,mBAAS,MAAM,OAAO,YAAY,eAAe;AAAA,QACnD,OAAO;AACL,mBAAS,MAAM;AAAA,QACjB;AAAA,MACF;AAEA,UAAI,SAAS,SAAS,MAAM,MAAM,oBAAoB,QAAQ,SAAS,QAAQ,MAAM,MAAM,oBAAoB,OAAO,SAAS,aAAa,MAAM,MAAM,oBAAoB,UAAU;AACpL;AAAA,MACF;AAEA,YAAM,SAAS;AAAA,QACb,qBAAqB;AAAA,MACvB,CAAC;AAAA,IACH,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,2BAA2B,WAAY;AACpF,UAAI,QAAQ,MAAM;AAClB,UAAI,cAAc,MAAM;AAExB,UAAI,CAAC,SAAS,CAAC,aAAa;AAG1B;AAAA,MACF;AAEA,kBAAY,aAAa,MAAM;AAC/B,kBAAY,YAAY,MAAM;AAC9B,kBAAY,SAAS,MAAM;AAAA,IAC7B,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,0BAA0B,WAAY;AACnF,oBAAc;AAAA,IAChB,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,wBAAwB,WAAY;AACjF,oBAAc;AAAA,IAChB,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,gBAAgB,SAAU,gBAAgB,cAAc;AACrG,UAAI,mBAAmB,QAAQ,iBAAiB,KAAM;AACtD,UAAI,KAAK,MAAM;AAEf,UAAI,GAAG,mBAAmB;AACxB,WAAG,kBAAkB,gBAAgB,YAAY;AAAA,MACnD,WAAW,GAAG,iBAAiB;AAC7B,YAAI,QAAQ,GAAG,gBAAgB;AAC/B,cAAM,SAAS,IAAI;AACnB,cAAM,QAAQ,aAAa,YAAY;AACvC,cAAM,UAAU,aAAa,cAAc;AAC3C,cAAM,OAAO;AAAA,MACf;AAAA,IACF,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,yBAAyB,SAAU,gBAAgB,eAAe;AAE/G,YAAM;AACN,YAAM,cAAc,CAAC;AAErB,YAAM,SAAS;AAAA,QACb,aAAa,CAAC;AAAA,MAChB,CAAC;AAED,UAAI,QAAQ,MAAM,MAAM,SAAS;AACjC,UAAI,WAAW,MAAM,MAAM;AAC3B,UAAI,SAAS,uBAAuB,QAAQ;AAC5C,UAAI,kBAAkB,kBAAkB,OAAO,QAAQ,eAAe,MAAM;AAE5E,UAAI,oBAAoB,MAAM;AAC5B;AAAA,MACF;AAGA,UAAI,sBAAsB,oBAAoB,MAAM,UAAU,GAAG,eAAe,GAAG,MAAM;AACzF,UAAI,YAAY,eAAe,UAAU,qBAAqB,aAAa;AAG3E,oBAAA1B,QAAM,SAAS,QAAQ,UAAU,SAAU,OAAO,YAAY;AAC5D,YAAI,CAAC,OAAO;AACV;AAAA,QACF;AAEA,YAAI,QAAQ,iBAAiB,MAAM,MAAM,SAAS,MAAM,KAAK;AAC7D,YAAI,QAAQ,UAAU,MAAM,KAAK;AAEjC,YAAI,OAAO;AACT,cAAI,qBAAqB,sBAAsB,UAAU,QAAQ,MAAM,CAAC,GAAG,MAAM,KAAK;AAEtF,gBAAM,UAAU,MAAM,CAAC,GAAG,YAAY,oBAAoB,qBAAqB,MAAM,CAAC,EAAE,QAAQ,cAAc;AAAA,QAChH;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,oBAAoB,WAAY;AAE7E,YAAM;AACN,YAAM,cAAc,CAAC;AAErB,YAAM,SAAS;AAAA,QACb,aAAa,CAAC;AAAA,QACd,YAAY;AAAA,MACd,CAAC;AAAA,IACH,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,aAAa,SAAU,OAAO,YAAY,oBAAoB,kBAAkB,gBAAgB;AAC7I,UAAI,eAAe,MAAM,OACrB,WAAW,aAAa,UACxB,gBAAgB,aAAa;AACjC,UAAI,eAAe,uBAAS,QAAQ,QAAQ,EAAE,UAAU;AACxD,UAAI,cAAc,gBAAgB,aAAa,MAAM,MAAM,aAAa;AACxE,UAAI,aAAa,YAAY,OAAO,MAAM,kBAAkB,KAAK,MAAM,MAAM,UAAU,YAAY,OAAO,oBAAoB,kBAAkB,cAAc,CAAC;AAE/J,UAAI,sBAAsB,OAAO;AAC/B,cAAM,kBAAkB,MAAM,UAAU,YAAY,OAAO,oBAAoB,kBAAkB,gBAAgB,UAAU;AAAA,MAC7H;AAAA,IACF,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,qBAAqB,SAAU,SAAS,YAAY,OAAO,oBAAoB,kBAAkB,gBAAgB,SAAS;AAEvK,UAAI,YAAY,MAAM,SAAU;AAGhC,YAAM,cAAc,gBAAgB,gBAAgB,CAAC,GAAG,MAAM,WAAW,GAAG,CAAC,GAAG,gBAAgB,CAAC,GAAG,YAAY;AAAA,QAC9G,WAAW;AAAA,UACT;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,QACF;AAAA,QACA;AAAA,MACF,CAAC,CAAC;AACF,UAAI,aAAa,MAAM,MAAM;AAC7B,UAAI,mBAAmB,iBAAiB,MAAM,WAAW;AAEzD,YAAM,SAAS;AAAA,QACb,aAAa,MAAM;AAAA,QACnB,YAAY,cAAc,mBAAmB,KAAK,IAAI,mBAAmB,GAAG,CAAC,IAAI;AAAA,MACnF,CAAC;AAAA,IACH,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,cAAc,SAAU,OAAO,OAAO;AACnF,UAAI,KAAK,MAAM,IACX,UAAU,MAAM;AACpB,UAAI,aAAa,MAAM,YACnB,qBAAqB,MAAM,oBAC3B,mBAAmB,MAAM,kBACzB,iBAAiB,MAAM;AAE3B,UAAI,QAAQ,MAAM,MAAM,SAAS;AACjC,UAAI,SAAS,uBAAuB,MAAM,MAAM,QAAQ;AACxD,UAAI,gBAAgB,uBAAS,QAAQ,MAAM,MAAM,QAAQ,EAAE,UAAU;AACrE,UAAI,uBAAuB,cAAc,OACrC,SAAS,qBAAqB,QAC9B5B,oBAAmB,qBAAqB,kBACxC,mBAAmB,qBAAqB,kBACxCwD,SAAQ,qBAAqB;AACjC,UAAI,QAAQ,kBAAkB,OAAO,QAAQ,oBAAoB,OAAO;AACxE,UAAI,MAAM,QAAQ,mBAAmB;AACrC,UAAI,SAAS,mBAAmB,QAAQ,IAAI,OAAO;AAEnD,UAAI,kBAAkB;AACpB,kBAAU;AAAA,MACZ;AAEA,UAAI,WAAW,aAAa,OAAO,OAAO,KAAK,MAAM;AAErD,YAAM,aAAa,MAAM;AAEzB,UAAI,eAAexD,kBAAiB,IAAI,OAAO;AAE/C,UAAI,kBAAkB;AACpB,wBAAgB;AAAA,MAClB;AAEA,UAAI,mBAAmB,qBAAqB,aAAa;AAEzD,YAAM,SAAS;AAAA,QACb,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,gCAAgC;AAAA,MAClC,CAAC;AAGD,UAAI,YAAY;AAAA,QACd,QAAQ;AAAA,UACN,OAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,WAAW,YAAY,UAAU,MAAM;AAC3C,UAAI,oBAAoB,aAAa,gBAAgB,oBAAoB,kBAAkB,YAAY;AAEvG,YAAM,gBAAgB,WAAW,UAAU,mBAAmB,QAAQ;AAEtE,UAAIwD,QAAO;AACT,QAAAA,OAAM,IAAI,SAAS,OAAO,GAAG;AAAA,MAC/B;AAGA,YAAM,iBAAiB;AAAA,IACzB,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,aAAa,WAAY;AACtE,UAAI,YAAY;AAChB,oBAAA5B,QAAM,SAAS,QAAQ,MAAM,MAAM,UAAU,SAAU,OAAO;AAC5D,oBAAY,aAAa,SAAS,MAAM,MAAM;AAAA,MAChD,CAAC;AACD,aAAO;AAAA,IACT,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,YAAY,WAAY;AACrE,aAAO,SAAS,MAAM,MAAM,cAAc,MAAM,iBAAiB,MAAM,MAAM,WAAW,MAAM,KAAK,MAAM,UAAU;AAAA,IACrH,CAAC;AAED,oBAAgB,uBAAuB,KAAK,GAAG,YAAY,CAAC;AAE5D,UAAM,cAAc,CAAC;AACrB,UAAM,yBAAyB,KAAK,OAAO,EAAE,SAAS,EAAE,EAAE,UAAU,CAAC;AACrE,UAAM,aAAa,MAAM,WAAW,KAAK,uBAAuB,KAAK,CAAC;AACtE,UAAM,YAAY,MAAM,UAAU,KAAK,uBAAuB,KAAK,CAAC;AACpE,UAAM,cAAc,MAAM,YAAY,KAAK,uBAAuB,KAAK,CAAC;AACxE,UAAM,QAAQ;AAAA,MACZ,YAAY;AAAA,MACZ,gBAAgB;AAAA,MAChB,cAAc;AAAA,MACd,aAAa,CAAC;AAAA,MACd,eAAe;AAAA,MACf,qBAAqB,CAAC;AAAA,MACtB,8BAA8B;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAEA,eAAawB,gBAAe,CAAC;AAAA,IAC3B,KAAK;AAAA,IACL,OAAO,SAAS,oBAAoB;AAClC,eAAS,iBAAiB,QAAQ,KAAK,UAAU;AACjD,eAAS,iBAAiB,OAAO,KAAK,SAAS;AAC/C,eAAS,iBAAiB,SAAS,KAAK,WAAW;AACnD,WAAK,0BAA0B;AAAA,IACjC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,mBAAmB,WAAW,WAAW;AAGvD,UAAI,UAAU,wBAAwB,KAAK,MAAM,qBAAqB;AACpE,aAAK,0BAA0B;AAAA,MACjC;AAIA,UAAI,KAAK,MAAM,gCAAgC;AAC7C,aAAK,SAAS;AAAA,UACZ,gCAAgC;AAAA,QAClC,CAAC;AACD,aAAK,aAAa,KAAK,MAAM,gBAAgB,KAAK,MAAM,YAAY;AAAA,MACtE;AAEA,UAAI,KAAK,MAAM,8BAA8B;AAC3C,aAAK,SAAS;AAAA,UACZ,8BAA8B;AAAA,QAChC,CAAC;AACD,aAAK,aAAa,KAAK,MAAM,gBAAgB,KAAK,MAAM,YAAY;AAAA,MACtE;AAAA,IACF;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,uBAAuB;AACrC,eAAS,oBAAoB,QAAQ,KAAK,UAAU;AACpD,eAAS,oBAAoB,OAAO,KAAK,SAAS;AAClD,eAAS,oBAAoB,SAAS,KAAK,WAAW;AAAA,IACxD;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,SAAS;AACvB,aAAoB,cAAAxB,QAAM,cAAc,OAAO,SAAS;AAAA,QACtD,KAAK,KAAK;AAAA,MACZ,GAAG,KAAK,MAAM,KAAK,GAAG,KAAK,cAAc,GAAG,KAAK,yBAAyB,CAAC;AAAA,IAC7E;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,YAAY,OAAO;AACjC,UAAI,MAAM,WAAW,KAAK,cAAc;AACtC;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,yBAAyB,KAAK,GAAG;AACzC;AAAA,MACF;AAEA,YAAM,eAAe;AACrB,UAAI,eAAe,KAAK,OACpB,iBAAiB,aAAa,gBAC9B,eAAe,aAAa;AAChC,UAAI,eAAe,KAAK,OACpB,QAAQ,aAAa,OACrB,WAAW,aAAa;AAC5B,UAAI,SAAS,uBAAuB,QAAQ;AAC5C,UAAI,mBAAmB,kBAAkB,OAAO,QAAQ,gBAAgB,OAAO;AAC/E,UAAI,iBAAiB,kBAAkB,OAAO,QAAQ,cAAc,KAAK;AACzE,UAAI,iBAAiB,MAAM,cAAc,QAAQ,qBAAqB;AACtE,UAAI,aAAa,MAAM,cAAc,QAAQ,YAAY;AACzD,UAAI,WAAW,aAAa,OAAO,kBAAkB,gBAAgB,kBAAkB,UAAU,EAAE,QAAQ,OAAO,EAAE;AACpH,UAAI,oBAAoB,aAAa,UAAU,MAAM;AACrD,UAAI,YAAY;AAAA,QACd,QAAQ,gBAAgB,gBAAgB,CAAC,GAAG,MAAM,MAAM,GAAG,CAAC,GAAG;AAAA,UAC7D,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,WAAK,gBAAgB,WAAW,UAAU,mBAAmB,YAAY,UAAU,MAAM,CAAC;AAE1F,UAAI,iBAAiB,8BAA8B,OAAO,QAAQ,cAAc;AAChF,UAAI,WAAW,kBAAkB,kBAAkB,aAAa,kBAAkB,YAAY,MAAM,EAAE;AACtG,WAAK,SAAS;AAAA,QACZ,gBAAgB;AAAA,QAChB,cAAc;AAAA,QACd,8BAA8B;AAAA,MAChC,CAAC;AAAA,IACH;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,yBAAyB,OAAO;AAG9C,UAAI,iBAAiB,KAAK,aAAa;AACvC,UAAI,eAAe,KAAK,aAAa;AACrC,UAAI,eAAe,KAAK,OACpB,WAAW,aAAa,UACxB,QAAQ,aAAa;AACzB,UAAI,SAAS,uBAAuB,QAAQ;AAC5C,UAAI,mBAAmB,kBAAkB,OAAO,QAAQ,gBAAgB,OAAO;AAC/E,UAAI,iBAAiB,kBAAkB,OAAO,QAAQ,cAAc,KAAK;AACzE,YAAM,cAAc,QAAQ,cAAc,MAAM,OAAO,MAAM,MAAM,gBAAgB,YAAY,CAAC;AAChG,YAAM,cAAc,QAAQ,uBAAuB,MAAM,MAAM,kBAAkB,cAAc,CAAC;AAAA,IAClG;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,yBAAyB,OAAO;AAC9C,aAAO,CAAC,CAAC,MAAM;AAAA,IACjB;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,WAAW,OAAO;AAChC,UAAI,MAAM,WAAW,KAAK,cAAc;AACtC;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,yBAAyB,KAAK,GAAG;AACzC;AAAA,MACF;AAEA,YAAM,eAAe;AACrB,WAAK,yBAAyB,KAAK;AAAA,IACrC;AAAA,EACF,GAAG;AAAA,IACD,KAAK;AAAA,IACL,OAAO,SAAS,UAAU,OAAO;AAC/B,UAAI,MAAM,WAAW,KAAK,cAAc;AACtC;AAAA,MACF;AAEA,UAAI,CAAC,KAAK,yBAAyB,KAAK,GAAG;AACzC;AAAA,MACF;AAEA,YAAM,eAAe;AACrB,WAAK,yBAAyB,KAAK;AACnC,UAAI,eAAe,KAAK,OACpB,iBAAiB,aAAa,gBAC9B,eAAe,aAAa;AAChC,UAAI,eAAe,KAAK,OACpB,WAAW,aAAa,UACxB,QAAQ,aAAa;AACzB,UAAI,SAAS,uBAAuB,QAAQ;AAC5C,UAAI,mBAAmB,kBAAkB,OAAO,QAAQ,gBAAgB,OAAO;AAC/E,UAAI,iBAAiB,kBAAkB,OAAO,QAAQ,cAAc,KAAK;AACzE,UAAI,WAAW,CAAC,MAAM,MAAM,GAAG,gBAAgB,GAAG,MAAM,MAAM,cAAc,CAAC,EAAE,KAAK,EAAE;AACtF,UAAI,oBAAoB,aAAa,UAAU,MAAM;AACrD,UAAI,YAAY;AAAA,QACd,QAAQ,gBAAgB,gBAAgB,CAAC,GAAG,MAAM,MAAM,GAAG,CAAC,GAAG;AAAA,UAC7D,OAAO;AAAA,QACT,CAAC;AAAA,MACH;AACA,WAAK,gBAAgB,WAAW,UAAU,mBAAmB,YAAY,OAAO,MAAM,CAAC;AAAA,IACzF;AAAA;AAAA,EAEF,CAAC,CAAC;AAEF,SAAOwB;AACT,EAAE,cAAAxB,QAAM,SAAS;AAOjB,gBAAgB,eAAe,aAAa,SAAS;AAErD,gBAAgB,eAAe,gBAAgB;AAAA,EAC7C,eAAe;AAAA,EACf,YAAY;AAAA,EACZ,6BAA6B;AAAA,EAC7B,WAAW,SAAS,YAAY;AAC9B,WAAO;AAAA,EACT;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,SAAS,SAAS;AACxB,WAAO;AAAA,EACT;AACF,CAAC;AAED,IAAI,6BAA6B,SAAS6B,4BAA2B,YAAY,cAAc;AAC7F,MAAI,SAAS,WAAW,OAAO,iBAAiB,YAAY,IAAI,EAAE,iBAAiB,YAAY,CAAC;AAChG,SAAO,SAAS,MAAM,IAAI,SAAS;AACrC;AAEA,IAAI,iBAAiB,OAAO,cAAc,eAAe,oBAAoB,KAAK,UAAU,SAAS;AACrG,IAAI,WAAW,mBAAmB;AAAA,EAChC,UAAU;AAAA,EACV,WAAW;AAAA,EACX,OAAO;AAAA,IACL,SAAS;AAAA,IACT,OAAO;AAAA,IACP,UAAU;AAAA,IACV,QAAQ;AAAA,IACR,KAAK;AAAA,IACL,MAAM;AAAA,IACN,WAAW;AAAA,IACX,iBAAiB;AAAA,IACjB,YAAY;AAAA,IACZ,UAAU;AAAA,IACV,eAAe;AAAA,EACjB;AAAA,EACA,cAAc;AAAA,IACZ,OAAO,gBAAgB;AAAA,MACrB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,QAAQ;AAAA,IACV,GAAG,iBAAiB;AAAA,MAClB,WAAW;AAAA,MACX,YAAY;AAAA,IACd,IAAI,IAAI;AAAA,EACV;AACF,GAAG,SAAU,OAAO;AAClB,MAAI,aAAa,MAAM;AACvB,SAAO;AAAA,IACL,eAAe;AAAA,IACf,cAAc,CAAC;AAAA,EACjB;AACF,CAAC;AACD,IAAI,kBAAkB,SAAS,aAAa;AAE5C,IAAI,eAAe;AAAA,EACjB,YAAY;AACd;AAEA,IAAI,UAAU,SAASC,SAAQ,MAAM;AACnC,MAAI,UAAU,KAAK,SACf,QAAQ,KAAK,OACb,YAAY,KAAK,WACjB,aAAa,KAAK;AACtB,MAAI,SAAS,YAAU,cAAc;AAAA,IACnC;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,SAAoB,cAAA9B,QAAM,cAAc,UAAU,QAAQ,OAAO;AACnE;AAEA,QAAQ,YAAY;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYlB,OAAO,kBAAAQ,QAAU;AAAA,EACjB,UAAU,kBAAAA,QAAU;AAAA,EACpB,kBAAkB,kBAAAA,QAAU;AAAA,EAC5B,SAAS,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,WAAW,MAAM,CAAC,CAAC;AAAA,EAC7E,QAAQ,kBAAAA,QAAU;AAAA,EAClB,kBAAkB,kBAAAA,QAAU;AAAA;AAAA;AAAA;AAAA,EAK5B,mBAAmB,kBAAAA,QAAU;AAAA,EAC7B,WAAW,kBAAAA,QAAU;AACvB;AACA,QAAQ,eAAe;AAAA,EACrB,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,kBAAkB,SAAS,iBAAiB,IAAI,SAAS;AACvD,WAAO,WAAW;AAAA,EACpB;AAAA,EACA,OAAO,SAAS,QAAQ;AACtB,WAAO;AAAA,EACT;AAAA,EACA,UAAU,SAAS,WAAW;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB;AAAA,EAClB,WAAW;AAAA,EACX,kBAAkB;AACpB;", "names": ["invariant", "_setPrototypeOf", "o", "p", "_typeof2", "obj", "_typeof", "_getPrototypeOf", "o", "import_react", "import_invariant", "_typeof", "o", "_typeof", "_typeof", "_defineProperty", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "keys", "values", "_defineProperty", "merge", "omit", "keys", "isPlainObject", "compact", "isModifier", "isElement", "camelize", "pickDirectStyles", "pickNestedStyles", "hoistModifierStylesRecursive", "_toConsumableArray", "keys", "_defineProperty", "defaultPropsDecorator", "coerceSelection", "memoize", "defaultStyle", "ownKeys", "keys", "_objectSpread", "_defineProperty", "guessBaseClassName", "deriveClassNames", "_toConsumableArray", "defaultStyle", "invariant", "collectSelectedStyles", "ownKeys", "keys", "_objectSpread", "_defineProperty", "inline", "import_react", "useStyles", "defaultStyle", "escapeRegex", "findPositionOfCapturingGroup", "invariant", "combineRegExps", "countPlaceholders", "emptyFn", "iterateMentionsMarkup", "markup", "displayTransform", "getPlainText", "mapPlainTextIndex", "textIteratee", "markupIteratee", "spliceString", "applyChangeToValue", "findStartOfMentionInPlainText", "getMentions", "getSuggestionHtmlId", "countSuggestions", "getEndOfLastMention", "markupToRegex", "readConfigFromChildren", "coerceCapturingGroups", "makeMentionsMarkup", "removeAccents", "normalizeString", "getSubstringIndex", "isIE", "isNumber", "keys", "omit", "ownKeys", "_objectSpread", "defaultStyle", "enhance", "DefaultStyleEnhancer", "React", "_generateComponentKey", "notifyCaretPosition", "mentionIteratee", "renderSubstring", "getMentionComponentForMatch", "renderHighlighterCaret", "children", "PropTypes", "renderContent", "getDisplay", "id", "renderHighlightedDisplay", "onSelect", "top", "renderSuggestions", "renderSuggestion", "onMouseEnter", "renderLoadingIndicator", "handleMouseEnter", "select", "getID", "makeTriggerRegex", "getDataProvider", "MentionsInput", "ReactDOM", "queryInfo", "result", "onAdd", "getComputedStyleLengthProp", "Mention"]}