# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Test coverage
coverage/
# Playwright
test-results/
playwright-report/
playwright/.cache/
# Vitest
.vitest/
# Test artifacts
*.test.js.snap
*.test.tsx.snap

# Build artifacts
dist/
dist-ssr/

# Tauri build cache
src-tauri/target/
src-tauri/gen/

# Temporary files
*.tmp
*.temp
api_key.txt
bun.lockb

# Documentation temporaire
*ANALYSE*.md
*CORRECTION*.md
*VERIFICATION*.md
*RESUME*.md
*SOLUTION*.md