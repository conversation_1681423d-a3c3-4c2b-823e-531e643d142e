
import { Status } from '@/types';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { StatusBadge } from './StatusBadge';

interface StatusSelectProps {
  value: Status;
  onChange: (value: Status) => void;
}

export const StatusSelect = ({ value, onChange }: StatusSelectProps) => {
  const statuses: { value: Status; label: string }[] = [
    { value: 'manquant', label: '🔴 Manquant' },
    { value: 'transmis', label: '🟠 Transmis' },
    { value: 'validation', label: '🔵 En validation' },
    { value: 'valide', label: '🟢 Validé' },
    { value: 'refuse', label: '⚫ Refusé' },
    { value: 'urgent', label: '⚡ Urgent' }
  ];

  return (
    <Select value={value} onValueChange={(val) => onChange(val as Status)}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Statut">
          <StatusBadge status={value} />
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        {statuses.map((status) => (
          <SelectItem key={status.value} value={status.value}>
            <div className="flex items-center gap-2">
              <StatusBadge status={status.value} />
            </div>
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};
