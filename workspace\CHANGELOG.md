# Changelog

Toutes les modifications notables de ce projet seront documentées dans ce fichier.

Le format est basé sur [Keep a Changelog](https://keepachangelog.com/fr/1.0.0/),
et ce projet adhère au [Versioning Sémantique](https://semver.org/spec/v2.0.0.html).

## [Non publié]

### Ajouté
- Fonctionnalité de changelog

## [0.1.0] - 2025-01-28

### Ajouté
- Configuration initiale du projet WeMa Tracker
- Interface utilisateur React avec TypeScript
- Intégration Supabase pour la base de données
- Application desktop Tauri
- Système de gestion des profils utilisateur
- Gestion des clients et dossiers
- Système de notifications en temps réel
- Fonctionnalités RGPD complètes
- Documentation de sécurité et conformité
- Scripts d'automatisation et tests
- Configuration pour Windows, macOS, Linux
- Support mobile Android/iOS
- Exportation de données
- Système de chiffrement
- Politique de rétention des données
- Interface de conformité RGPD
- Système de ping et keep-alive
- Configuration d'installation personnalisée (NSIS)
- Icônes pour toutes les plateformes
- Documentation complète

### Sécurité
- Implémentation du chiffrement des données
- Configuration CSP pour Tauri
- Protection contre les fuites de données
- Audit de sécurité Supabase
- Conformité RGPD complète 