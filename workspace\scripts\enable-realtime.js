/**
 * Script pour activer les souscriptions temps réel sur Supabase
 * Ce script doit être exécuté une seule fois pour configurer le realtime
 * sur les tables principales de l'application.
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtenir le répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Charger les variables d'environnement depuis le fichier .env
function loadEnv() {
  const envPath = path.join(__dirname, '..', '.env');
  if (!fs.existsSync(envPath)) {
    console.error('Fichier .env non trouvé');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      env[key.trim()] = value.trim().replace(/^["']|["']$/g, '');
    }
  });
  
  return env;
}

async function enableRealtime() {
  try {
    console.log('🚀 Activation des souscriptions temps réel sur Supabase...\n');

    const env = loadEnv();
    const supabaseUrl = env.VITE_SUPABASE_URL;
    const supabaseKey = env.VITE_SUPABASE_API_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Variables d\'environnement Supabase manquantes dans .env');
    }

    console.log('📡 Connexion à Supabase...');
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Lire le fichier de migration
    const migrationPath = path.join(__dirname, '..', 'supabase', 'migrations', '20250101_enable_realtime.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('⚡ Exécution de la migration pour activer le temps réel...');

    // Exécuter la migration
    const { data, error } = await supabase.rpc('exec_sql', { sql: migrationSQL });

    if (error) {
      // Si la fonction exec_sql n'existe pas, essayer d'exécuter les commandes une par une
      console.log('📝 Exécution des commandes SQL individuellement...');
      
      const commands = [
        "ALTER PUBLICATION supabase_realtime ADD TABLE public.clients;",
        "ALTER PUBLICATION supabase_realtime ADD TABLE public.steps;", 
        "ALTER PUBLICATION supabase_realtime ADD TABLE public.step_assignments;",
        "ALTER PUBLICATION supabase_realtime ADD TABLE public.assignees;",
        "ALTER PUBLICATION supabase_realtime ADD TABLE public.notifications;",
        "ALTER PUBLICATION supabase_realtime ADD TABLE public.pings;"
      ];

      for (const command of commands) {
        try {
          console.log(`   Exécution: ${command}`);
          const { error: cmdError } = await supabase.rpc('exec_sql', { sql: command });
          if (cmdError) {
            console.log(`   ⚠️  Commande ignorée (probablement déjà exécutée): ${cmdError.message}`);
          } else {
            console.log(`   ✅ Commande exécutée avec succès`);
          }
        } catch (e) {
          console.log(`   ⚠️  Erreur ignorée: ${e.message}`);
        }
      }
    } else {
      console.log('✅ Migration exécutée avec succès');
    }

    // Vérifier que les souscriptions sont actives
    console.log('\n🔍 Vérification des souscriptions temps réel...');
    
    const tables = ['clients', 'steps', 'step_assignments', 'assignees', 'notifications', 'pings'];
    
    for (const table of tables) {
      try {
        // Tester la souscription en créant un canal temporaire
        const channel = supabase.channel(`test-${table}`)
          .on('postgres_changes', { 
            event: '*', 
            schema: 'public', 
            table: table 
          }, () => {})
          .subscribe();
          
        console.log(`   ✅ Table '${table}' : Souscription temps réel active`);
        
        // Nettoyer le canal de test
        await supabase.removeChannel(channel);
      } catch (e) {
        console.log(`   ❌ Table '${table}' : Erreur de souscription - ${e.message}`);
      }
    }

    console.log('\n🎉 Configuration du temps réel terminée !');
    console.log('\n📋 Résumé :');
    console.log('   • Tables avec temps réel activé : clients, steps, step_assignments, assignees, notifications, pings');
    console.log('   • Les changements seront maintenant propagés en temps réel entre tous les profils');
    console.log('   • Redémarrez l\'application pour que les changements prennent effet');

  } catch (error) {
    console.error('❌ Erreur lors de l\'activation du temps réel:', error.message);
    process.exit(1);
  }
}

// Exécuter le script
enableRealtime();
