import React from 'react';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { Profile } from '@/types';
import { useProfileContext } from '@/contexts/ProfileContext';

interface DeleteProfileDialogProps {
  profile: Profile;
  onDeleted?: () => void;
}

export const DeleteProfileDialog: React.FC<DeleteProfileDialogProps> = ({ profile, onDeleted }) => {
  const { deleteProfile, currentProfile } = useProfileContext();
  const [isOpen, setIsOpen] = React.useState(false);
  const [isDeleting, setIsDeleting] = React.useState(false);

  const handleDelete = async () => {
    try {
      setIsDeleting(true);
      const success = await deleteProfile(profile.id);
      setIsDeleting(false);

      if (success) {
        setIsOpen(false);
        if (onDeleted) onDeleted();
      }
    } catch (error) {
      setIsDeleting(false);
      console.error('Error deleting profile:', error);
    }
  };

  // Désactiver le bouton si c'est le profil courant
  const isCurrentProfile = currentProfile?.id === profile.id;

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          className="text-red-500 hover:text-red-700 hover:bg-red-100/50 p-1 h-7 w-7 min-w-0 flex items-center justify-center rounded-full"
          disabled={isCurrentProfile}
          title={isCurrentProfile ? "Vous ne pouvez pas supprimer le profil actuellement sélectionné" : "Supprimer ce profil"}
        >
          <Trash2 size={15} />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Êtes-vous sûr de vouloir supprimer ce profil ?</AlertDialogTitle>
          <AlertDialogDescription>
            Cette action est irréversible. Toutes les données associées à {profile.name} seront supprimées.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel disabled={isDeleting}>Annuler</AlertDialogCancel>
          <AlertDialogAction
            onClick={handleDelete}
            disabled={isDeleting}
            className="bg-red-500 hover:bg-red-700"
          >
            {isDeleting ? 'Suppression...' : 'Supprimer'}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
