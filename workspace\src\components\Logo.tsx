
import React from 'react';
import { cn } from '@/lib/utils';

type LogoProps = {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
};

export const Logo: React.FC<LogoProps> = ({
  className,
  size = 'md',
  onClick
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  };

  return (
    <img
      src="/lovable-uploads/wema-logo.png"
      alt="WeMa Tracker Client Logo"
      className={cn(
        'object-contain',
        sizeClasses[size],
        onClick && 'cursor-pointer',
        className
      )}
      onClick={onClick}
    />
  );
};
