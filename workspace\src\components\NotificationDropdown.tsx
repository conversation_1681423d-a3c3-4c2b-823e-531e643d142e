
import React, { useEffect, useState, useCallback, useRef } from 'react';
import { Bell } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Notification } from '@/types';
import { useClientContext } from '@/contexts/ClientContext';
import { useNotificationContext } from '@/contexts/NotificationContext';
import { useProfileContext } from '@/contexts/ProfileContext';
import { format, formatDistance } from 'date-fns';
import { fr } from 'date-fns/locale';
import { useNavigate } from 'react-router-dom';
import { Badge } from './ui/badge';
import { sendDesktopNotification } from '@/utils/notifications';
import { getUserNotifications, markNotificationAsRead, markAllNotificationsAsRead } from '@/services/notificationService';
import { useToast } from './ui/use-toast';

export const NotificationDropdown = () => {
  const navigate = useNavigate();
  const { getClientByStepId } = useClientContext();
  const { currentProfile } = useProfileContext();
  const { notifications, unreadCount, getNotifications, markNotificationAsRead: markAsRead } = useNotificationContext();
  const [isOpen, setIsOpen] = useState(false);
  const { toast, dismiss } = useToast();

  // Utiliser directement les notifications du contexte - pas besoin d'état local
  // Cela évite les problèmes de synchronisation et les notifications qui réapparaissent

  // Référence pour stocker le nombre précédent de notifications non lues
  const previousUnreadCountRef = useRef(0);

  // Effet pour détecter les nouvelles notifications et afficher une notification toast
  useEffect(() => {
    // Si le nombre de notifications non lues a augmenté et que le menu n'est pas ouvert
    if (unreadCount > previousUnreadCountRef.current && !isOpen) {
      // Trouver les nouvelles notifications (celles qui ne sont pas lues)
      const unreadNotifications = notifications.filter(n => !n.read);
      if (unreadNotifications.length > 0) {
        // Prendre la notification la plus récente
        const newestNotification = unreadNotifications[0];

        // Afficher une notification toast
        toast({
          title: "Nouvelle notification",
          description: newestNotification.message,
          duration: 5000, // Reste affiché 5 secondes
          variant: "info",
          action: (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsOpen(true);
                // Fermer toutes les notifications toast
                dismiss();
              }}
              className="mt-2"
            >
              Voir la notification
            </Button>
          ),
        });
      }
    }

    // Mettre à jour la référence
    previousUnreadCountRef.current = unreadCount;
  }, [unreadCount, isOpen, notifications, toast, dismiss]);

  const handleNotificationClick = async (notification: Notification) => {
    if (!currentProfile) return;

    try {
      // Marquer la notification comme lue en utilisant le contexte
      await markAsRead(notification.id);

      // Trouver l'étape et naviguer vers elle
      if (notification.step_id) {
        const clientInfo = await getClientByStepId(notification.step_id);
        if (clientInfo) {
          navigate(`/client/${clientInfo.clientId}`);
        }
      }

      // Pas besoin de mise à jour locale - le contexte gère automatiquement l'état
      setIsOpen(false);
    } catch (error) {
      console.error('Erreur lors du traitement de la notification:', error);
    }
  };

  // Fonction pour marquer toutes les notifications comme lues
  const markAllAsRead = async () => {
    if (!currentProfile) return;

    try {
      // Marquer toutes les notifications comme lues en utilisant le contexte
      for (const notification of notifications.filter(n => !n.read)) {
        await markAsRead(notification.id);
      }
    } catch (error) {
      console.error('Erreur lors du marquage de toutes les notifications comme lues:', error);
    }
  };



  // Fonction pour gérer l'ouverture/fermeture du menu et marquer les notifications comme lues
  const handleOpenChange = async (open: boolean) => {
    setIsOpen(open);

    // Si le menu s'ouvre, marquer toutes les notifications comme lues
    if (open && notifications.filter(n => !n.read).length > 0) {
      await markAllAsRead();

      // Fermer toutes les notifications toast actives
      // pour éviter qu'elles restent affichées après avoir ouvert le menu
      dismiss();
    }
  };

  return (
    <DropdownMenu open={isOpen} onOpenChange={handleOpenChange}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="relative">
          <Bell size={18} className="text-red-600" />
          {unreadCount > 0 && (
            <Badge className="absolute -top-2 -right-2 bg-yellow-500 text-black font-bold text-xs w-5 h-5 flex items-center justify-center p-0 rounded-full shadow-sm">
              {unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-72">
        <div className="px-3 py-2 text-sm font-medium border-b flex justify-between items-center">
          <span>Notifications</span>
          {notifications.filter(n => !n.read).length > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              className="text-xs h-6 px-2 py-0 hover:bg-gray-100"
            >
              Tout marquer comme lu
            </Button>
          )}
        </div>
        <div className="max-h-80 overflow-y-auto">
          {notifications.length === 0 ? (
            <div className="px-3 py-4 text-center text-sm text-gray-500">
              Aucune notification
            </div>
          ) : (
            notifications.map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                className={`px-3 py-2 cursor-pointer ${!notification.read ? 'bg-blue-50' : ''}`}
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="flex items-start gap-3">
                  <Avatar className="h-8 w-8">
                    <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
                      {notification.created_by_profile?.name?.charAt(0) || '?'}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <p className="text-sm font-medium">
                      {notification.message}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatDistance(new Date(notification.created_at), new Date(), {
                        addSuffix: true,
                        locale: fr
                      })}
                    </p>
                  </div>
                </div>
              </DropdownMenuItem>
            ))
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
