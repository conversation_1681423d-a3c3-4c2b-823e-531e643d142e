
/**
 * Types et interfaces pour l'application WeMa Tracker
 * Point d'entrée principal pour tous les types de l'application
 */

// Réexportation des types par domaine métier
export * from './client.types';
export * from './supabase.types';
export * from './communication.types';

// Types communs réexportés pour compatibilité
export type { UUID, ISODateString, Status } from './client.types';
export type {
  Client,
  Step,
  Assignee,
  ProgressStats,
  UrgentTask,
  ClientSearchFilters,
  ClientSortOptions
} from './client.types';

export type {
  Profile,
  Notification,
  Ping,
  NotificationType,
  NotificationPreferences
} from './communication.types';

export type {
  SupabaseClient,
  SupabaseStep,
  SupabaseAssignee,
  SupabaseStepAssignment,
  SupabaseNotification,
  SupabasePing,
  SupabaseComplianceDocument
} from './supabase.types';



// Types pour les réponses d'API
export interface ApiResponse<T> {
  data: T | null;
  error: Error | null;
}

// Types pour la gestion des erreurs
export interface AppError extends Error {
  code?: string;
  details?: string;
  hint?: string;
}

// Fonctions utilitaires pour la conversion de types
export function supabaseClientToClient(supabaseClient: SupabaseClient, steps: Step[] = []): Client {
  return {
    id: supabaseClient.id,
    name: supabaseClient.name,
    dueDate: supabaseClient.due_date,
    completed: supabaseClient.completed ?? false, // Convertir null en false
    steps,
    created_at: supabaseClient.created_at
  };
}

export function clientToSupabaseClient(client: Client): Omit<SupabaseClient, 'created_at'> {
  return {
    id: client.id,
    name: client.name,
    due_date: client.dueDate,
    completed: client.completed
  };
}
