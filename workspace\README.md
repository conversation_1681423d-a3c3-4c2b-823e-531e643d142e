# WeMa Tracker

Application de suivi des dossiers clients pour les professionnels.

## Fonctionnalités

- Gestion des dossiers clients
- Suivi des étapes de traitement
- Système de notifications et de messages entre utilisateurs
- Gestion des profils utilisateurs
- Interface moderne et intuitive

## Project info

**URL**: https://lovable.dev/projects/b4711b05-f8bd-4b04-bc12-eea183ea1d33

## How can I edit this code?

There are several ways of editing your application.

**Use Lovable**

Simply visit the [Lovable Project](https://lovable.dev/projects/b4711b05-f8bd-4b04-bc12-eea183ea1d33) and start prompting.

Changes made via Lovable will be committed automatically to this repo.

**Use your preferred IDE**

If you want to work locally using your own IDE, you can clone this repo and push changes. Pushed changes will also be reflected in Lovable.

The only requirement is having Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository using the project's Git URL.
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory.
cd <YOUR_PROJECT_NAME>

# Step 3: Install the necessary dependencies.
npm i

# Step 4: Start the development server with auto-reloading and an instant preview.
npm run dev
```

**Edit a file directly in GitHub**

- Navigate to the desired file(s).
- Click the "Edit" button (pencil icon) at the top right of the file view.
- Make your changes and commit the changes.

**Use GitHub Codespaces**

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/b4711b05-f8bd-4b04-bc12-eea183ea1d33) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)

## Génération d'un exécutable Windows (.exe)

Pour générer un exécutable Windows (.exe) de l'application, suivez ces étapes :

1. Assurez-vous d'avoir installé toutes les dépendances nécessaires :
   - Node.js et npm
   - Rust et Cargo (pour Tauri)
   - Les outils de développement Windows (Visual Studio Build Tools)

2. Ajoutez les scripts de build dans le fichier `package.json` :
   ```json
   "scripts": {
     "tauri": "tauri",
     "build:windows": "tauri build --target windows"
   }
   ```

3. Construisez l'application :
   ```bash
   npm run build:windows
   ```

4. L'exécutable sera généré dans le dossier suivant :
   ```
   src-tauri/target/release/bundle/nsis/
   ```

5. Vous y trouverez deux fichiers :
   - `wema-tracker_0.1.0_x64-setup.exe` : Programme d'installation qui installera l'application sur l'ordinateur
   - `wema-tracker_0.1.0_x64.msi` : Package d'installation MSI (pour les déploiements en entreprise)

6. Pour installer l'application, exécutez le fichier `wema-tracker_0.1.0_x64-setup.exe` et suivez les instructions.
