/**
 * Composant pour afficher la date de rendu avec indicateurs visuels
 * Affiche la date avec des couleurs et icônes selon l'urgence
 */

import React from 'react';

interface DueDateDisplayProps {
  dueDate: string;
  variant?: 'card' | 'detail';
  className?: string;
}

export const DueDateDisplay: React.FC<DueDateDisplayProps> = ({ 
  dueDate, 
  variant = 'card',
  className = '' 
}) => {
  const today = new Date();
  const dueDateObj = new Date(dueDate);
  const diffTime = dueDateObj.getTime() - today.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  // Déterminer les couleurs selon l'urgence
  let bgColor = 'bg-green-50';
  let textColor = 'text-green-700';
  let borderColor = 'border-green-200';

  if (diffDays < 0) {
    bgColor = 'bg-red-50';
    textColor = 'text-red-700';
    borderColor = 'border-red-200';
  } else if (diffDays === 0) {
    bgColor = 'bg-orange-50';
    textColor = 'text-orange-700';
    borderColor = 'border-orange-200';
  } else if (diffDays <= 7) {
    bgColor = 'bg-yellow-50';
    textColor = 'text-yellow-700';
    borderColor = 'border-yellow-200';
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('fr-FR');
  };

  const formatDateLong = (date: Date) => {
    return date.toLocaleDateString('fr-FR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getTimeRemaining = () => {
    if (diffDays < 0) {
      return `${Math.abs(diffDays)}j en retard`;
    } else if (diffDays === 0) {
      return 'Aujourd\'hui';
    } else {
      return `${diffDays}j restants`;
    }
  };

  const getActionText = () => {
    if (diffDays < 0) {
      return 'Rendu le'; // Passé = rendu
    } else {
      return 'À rendre le'; // Futur = à rendre
    }
  };

  if (variant === 'detail') {
    return (
      <div className={`mt-3 p-2 bg-white rounded border border-blue-100 ${className}`}>
        <p className="text-sm text-blue-700 font-medium">
          {formatDateLong(dueDateObj)}
        </p>
        <p className={`text-xs mt-1 ${
          diffDays < 0
            ? 'text-red-600'
            : diffDays === 0
              ? 'text-orange-600'
              : diffDays <= 7
                ? 'text-orange-600'
                : 'text-green-600'
        }`}>
          {diffDays < 0 && `Échéance dépassée de ${Math.abs(diffDays)} jour${Math.abs(diffDays) > 1 ? 's' : ''}`}
          {diffDays === 0 && 'Échéance aujourd\'hui !'}
          {diffDays > 0 && `${diffDays} jour${diffDays > 1 ? 's' : ''} restant${diffDays > 1 ? 's' : ''}`}
        </p>
      </div>
    );
  }

  // Variant 'card' (par défaut)
  return (
    <div className={`${bgColor} ${borderColor} border rounded-md p-2 ${className}`}>
      <div className="flex items-center justify-between">
        <span className={`text-xs font-medium ${textColor}`}>
          {getActionText()} {formatDate(dueDateObj)}
        </span>
        <span className={`text-xs ${textColor}`}>
          {getTimeRemaining()}
        </span>
      </div>
    </div>
  );
};
