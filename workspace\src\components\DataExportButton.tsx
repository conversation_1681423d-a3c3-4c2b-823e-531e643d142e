import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { exportUserData, downloadExportedData } from "@/utils/data-export";
import { useClientContext } from "@/contexts/ClientContext";
import { Loader2 } from "lucide-react";

/**
 * Bouton permettant à l'utilisateur d'exporter ses données personnelles
 * Conforme aux recommandations de la CNIL pour le droit à la portabilité
 */
const DataExportButton: React.FC = () => {
  const { currentProfile } = useClientContext();
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    if (!currentProfile) {
      console.error("Aucun profil sélectionné");
      return;
    }

    try {
      setIsExporting(true);
      
      // Exporter les données de l'utilisateur
      const userData = await exportUserData(currentProfile.id);
      
      if (!userData) {
        throw new Error("Impossible d'exporter les données");
      }
      
      // Télécharger les données au format JSON
      const filename = `export_donnees_${currentProfile.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.json`;
      downloadExportedData(userData, filename);
      
    } catch (error) {
      console.error("Erreur lors de l'exportation des données:", error);
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <Button
      variant="outline"
      size="sm"
      onClick={handleExport}
      disabled={!currentProfile || isExporting}
      className="flex items-center gap-2"
    >
      {isExporting ? (
        <>
          <Loader2 className="h-4 w-4 animate-spin" />
          Exportation en cours...
        </>
      ) : (
        <>
          Exporter mes données
        </>
      )}
    </Button>
  );
};

export default DataExportButton;
