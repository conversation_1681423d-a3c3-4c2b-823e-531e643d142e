import React, { useState, useMemo } from 'react';
import {
  AreaChart,
  Area,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { usePatrimoineEvolutionSimple } from '@/hooks/usePatrimoineEvolutionSimple';
import { usePatrimoineEvolutionFournisseurs } from '@/hooks/usePatrimoineEvolutionFournisseurs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { TrendingUp, TrendingDown, Calendar, Euro, Users, BarChart3 } from 'lucide-react';

interface PatrimoineEvolutionChartProps {
  className?: string;
  fournisseurs?: Array<{ id: string; nom: string; total: number }>;
}

export const PatrimoineEvolutionChart: React.FC<PatrimoineEvolutionChartProps> = ({
  className,
  fournisseurs = []
}) => {
  const [showFournisseurs, setShowFournisseurs] = useState(false);
  const [selectedFournisseurs, setSelectedFournisseurs] = useState<string[]>([]);

  const {
    snapshots,
    loading: loadingGlobal,
    getEvolutionMetrics,
    formatMontant,
    formatPercentage,
    hasData
  } = usePatrimoineEvolutionSimple();

  const {
    getEvolutionsByDate,
    getChartData,
    loading: loadingFournisseurs,
    formatMontant: formatMontantFournisseur
  } = usePatrimoineEvolutionFournisseurs();

  const loading = loadingGlobal || loadingFournisseurs;
  const metrics = getEvolutionMetrics();

  // Couleurs pour les fournisseurs
  const fournisseurColors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
  ];

  // Préparer les données combinées pour le graphique
  const chartData = useMemo(() => {
    if (!showFournisseurs || selectedFournisseurs.length === 0) {
      // Mode global uniquement
      return snapshots.map(snapshot => ({
        date: snapshot.date,
        dateFormatted: new Date(snapshot.date).toLocaleDateString('fr-FR', {
          day: '2-digit',
          month: '2-digit'
        }),
        total: snapshot.total
      }));
    }

    // Mode avec fournisseurs sélectionnés
    const fournisseurData = getChartData(selectedFournisseurs);
    const globalData = snapshots.reduce((acc, snapshot) => {
      acc[snapshot.date] = snapshot.total;
      return acc;
    }, {} as Record<string, number>);

    // Combiner toutes les dates
    const allDates = new Set([
      ...snapshots.map(s => s.date),
      ...fournisseurData.map(f => f.date)
    ]);

    return Array.from(allDates)
      .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
      .map(date => {
        const fournisseurEntry = fournisseurData.find(f => f.date === date);
        const result: any = {
          date,
          dateFormatted: new Date(date).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit'
          }),
          total: globalData[date] || 0
        };

        // Ajouter les données de chaque fournisseur sélectionné
        selectedFournisseurs.forEach(fournisseurId => {
          const fournisseur = fournisseurs.find(f => f.id === fournisseurId);
          if (fournisseur) {
            result[fournisseur.nom] = fournisseurEntry?.[fournisseurId] || 0;
          }
        });

        return result;
      });
  }, [snapshots, showFournisseurs, selectedFournisseurs, getChartData, fournisseurs]);

  const handleFournisseurToggle = (fournisseurId: string) => {
    setSelectedFournisseurs(prev =>
      prev.includes(fournisseurId)
        ? prev.filter(id => id !== fournisseurId)
        : [...prev, fournisseurId]
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hasData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-blue-600">
            <Calendar className="h-16 w-16 mx-auto mb-4 text-blue-300" />
            <p className="text-lg font-medium mb-2">Aucun point d'évolution</p>
            <p className="text-sm text-blue-500">Ajoutez des points dans le tableau global pour voir l'évolution</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
          <div className="flex items-center gap-3">
            {metrics.totalGrowth !== 0 && (
              <Badge
                variant={metrics.totalGrowth >= 0 ? "default" : "destructive"}
                className={metrics.totalGrowth >= 0 ? "bg-blue-100 text-blue-800" : "bg-red-100 text-red-800"}
              >
                {metrics.totalGrowth >= 0 ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {formatPercentage(metrics.totalGrowth)}
              </Badge>
            )}
            <Badge variant="outline" className="bg-blue-50 text-blue-800">
              <Euro className="h-3 w-3 mr-1" />
              {formatMontant(metrics.currentTotal)}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Sélecteur de fournisseurs - Petit élément en haut à gauche */}
        {fournisseurs.length > 0 && (
          <div className="relative">
            <div className="absolute top-2 left-2 z-10 bg-white/95 backdrop-blur-sm border border-gray-200 rounded-lg p-2 shadow-sm">
              <div className="flex items-center gap-2 mb-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFournisseurs(!showFournisseurs)}
                  className="h-6 px-2 text-xs"
                >
                  <Users className="h-3 w-3 mr-1" />
                  Fournisseurs
                </Button>
                {showFournisseurs && selectedFournisseurs.length > 0 && (
                  <Badge variant="secondary" className="text-xs">
                    {selectedFournisseurs.length}
                  </Badge>
                )}
              </div>

              {showFournisseurs && (
                <div className="space-y-1 max-h-32 overflow-y-auto">
                  {fournisseurs.map((fournisseur, index) => (
                    <div key={fournisseur.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={`fournisseur-${fournisseur.id}`}
                        checked={selectedFournisseurs.includes(fournisseur.id)}
                        onCheckedChange={() => handleFournisseurToggle(fournisseur.id)}
                        className="h-3 w-3"
                      />
                      <div className="flex items-center gap-1">
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: fournisseurColors[index % fournisseurColors.length] }}
                        />
                        <label
                          htmlFor={`fournisseur-${fournisseur.id}`}
                          className="text-xs font-medium cursor-pointer truncate max-w-20"
                          title={fournisseur.nom}
                        >
                          {fournisseur.nom}
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Graphique d'évolution */}
        <div className="h-80 relative">
          <ResponsiveContainer width="100%" height="100%">
            {showFournisseurs && selectedFournisseurs.length > 0 ? (
              <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis
                  dataKey="dateFormatted"
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => {
                    if (value >= 1000000) {
                      return `${(value / 1000000).toFixed(1)}M€`;
                    } else if (value >= 1000) {
                      return `${(value / 1000).toFixed(0)}K€`;
                    }
                    return `${value}€`;
                  }}
                />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="bg-white p-3 border border-blue-200 rounded-lg shadow-lg">
                          <p className="font-medium text-blue-900 mb-2">{label}</p>
                          {payload.map((entry, index) => (
                            <div key={`${entry.dataKey}-${index}`} className="flex items-center gap-2">
                              <div
                                className="w-2 h-2 rounded-full"
                                style={{ backgroundColor: entry.color }}
                              />
                              <span className="text-xs font-medium">{entry.dataKey}:</span>
                              <span className="text-xs font-bold">
                                {formatMontant(entry.value as number)}
                              </span>
                            </div>
                          ))}
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Legend />

                {/* Ligne du total global */}
                <Line
                  type="monotone"
                  dataKey="total"
                  stroke="#1F2937"
                  strokeWidth={3}
                  dot={{ fill: '#1F2937', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#1F2937', strokeWidth: 2, fill: '#ffffff' }}
                  name="Total Global"
                />

                {/* Lignes des fournisseurs sélectionnés */}
                {selectedFournisseurs.map((fournisseurId, index) => {
                  const fournisseur = fournisseurs.find(f => f.id === fournisseurId);
                  if (!fournisseur) return null;

                  return (
                    <Line
                      key={fournisseurId}
                      type="monotone"
                      dataKey={fournisseur.nom}
                      stroke={fournisseurColors[index % fournisseurColors.length]}
                      strokeWidth={2}
                      dot={{ fill: fournisseurColors[index % fournisseurColors.length], strokeWidth: 1, r: 3 }}
                      activeDot={{ r: 5, stroke: fournisseurColors[index % fournisseurColors.length], strokeWidth: 2, fill: '#ffffff' }}
                      name={fournisseur.nom}
                    />
                  );
                })}
              </LineChart>
            ) : (
              <AreaChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <defs>
                  <linearGradient id="patrimoineGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                    <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.05} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis
                  dataKey="dateFormatted"
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => {
                    if (value >= 1000000) {
                      return `${(value / 1000000).toFixed(1)}M€`;
                    } else if (value >= 1000) {
                      return `${(value / 1000).toFixed(0)}K€`;
                    }
                    return `${value}€`;
                  }}
                />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="bg-white p-3 border border-blue-200 rounded-lg shadow-lg">
                          <p className="font-medium text-blue-900">{label}</p>
                          <p className="text-blue-600 font-bold">
                            {formatMontant(payload[0].value as number)}
                          </p>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="total"
                  stroke="#3B82F6"
                  strokeWidth={3}
                  fill="url(#patrimoineGradient)"
                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 5 }}
                  activeDot={{ r: 7, stroke: '#3B82F6', strokeWidth: 2, fill: '#ffffff' }}
                />
              </AreaChart>
            )}
          </ResponsiveContainer>
        </div>

        {/* Métriques d'évolution */}
        {chartData.length > 1 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Croissance totale</div>
              <div className={`text-xl font-bold ${metrics.totalGrowth >= 0 ? 'text-blue-700' : 'text-red-600'}`}>
                {formatPercentage(metrics.totalGrowth)}
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Meilleur point</div>
              <div className="text-xl font-bold text-blue-700">
                {metrics.bestSnapshot ? formatMontant(metrics.bestSnapshot.total) : '-'}
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Point précédent</div>
              <div className="text-xl font-bold text-blue-700">
                {formatMontant(metrics.previousTotal)}
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Nombre de points</div>
              <div className="text-xl font-bold text-blue-700">
                {chartData.length}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
