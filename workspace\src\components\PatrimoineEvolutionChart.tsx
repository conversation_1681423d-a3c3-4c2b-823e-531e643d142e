import React, { useState } from 'react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { usePatrimoineEvolutionSimple } from '@/hooks/usePatrimoineEvolutionSimple';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { TrendingUp, TrendingDown, Calendar, Euro, Plus } from 'lucide-react';

interface PatrimoineEvolutionChartProps {
  className?: string;
}

export const PatrimoineEvolutionChart: React.FC<PatrimoineEvolutionChartProps> = ({ className }) => {
  const {
    snapshots,
    loading,
    getEvolutionMetrics,
    formatMontant,
    formatPercentage,
    hasData
  } = usePatrimoineEvolutionSimple();

  const metrics = getEvolutionMetrics();

  // Préparer les données pour le graphique (seulement les snapshots existants)
  const chartData = snapshots.map(snapshot => ({
    date: snapshot.date,
    dateFormatted: new Date(snapshot.date).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit'
    }),
    total: snapshot.total
  }));

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hasData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-blue-600">
            <Calendar className="h-16 w-16 mx-auto mb-4 text-blue-300" />
            <p className="text-lg font-medium mb-2">Aucun point d'évolution</p>
            <p className="text-sm text-blue-500">Ajoutez des points dans le tableau global pour voir l'évolution</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
          <div className="flex items-center gap-3">
            {metrics.totalGrowth !== 0 && (
              <Badge
                variant={metrics.totalGrowth >= 0 ? "default" : "destructive"}
                className={metrics.totalGrowth >= 0 ? "bg-blue-100 text-blue-800" : "bg-red-100 text-red-800"}
              >
                {metrics.totalGrowth >= 0 ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {formatPercentage(metrics.totalGrowth)}
              </Badge>
            )}
            <Badge variant="outline" className="bg-blue-50 text-blue-800">
              <Euro className="h-3 w-3 mr-1" />
              {formatMontant(metrics.currentTotal)}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Graphique d'évolution */}
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
              <defs>
                <linearGradient id="patrimoineGradient" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                  <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.05} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
              <XAxis
                dataKey="dateFormatted"
                stroke="#6B7280"
                fontSize={12}
                tickLine={false}
                axisLine={false}
              />
              <YAxis
                stroke="#6B7280"
                fontSize={12}
                tickLine={false}
                axisLine={false}
                tickFormatter={(value) => {
                  if (value >= 1000000) {
                    return `${(value / 1000000).toFixed(1)}M€`;
                  } else if (value >= 1000) {
                    return `${(value / 1000).toFixed(0)}K€`;
                  }
                  return `${value}€`;
                }}
              />
              <Tooltip
                content={({ active, payload, label }) => {
                  if (active && payload && payload.length) {
                    return (
                      <div className="bg-white p-3 border border-blue-200 rounded-lg shadow-lg">
                        <p className="font-medium text-blue-900">{label}</p>
                        <p className="text-blue-600 font-bold">
                          {formatMontant(payload[0].value as number)}
                        </p>
                      </div>
                    );
                  }
                  return null;
                }}
              />
              <Area
                type="monotone"
                dataKey="total"
                stroke="#3B82F6"
                strokeWidth={3}
                fill="url(#patrimoineGradient)"
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 5 }}
                activeDot={{ r: 7, stroke: '#3B82F6', strokeWidth: 2, fill: '#ffffff' }}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>

        {/* Métriques d'évolution */}
        {chartData.length > 1 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Croissance totale</div>
              <div className={`text-xl font-bold ${metrics.totalGrowth >= 0 ? 'text-blue-700' : 'text-red-600'}`}>
                {formatPercentage(metrics.totalGrowth)}
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Meilleur point</div>
              <div className="text-xl font-bold text-blue-700">
                {metrics.bestSnapshot ? formatMontant(metrics.bestSnapshot.total) : '-'}
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Point précédent</div>
              <div className="text-xl font-bold text-blue-700">
                {formatMontant(metrics.previousTotal)}
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Nombre de points</div>
              <div className="text-xl font-bold text-blue-700">
                {chartData.length}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
