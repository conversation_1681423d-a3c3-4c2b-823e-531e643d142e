import React, { useState } from 'react';
import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { usePatrimoineEvolutionSimple } from '@/hooks/usePatrimoineEvolutionSimple';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { TrendingUp, TrendingDown, Calendar, Euro, Plus } from 'lucide-react';

interface PatrimoineEvolutionChartProps {
  className?: string;
}

export const PatrimoineEvolutionChart: React.FC<PatrimoineEvolutionChartProps> = ({ className }) => {
  const {
    snapshots,
    loading,
    getEvolutionMetrics,
    formatMontant,
    formatPercentage,
    hasData,
    upsertSnapshot
  } = usePatrimoineEvolutionSimple();

  // États pour l'édition en ligne (comme le tableau global)
  const [editingCell, setEditingCell] = useState<{ index: number; field: 'date' | 'total' } | null>(null);
  const [editingValue, setEditingValue] = useState('');

  const metrics = getEvolutionMetrics();

  // Créer 4 lignes natives (compléter avec des vides si nécessaire)
  const evolutionRows = Array.from({ length: 4 }, (_, index) => {
    const snapshot = snapshots[index];
    return snapshot || {
      id: `empty-${index}`,
      date: '',
      total: 0,
      isEmpty: true
    };
  });

  // Préparer les données pour le graphique (seulement les lignes non vides)
  const chartData = snapshots.map(snapshot => ({
    date: snapshot.date,
    dateFormatted: new Date(snapshot.date).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit'
    }),
    total: snapshot.total
  }));

  // Gérer l'édition d'une cellule (comme dans GlobalPatrimoineTable)
  const handleCellEdit = (index: number, field: 'date' | 'total', currentValue: any) => {
    setEditingCell({ index, field });
    if (field === 'date') {
      setEditingValue(currentValue || '');
    } else {
      setEditingValue(currentValue === 0 ? '' : currentValue.toString());
    }
  };

  // Sauvegarder l'édition
  const handleSaveEdit = async () => {
    if (!editingCell) return;

    const row = evolutionRows[editingCell.index];

    if (editingCell.field === 'date') {
      if (!editingValue) return;

      const currentTotal = row.isEmpty ? 0 : row.total;
      await upsertSnapshot(editingValue, currentTotal);
    } else {
      const newTotal = parseFloat(editingValue) || 0;
      const currentDate = row.isEmpty ? new Date().toISOString().split('T')[0] : row.date;

      await upsertSnapshot(currentDate, newTotal);
    }

    setEditingCell(null);
    setEditingValue('');
  };

  // Annuler l'édition
  const handleCancelEdit = () => {
    setEditingCell(null);
    setEditingValue('');
  };

  // Formater la date pour l'affichage
  const formatDate = (dateStr: string) => {
    if (!dateStr) return '';
    return new Date(dateStr).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
          <div className="flex items-center gap-3">
            {hasData && metrics.totalGrowth !== 0 && (
              <Badge
                variant={metrics.totalGrowth >= 0 ? "default" : "destructive"}
                className={metrics.totalGrowth >= 0 ? "bg-blue-100 text-blue-800" : "bg-red-100 text-red-800"}
              >
                {metrics.totalGrowth >= 0 ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {formatPercentage(metrics.totalGrowth)}
              </Badge>
            )}
            {hasData && (
              <Badge variant="outline" className="bg-blue-50 text-blue-800">
                <Euro className="h-3 w-3 mr-1" />
                {formatMontant(metrics.currentTotal)}
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Tableau d'évolution avec 4 lignes natives */}
        <div className="bg-white border-2 border-blue-200 rounded-lg overflow-hidden">
          <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-4 py-3 border-b border-blue-200">
            <h4 className="font-semibold text-blue-800 flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Points d'évolution temporelle
            </h4>
            <p className="text-xs text-blue-600 mt-1">Double-cliquez sur une cellule pour modifier</p>
          </div>

          <div className="divide-y divide-blue-100">
            {evolutionRows.map((row, index) => (
              <div key={index} className="grid grid-cols-2 hover:bg-blue-50/50 transition-colors">
                {/* Colonne Date */}
                <div className="p-4 border-r border-blue-100">
                  <div className="text-xs text-gray-500 mb-1 font-medium">Date</div>
                  {editingCell?.index === index && editingCell?.field === 'date' ? (
                    <Input
                      type="date"
                      value={editingValue}
                      onChange={(e) => setEditingValue(e.target.value)}
                      onBlur={handleSaveEdit}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSaveEdit();
                        if (e.key === 'Escape') handleCancelEdit();
                      }}
                      className="border-blue-300 focus:border-blue-500 text-sm"
                      autoFocus
                    />
                  ) : (
                    <div
                      className="text-sm font-medium text-gray-800 cursor-pointer hover:bg-blue-100 px-2 py-1 rounded transition-colors min-h-[28px] flex items-center"
                      onDoubleClick={() => handleCellEdit(index, 'date', row.date)}
                    >
                      {row.isEmpty ? (
                        <span className="text-gray-400 italic">Cliquez pour ajouter</span>
                      ) : (
                        formatDate(row.date)
                      )}
                    </div>
                  )}
                </div>

                {/* Colonne Total */}
                <div className="p-4">
                  <div className="text-xs text-gray-500 mb-1 font-medium">Total sous gestion</div>
                  {editingCell?.index === index && editingCell?.field === 'total' ? (
                    <Input
                      type="number"
                      value={editingValue}
                      onChange={(e) => setEditingValue(e.target.value)}
                      onBlur={handleSaveEdit}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSaveEdit();
                        if (e.key === 'Escape') handleCancelEdit();
                      }}
                      className="border-blue-300 focus:border-blue-500 text-sm"
                      placeholder="Montant en €"
                      autoFocus
                    />
                  ) : (
                    <div
                      className="text-sm font-semibold text-blue-700 cursor-pointer hover:bg-blue-100 px-2 py-1 rounded transition-colors min-h-[28px] flex items-center"
                      onDoubleClick={() => handleCellEdit(index, 'total', row.total)}
                    >
                      {row.isEmpty ? (
                        <span className="text-gray-400 italic font-normal">Cliquez pour ajouter</span>
                      ) : (
                        formatMontant(row.total)
                      )}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Graphique d'évolution */}
        {hasData && chartData.length > 0 && (
          <div className="bg-white border-2 border-blue-200 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-4 flex items-center gap-2">
              <TrendingUp className="h-4 w-4" />
              Courbe d'évolution
            </h4>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                  <defs>
                    <linearGradient id="patrimoineGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                      <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.05} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                  <XAxis
                    dataKey="dateFormatted"
                    stroke="#6B7280"
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                  />
                  <YAxis
                    stroke="#6B7280"
                    fontSize={12}
                    tickLine={false}
                    axisLine={false}
                    tickFormatter={(value) => {
                      if (value >= 1000000) {
                        return `${(value / 1000000).toFixed(1)}M€`;
                      } else if (value >= 1000) {
                        return `${(value / 1000).toFixed(0)}K€`;
                      }
                      return `${value}€`;
                    }}
                  />
                  <Tooltip
                    content={({ active, payload, label }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="bg-white p-3 border border-blue-200 rounded-lg shadow-lg">
                            <p className="font-medium text-blue-900">{label}</p>
                            <p className="text-blue-600 font-bold">
                              {formatMontant(payload[0].value as number)}
                            </p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Area
                    type="monotone"
                    dataKey="total"
                    stroke="#3B82F6"
                    strokeWidth={3}
                    fill="url(#patrimoineGradient)"
                    dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: '#3B82F6', strokeWidth: 2, fill: '#ffffff' }}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>
        )}

        {/* Métriques d'évolution */}
        {hasData && chartData.length > 1 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
            <div className="bg-blue-50 p-3 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-1 font-medium">Croissance totale</div>
              <div className={`text-lg font-bold ${metrics.totalGrowth >= 0 ? 'text-blue-700' : 'text-red-600'}`}>
                {formatPercentage(metrics.totalGrowth)}
              </div>
            </div>
            <div className="bg-blue-50 p-3 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-1 font-medium">Meilleur point</div>
              <div className="text-lg font-bold text-blue-700">
                {metrics.bestSnapshot ? formatMontant(metrics.bestSnapshot.total) : '-'}
              </div>
            </div>
            <div className="bg-blue-50 p-3 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-1 font-medium">Point précédent</div>
              <div className="text-lg font-bold text-blue-700">
                {formatMontant(metrics.previousTotal)}
              </div>
            </div>
            <div className="bg-blue-50 p-3 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-1 font-medium">Nombre de points</div>
              <div className="text-lg font-bold text-blue-700">
                {chartData.length}
              </div>
            </div>
          </div>
        )}

        {/* Message d'aide si pas de données */}
        {!hasData && (
          <div className="text-center py-8 text-blue-600">
            <Calendar className="h-12 w-12 mx-auto mb-3 text-blue-300" />
            <p className="font-medium mb-2">Aucun point d'évolution</p>
            <p className="text-sm text-blue-500">Double-cliquez sur les cellules ci-dessus pour commencer</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
