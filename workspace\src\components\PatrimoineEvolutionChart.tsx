import React from 'react';
import {
  AreaChart,
  Area,
  XAxis,
  YA<PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer
} from 'recharts';
import { usePatrimoineEvolutionSimple } from '@/hooks/usePatrimoineEvolutionSimple';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { TrendingUp, TrendingDown, Calendar, Euro } from 'lucide-react';

interface PatrimoineEvolutionChartProps {
  className?: string;
}

export const PatrimoineEvolutionChart: React.FC<PatrimoineEvolutionChartProps> = ({ className }) => {
  const {
    snapshots,
    loading,
    getEvolutionMetrics,
    formatMontant,
    formatPercentage,
    hasData
  } = usePatrimoineEvolutionSimple();

  const metrics = getEvolutionMetrics();

  // Préparer les données pour le graphique
  const chartData = snapshots.map(snapshot => ({
    date: snapshot.date,
    dateFormatted: new Date(snapshot.date).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: '2-digit'
    }),
    total: snapshot.total,
    commentaire: snapshot.commentaire
  }));

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hasData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-lg font-medium mb-2">Aucune donnée d'évolution</p>
            <p className="text-sm">Ajoutez des snapshots dans le tableau global pour voir l'évolution</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-600" />
            Évolution du Patrimoine
          </CardTitle>
          <div className="flex items-center gap-3">
            {metrics.totalGrowth !== 0 && (
              <Badge 
                variant={metrics.totalGrowth >= 0 ? "default" : "destructive"}
                className={metrics.totalGrowth >= 0 ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}
              >
                {metrics.totalGrowth >= 0 ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {formatPercentage(metrics.totalGrowth)}
              </Badge>
            )}
            <Badge variant="outline" className="bg-blue-50 text-blue-800">
              <Euro className="h-3 w-3 mr-1" />
              {formatMontant(metrics.currentTotal)}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {chartData.length === 1 ? (
          // Affichage spécial pour un seul point
          <div className="text-center py-8">
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <div className="flex items-center justify-center gap-2 text-green-700 mb-4">
                <TrendingUp className="h-6 w-6" />
                <span className="text-lg font-medium">Point de départ établi</span>
              </div>
              <div className="text-3xl font-bold text-green-800 mb-2">
                {formatMontant(chartData[0].total)}
              </div>
              <div className="text-sm text-green-600">
                {chartData[0].dateFormatted}
              </div>
              {chartData[0].commentaire && (
                <div className="text-xs text-green-600 mt-2 italic">
                  {chartData[0].commentaire}
                </div>
              )}
              <div className="text-xs text-green-600 mt-4">
                La courbe évoluera automatiquement avec les prochains snapshots
              </div>
            </div>
          </div>
        ) : (
          // Graphique normal avec plusieurs points
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={chartData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                <defs>
                  <linearGradient id="patrimoineGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#10B981" stopOpacity={0.3} />
                    <stop offset="95%" stopColor="#10B981" stopOpacity={0.05} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis 
                  dataKey="dateFormatted" 
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis 
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => {
                    if (value >= 1000000) {
                      return `${(value / 1000000).toFixed(1)}M€`;
                    } else if (value >= 1000) {
                      return `${(value / 1000).toFixed(0)}K€`;
                    }
                    return `${value}€`;
                  }}
                />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      const data = payload[0].payload;
                      return (
                        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
                          <p className="font-medium text-gray-900">{label}</p>
                          <p className="text-green-600 font-bold">
                            {formatMontant(payload[0].value as number)}
                          </p>
                          {data.commentaire && (
                            <p className="text-xs text-gray-500 mt-1 italic">
                              {data.commentaire}
                            </p>
                          )}
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="total"
                  stroke="#10B981"
                  strokeWidth={3}
                  fill="url(#patrimoineGradient)"
                  dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                  activeDot={{ r: 6, stroke: '#10B981', strokeWidth: 2, fill: '#ffffff' }}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        )}

        {/* Métriques d'évolution */}
        {chartData.length > 1 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6 pt-4 border-t border-gray-200">
            <div className="text-center">
              <div className="text-xs text-gray-500 mb-1">Croissance totale</div>
              <div className={`text-sm font-bold ${metrics.totalGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {formatPercentage(metrics.totalGrowth)}
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs text-gray-500 mb-1">Meilleure période</div>
              <div className="text-sm font-bold text-blue-600">
                {metrics.bestSnapshot ? formatMontant(metrics.bestSnapshot.total) : '-'}
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs text-gray-500 mb-1">Période précédente</div>
              <div className="text-sm font-bold text-gray-600">
                {formatMontant(metrics.previousTotal)}
              </div>
            </div>
            <div className="text-center">
              <div className="text-xs text-gray-500 mb-1">Snapshots</div>
              <div className="text-sm font-bold text-purple-600">
                {chartData.length}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
