import React, { useState, useMemo } from 'react';
import {
  AreaChart,
  Area,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import { usePatrimoineEvolutionSimple } from '@/hooks/usePatrimoineEvolutionSimple';
import { usePatrimoineEvolutionFournisseurs } from '@/hooks/usePatrimoineEvolutionFournisseurs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { TrendingUp, TrendingDown, Calendar, Euro, Users, BarChart3, ChevronUp, ChevronDown } from 'lucide-react';

interface PatrimoineEvolutionChartProps {
  className?: string;
  fournisseurs?: Array<{ id: string; nom: string; total: number }>;
}

export const PatrimoineEvolutionChart: React.FC<PatrimoineEvolutionChartProps> = ({
  className,
  fournisseurs = []
}) => {
  const [showFournisseurs, setShowFournisseurs] = useState(false);
  const [selectedFournisseurs, setSelectedFournisseurs] = useState<string[]>([]);

  const {
    snapshots,
    loading: loadingGlobal,
    getEvolutionMetrics,
    formatMontant,
    formatPercentage,
    hasData
  } = usePatrimoineEvolutionSimple();

  const {
    getEvolutionsByDate,
    getChartData,
    loading: loadingFournisseurs,
    formatMontant: formatMontantFournisseur
  } = usePatrimoineEvolutionFournisseurs();

  const loading = loadingGlobal || loadingFournisseurs;
  const metrics = getEvolutionMetrics();

  // Couleurs pour les fournisseurs
  const fournisseurColors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
  ];

  // Préparer les données combinées pour le graphique
  const chartData = useMemo(() => {
    if (!showFournisseurs || selectedFournisseurs.length === 0) {
      // Mode global uniquement - données simplifiées
      return snapshots.map(snapshot => ({
        date: snapshot.date,
        dateFormatted: new Date(snapshot.date).toLocaleDateString('fr-FR', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit'
        }),
        total: snapshot.total
      }));
    }

    // Mode avec fournisseurs sélectionnés - données enrichies
    const fournisseurData = getChartData(selectedFournisseurs);
    const globalData = snapshots.reduce((acc, snapshot) => {
      acc[snapshot.date] = snapshot.total;
      return acc;
    }, {} as Record<string, number>);

    // Combiner toutes les dates disponibles
    const allDates = new Set([
      ...snapshots.map(s => s.date),
      ...fournisseurData.map(f => f.date)
    ]);

    return Array.from(allDates)
      .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
      .map(date => {
        const fournisseurEntry = fournisseurData.find(f => f.date === date);
        const result: any = {
          date,
          dateFormatted: new Date(date).toLocaleDateString('fr-FR', {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit'
          }),
          total: globalData[date] || 0
        };

        // Ajouter les données de chaque fournisseur sélectionné
        selectedFournisseurs.forEach(fournisseurId => {
          const fournisseur = fournisseurs.find(f => f.id === fournisseurId);
          if (fournisseur) {
            // Utiliser le nom du fournisseur comme clé pour les données
            const montant = fournisseurEntry?.[fournisseurId] || 0;
            result[fournisseur.nom] = montant;

            // Ajouter aussi une version courte du nom pour l'affichage
            const shortName = fournisseur.nom.length > 15
              ? fournisseur.nom.substring(0, 12) + '...'
              : fournisseur.nom;
            result[`${fournisseur.nom}_short`] = shortName;
          }
        });

        return result;
      });
  }, [snapshots, showFournisseurs, selectedFournisseurs, getChartData, fournisseurs]);

  const handleFournisseurToggle = (fournisseurId: string) => {
    setSelectedFournisseurs(prev =>
      prev.includes(fournisseurId)
        ? prev.filter(id => id !== fournisseurId)
        : [...prev, fournisseurId]
    );
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!hasData) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12 text-blue-600">
            <Calendar className="h-16 w-16 mx-auto mb-4 text-blue-300" />
            <p className="text-lg font-medium mb-2">Aucun point d'évolution</p>
            <p className="text-sm text-blue-500">Ajoutez des points dans le tableau global pour voir l'évolution</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-blue-600" />
            Évolution du Patrimoine
          </CardTitle>
          <div className="flex items-center gap-3">
            {metrics.totalGrowth !== 0 && (
              <Badge
                variant={metrics.totalGrowth >= 0 ? "default" : "destructive"}
                className={metrics.totalGrowth >= 0 ? "bg-blue-100 text-blue-800" : "bg-red-100 text-red-800"}
              >
                {metrics.totalGrowth >= 0 ? (
                  <TrendingUp className="h-3 w-3 mr-1" />
                ) : (
                  <TrendingDown className="h-3 w-3 mr-1" />
                )}
                {formatPercentage(metrics.totalGrowth)}
              </Badge>
            )}
            <Badge variant="outline" className="bg-blue-50 text-blue-800">
              <Euro className="h-3 w-3 mr-1" />
              {formatMontant(metrics.currentTotal)}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Sélecteur de fournisseurs - Élément perfectionné en haut à gauche */}
        {fournisseurs.length > 0 && (
          <div className="relative">
            <div className="absolute top-3 left-3 z-20 bg-white/98 backdrop-blur-md border border-blue-200 rounded-xl p-3 shadow-lg min-w-[200px]">
              <div className="flex items-center justify-between mb-3">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFournisseurs(!showFournisseurs)}
                  className="h-7 px-3 text-xs font-medium text-blue-700 hover:bg-blue-50 hover:text-blue-800 transition-all"
                >
                  <Users className="h-3 w-3 mr-1.5" />
                  Courbes Fournisseurs
                  {showFournisseurs ? (
                    <ChevronUp className="h-3 w-3 ml-1" />
                  ) : (
                    <ChevronDown className="h-3 w-3 ml-1" />
                  )}
                </Button>
                {selectedFournisseurs.length > 0 && (
                  <Badge variant="default" className="text-xs bg-blue-100 text-blue-800 border-blue-200">
                    {selectedFournisseurs.length}
                  </Badge>
                )}
              </div>

              {showFournisseurs && (
                <div className="space-y-2">
                  {/* Boutons de sélection rapide */}
                  <div className="flex gap-1 mb-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedFournisseurs(fournisseurs.map(f => f.id))}
                      className="h-6 px-2 text-xs border-blue-200 text-blue-600 hover:bg-blue-50"
                    >
                      Tout
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setSelectedFournisseurs([])}
                      className="h-6 px-2 text-xs border-gray-200 text-gray-600 hover:bg-gray-50"
                    >
                      Aucun
                    </Button>
                  </div>

                  {/* Liste des fournisseurs avec scroll */}
                  <div className="space-y-1.5 max-h-40 overflow-y-auto pr-1 scrollbar-thin scrollbar-thumb-blue-200 scrollbar-track-transparent">
                    {fournisseurs.map((fournisseur, index) => (
                      <div
                        key={fournisseur.id}
                        className="flex items-center space-x-2.5 p-1.5 rounded-lg hover:bg-blue-50/50 transition-colors group"
                      >
                        <Checkbox
                          id={`fournisseur-${fournisseur.id}`}
                          checked={selectedFournisseurs.includes(fournisseur.id)}
                          onCheckedChange={() => handleFournisseurToggle(fournisseur.id)}
                          className="h-3.5 w-3.5 border-blue-300 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
                        />
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          <div
                            className="w-3 h-3 rounded-full border border-white shadow-sm flex-shrink-0"
                            style={{ backgroundColor: fournisseurColors[index % fournisseurColors.length] }}
                          />
                          <label
                            htmlFor={`fournisseur-${fournisseur.id}`}
                            className="text-xs font-medium cursor-pointer truncate text-gray-700 group-hover:text-blue-700 transition-colors"
                            title={`${fournisseur.nom} - ${formatMontant(fournisseur.total)}`}
                          >
                            {fournisseur.nom}
                          </label>
                          <span className="text-xs text-gray-500 font-mono ml-auto">
                            {formatMontant(fournisseur.total).replace(' €', '€')}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Indicateur de mode */}
                  {selectedFournisseurs.length > 0 && (
                    <div className="pt-2 border-t border-blue-100">
                      <div className="text-xs text-blue-600 font-medium flex items-center gap-1">
                        <BarChart3 className="h-3 w-3" />
                        Mode multi-courbes actif
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Graphique d'évolution */}
        <div className="h-80 relative">
          <ResponsiveContainer width="100%" height="100%">
            {showFournisseurs && selectedFournisseurs.length > 0 ? (
              <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis
                  dataKey="dateFormatted"
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => {
                    if (value >= 1000000) {
                      return `${(value / 1000000).toFixed(1)}M€`;
                    } else if (value >= 1000) {
                      return `${(value / 1000).toFixed(0)}K€`;
                    }
                    return `${value}€`;
                  }}
                />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      // Trier les entrées : Total Global en premier, puis fournisseurs par montant décroissant
                      const sortedPayload = [...payload].sort((a, b) => {
                        if (a.dataKey === 'total') return -1;
                        if (b.dataKey === 'total') return 1;
                        return (b.value as number) - (a.value as number);
                      });

                      return (
                        <div className="bg-white/98 backdrop-blur-sm p-4 border border-blue-200 rounded-xl shadow-xl max-w-xs">
                          <p className="font-semibold text-blue-900 mb-3 text-sm border-b border-blue-100 pb-2">
                            📅 {label}
                          </p>
                          <div className="space-y-2">
                            {sortedPayload.map((entry, index) => {
                              const isTotal = entry.dataKey === 'total';
                              const value = entry.value as number;

                              if (value === 0 && !isTotal) return null;

                              return (
                                <div key={`${entry.dataKey}-${index}`} className={`flex items-center justify-between gap-3 ${isTotal ? 'bg-gray-50 -mx-1 px-1 py-1 rounded' : ''}`}>
                                  <div className="flex items-center gap-2 min-w-0 flex-1">
                                    <div
                                      className={`w-3 h-3 rounded-full border border-white shadow-sm flex-shrink-0 ${isTotal ? 'border-2' : ''}`}
                                      style={{ backgroundColor: entry.color }}
                                    />
                                    <span className={`text-xs truncate ${isTotal ? 'font-bold text-gray-800' : 'font-medium text-gray-700'}`}>
                                      {isTotal ? '🏆 Total Global' : entry.dataKey}
                                    </span>
                                  </div>
                                  <span className={`text-xs font-bold whitespace-nowrap ${isTotal ? 'text-gray-800' : 'text-gray-600'}`}>
                                    {formatMontant(value)}
                                  </span>
                                </div>
                              );
                            })}
                          </div>

                          {/* Calcul du pourcentage si plus d'un fournisseur */}
                          {sortedPayload.length > 2 && (
                            <div className="mt-3 pt-2 border-t border-blue-100">
                              <div className="text-xs text-blue-600 font-medium">
                                💡 {sortedPayload.length - 1} fournisseur{sortedPayload.length > 2 ? 's' : ''} affiché{sortedPayload.length > 2 ? 's' : ''}
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Legend />

                {/* Ligne du total global - style premium */}
                <Line
                  type="monotone"
                  dataKey="total"
                  stroke="#1F2937"
                  strokeWidth={4}
                  strokeDasharray="0"
                  dot={{
                    fill: '#1F2937',
                    strokeWidth: 3,
                    r: 5,
                    stroke: '#ffffff'
                  }}
                  activeDot={{
                    r: 8,
                    stroke: '#1F2937',
                    strokeWidth: 3,
                    fill: '#ffffff',
                    filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'
                  }}
                  name="Total Global"
                />

                {/* Lignes des fournisseurs sélectionnés - styles différenciés */}
                {selectedFournisseurs.map((fournisseurId, index) => {
                  const fournisseur = fournisseurs.find(f => f.id === fournisseurId);
                  if (!fournisseur) return null;

                  const color = fournisseurColors[index % fournisseurColors.length];
                  const isEven = index % 2 === 0;

                  return (
                    <Line
                      key={fournisseurId}
                      type="monotone"
                      dataKey={fournisseur.nom}
                      stroke={color}
                      strokeWidth={3}
                      strokeDasharray={isEven ? "0" : "5 5"}
                      dot={{
                        fill: color,
                        strokeWidth: 2,
                        r: 4,
                        stroke: '#ffffff'
                      }}
                      activeDot={{
                        r: 7,
                        stroke: color,
                        strokeWidth: 3,
                        fill: '#ffffff',
                        filter: 'drop-shadow(0 1px 3px rgba(0,0,0,0.1))'
                      }}
                      name={fournisseur.nom.length > 20 ? fournisseur.nom.substring(0, 17) + '...' : fournisseur.nom}
                      connectNulls={false}
                    />
                  );
                })}
              </LineChart>
            ) : (
              <AreaChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                <defs>
                  <linearGradient id="patrimoineGradient" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.3} />
                    <stop offset="95%" stopColor="#3B82F6" stopOpacity={0.05} />
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#E5E7EB" />
                <XAxis
                  dataKey="dateFormatted"
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                />
                <YAxis
                  stroke="#6B7280"
                  fontSize={12}
                  tickLine={false}
                  axisLine={false}
                  tickFormatter={(value) => {
                    if (value >= 1000000) {
                      return `${(value / 1000000).toFixed(1)}M€`;
                    } else if (value >= 1000) {
                      return `${(value / 1000).toFixed(0)}K€`;
                    }
                    return `${value}€`;
                  }}
                />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      return (
                        <div className="bg-white p-3 border border-blue-200 rounded-lg shadow-lg">
                          <p className="font-medium text-blue-900">{label}</p>
                          <p className="text-blue-600 font-bold">
                            {formatMontant(payload[0].value as number)}
                          </p>
                        </div>
                      );
                    }
                    return null;
                  }}
                />
                <Area
                  type="monotone"
                  dataKey="total"
                  stroke="#3B82F6"
                  strokeWidth={3}
                  fill="url(#patrimoineGradient)"
                  dot={{ fill: '#3B82F6', strokeWidth: 2, r: 5 }}
                  activeDot={{ r: 7, stroke: '#3B82F6', strokeWidth: 2, fill: '#ffffff' }}
                />
              </AreaChart>
            )}
          </ResponsiveContainer>
        </div>

        {/* Métriques d'évolution */}
        {chartData.length > 1 && (
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Croissance totale</div>
              <div className={`text-xl font-bold ${metrics.totalGrowth >= 0 ? 'text-blue-700' : 'text-red-600'}`}>
                {formatPercentage(metrics.totalGrowth)}
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Meilleur point</div>
              <div className="text-xl font-bold text-blue-700">
                {metrics.bestSnapshot ? formatMontant(metrics.bestSnapshot.total) : '-'}
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Point précédent</div>
              <div className="text-xl font-bold text-blue-700">
                {formatMontant(metrics.previousTotal)}
              </div>
            </div>
            <div className="bg-blue-50 p-4 rounded-lg text-center border border-blue-200">
              <div className="text-xs text-blue-600 mb-2 font-medium">Nombre de points</div>
              <div className="text-xl font-bold text-blue-700">
                {chartData.length}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
