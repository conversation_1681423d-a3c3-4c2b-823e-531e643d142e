# Demande de mise en liste blanche - WeMa Tracker

## Informations sur l'application

**Nom** : WeMa Tracker  
**Version** : 0.1.0  
**Éditeur** : WeMa  
**Type** : Application de gestion de dossiers clients  
**Framework** : Tauri (Rust + React)  

## Hashes de l'application
- **SHA256** : 63f657035b852b63254ce2a99227618364d03f6f2f7494b868d6a094595d6f7
- **MD5** : [À compléter avec le hash actuel]
- **SHA1** : [À compléter avec le hash actuel]

## Raison de la demande

Notre application légitime **WeMa Tracker** est incorrectement détectée comme malveillante par votre antivirus. Il s'agit d'un **faux positif**.

### Preuves de légitimité :

1. **Application d'entreprise** : Développée pour WeMa, usage interne uniquement
2. **Code source** : Application open-source basée sur Tauri (framework Rust)
3. **Fonctionnalités légitimes** :
   - Gestion de dossiers clients
   - Interface web sécurisée
   - Chiffrement des données sensibles (AES-256)
   - Conformité RGPD

4. **Analyse Zenbox** : Score 2/100 (non malveillant)
5. **Comportements détectés** : Tous légitimes (installation, requêtes HTTPS, chiffrement)

### Composants détectés à tort :
- `nsis_tauri_utils.dll` : Bibliothèque Tauri pour l'installation
- `uninstall.exe` : Désinstalleur standard NSIS

## Contacts pour vérification

**Développeur** : WeMa  
**Email** : <EMAIL>  
**Site web** : https://wema.fr  

## Demande

Nous demandons respectueusement l'ajout de notre application à votre liste blanche pour éviter les faux positifs futurs.

---

## URLs de soumission par antivirus :

### Bkav Pro
- **URL** : https://www.bkav.com/support/false-positive-report
- **Email** : <EMAIL>

### Autres antivirus majeurs :
- **Microsoft Defender** : https://www.microsoft.com/en-us/wdsi/filesubmission
- **Kaspersky** : https://support.kaspersky.com/virlab
- **Avast** : https://www.avast.com/false-positive-file-form.php
- **AVG** : https://www.avg.com/false-positive-file-form
- **Bitdefender** : https://www.bitdefender.com/submit/
- **ESET** : https://support.eset.com/en/submit-sample
- **Trend Micro** : https://www.trendmicro.com/en_us/about/legal/detection-reevaluation.html
