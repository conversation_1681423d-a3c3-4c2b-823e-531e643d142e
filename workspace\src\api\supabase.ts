import { supabase } from "@/integrations/supabase/client";
import { Profile } from "@/types";
import { encryptText, safeDecrypt } from '@/utils/encryption';

// Utiliser les variables d'environnement pour les clés sensibles
const _SUPABASE_API_KEY = import.meta.env.VITE_SUPABASE_API_KEY;
const _SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;

// Fonction pour créer un profil directement avec supabase (utilise la table assignees)
export const createProfileWithFetch = async (name: string): Promise<Profile | null> => {
  try {
    console.log('Tentative de création de profil dans la table assignees pour:', name);

    // Les noms des profils ne sont pas chiffrés selon la configuration
    // Utiliser directement le client supabase pour insérer dans la table assignees
    const { data, error } = await supabase
      .from('assignees')
      .insert({ name })
      .select()
      .single();

    if (error) {
      console.error('Erreur lors de la création du profil dans assignees:', error);
      throw error;
    }

    console.log('Profil créé avec succès dans la table assignees:', data.id);

    // Convertir l'assignee en profil
    const profile: Profile = {
      id: data.id,
      name: data.name,
      created_at: data.created_at
    };

    return profile;
  } catch (error) {
    console.error('Erreur lors de la création du profil:', error);
    return null;
  }
};

// Fonction pour créer un ping direct entre utilisateurs (utilise la table pings)
export const createPing = async (
  fromUserId: string,
  toUserId: string,
  message: string
): Promise<boolean> => {
  try {
    console.log('Création d\'un ping de', fromUserId, 'à', toUserId);

    // Chiffrer le message pour protéger les communications
    const encryptedMessage = await encryptText(message);

    const { error } = await supabase
      .from('pings')
      .insert({
        from_user_id: fromUserId,
        to_user_id: toUserId,
        message: encryptedMessage, // Message chiffré
        read: false
      });

    if (error) {
      console.error('Erreur lors de la création du ping:', error);
      throw error;
    }

    console.log('Ping créé avec succès');
    return true;
  } catch (error) {
    console.error('Error creating ping:', error);
    return false;
  }
};

// Fonction pour récupérer les pings d'un utilisateur (utilise la table pings)
export const getUserPings = async (userId: string) => {
  try {
    console.log('Récupération des pings pour l\'utilisateur:', userId);

    // Récupérer les pings
    const { data, error } = await supabase
      .from('pings')
      .select('*')
      .eq('to_user_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Erreur lors de la récupération des pings:', error);
      throw error;
    }

    // Récupérer les noms des expéditeurs
    const fromUserIds = [...new Set(data?.map(ping => ping.from_user_id) || [])];
    const senderNames: Record<string, string> = {};

    if (fromUserIds.length > 0) {
      const { data: assigneesData, error: assigneesError } = await supabase
        .from('assignees')
        .select('id, name')
        .in('id', fromUserIds);

      if (!assigneesError && assigneesData) {
        // Les noms des assignees ne sont pas chiffrés selon la configuration
        for (const assignee of assigneesData) {
          senderNames[assignee.id] = assignee.name;
        }
      }
    }

    console.log('Pings récupérés avec succès:', data?.length || 0);

    // Déchiffrer les messages et formater les pings
    const formattedPings = await Promise.all((data || []).map(async ping => {
      const decryptedMessage = await safeDecrypt(ping.message);

      return {
        id: ping.id,
        from_user_id: ping.from_user_id,
        to_user_id: ping.to_user_id,
        message: decryptedMessage, // Message déchiffré
        read: ping.read,
        created_at: ping.created_at,
        from_user: {
          name: senderNames[ping.from_user_id] || 'Utilisateur inconnu'
        }
      };
    }));

    return formattedPings;
  } catch (error) {
    console.error('Error fetching pings:', error);
    return [];
  }
};

// Fonction pour marquer un ping comme lu (utilise la table pings)
// Note: Cette fonction ne marque le ping comme lu que pour le profil courant
// en utilisant une table de lecture séparée
export const markPingAsRead = async (pingId: string, currentProfileId: string): Promise<boolean> => {
  try {
    console.log('Marquage du ping comme lu pour le profil:', currentProfileId, 'ping:', pingId);

    // Vérifier si le ping existe et appartient au profil courant
    const { data: pingData, error: pingError } = await supabase
      .from('pings')
      .select('*')
      .eq('id', pingId)
      .eq('to_user_id', currentProfileId)
      .single();

    if (pingError) {
      console.error('Erreur lors de la vérification du ping:', pingError);
      throw pingError;
    }

    if (!pingData) {
      console.error('Le ping n\'existe pas ou n\'appartient pas au profil courant');
      return false;
    }

    // Marquer le ping comme lu uniquement pour ce profil
    const { error } = await supabase
      .from('pings')
      .update({ read: true })
      .eq('id', pingId)
      .eq('to_user_id', currentProfileId);

    if (error) {
      console.error('Erreur lors du marquage du ping comme lu:', error);
      throw error;
    }

    console.log('Ping marqué comme lu avec succès pour le profil:', currentProfileId);
    return true;
  } catch (error) {
    console.error('Error marking ping as read:', error);
    return false;
  }
};

// Fonction pour supprimer un profil (utilise la table assignees)
export const deleteProfile = async (profileId: string): Promise<boolean> => {
  try {
    console.log('Suppression du profil:', profileId);

    // Vérifier si le profil existe
    const { data: profileData, error: profileError } = await supabase
      .from('assignees')
      .select('id')
      .eq('id', profileId)
      .single();

    if (profileError) {
      console.error('Erreur lors de la vérification du profil:', profileError);
      return false;
    }

    if (!profileData) {
      console.error('Le profil n\'existe pas');
      return false;
    }

    // Supprimer les pings associés au profil
    const { error: pingsError } = await supabase
      .from('pings')
      .delete()
      .or(`from_user_id.eq.${profileId},to_user_id.eq.${profileId}`);

    if (pingsError) {
      console.error('Erreur lors de la suppression des pings:', pingsError);
      // Continuer malgré l'erreur
    }

    // Supprimer les notifications associées au profil
    const { error: notificationsError } = await supabase
      .from('notifications')
      .delete()
      .eq('user_id', profileId);

    if (notificationsError) {
      console.error('Erreur lors de la suppression des notifications:', notificationsError);
      // Continuer malgré l'erreur
    }

    // Supprimer les assignations d'étapes associées au profil
    const { error: assignmentsError } = await supabase
      .from('step_assignments')
      .delete()
      .eq('assignee_id', profileId);

    if (assignmentsError) {
      console.error('Erreur lors de la suppression des assignations d\'étapes:', assignmentsError);
      // Continuer malgré l'erreur
    }

    // Note: Nous n'utilisons plus la table profiles

    // Supprimer le profil de la table assignees
    const { error } = await supabase
      .from('assignees')
      .delete()
      .eq('id', profileId);

    if (error) {
      console.error('Erreur lors de la suppression du profil de la table assignees:', error);
      throw error;
    }

    console.log('Profil supprimé avec succès');
    return true;
  } catch (error) {
    console.error('Error deleting profile:', error);
    return false;
  }
};