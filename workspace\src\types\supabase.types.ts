/**
 * Types spécifiques à Supabase (tables de base de données)
 */

import { UUID, ISODateString, Status } from './client.types';

/**
 * Interface pour la table clients dans Supabase
 */
export interface SupabaseClient {
  id: UUID;
  name: string; // Nom chiffré
  due_date: ISODateString | null;
  completed: boolean | null;
  created_at?: ISODateString;
}

/**
 * Interface pour la table steps dans Supabase
 */
export interface SupabaseStep {
  id: UUID;
  client_id: UUID;
  name: string; // Nom chiffré
  status: Status;
  received_date: ISODateString | null;
  comment: string; // Commentaire chiffré
  created_at?: ISODateString;
}

/**
 * Interface pour la table assignees dans Supabase
 */
export interface SupabaseAssignee {
  id: UUID;
  name: string; // Nom chiffré
  created_at?: ISODateString;
}

/**
 * Interface pour la table step_assignments dans Supabase
 */
export interface SupabaseStepAssignment {
  id: UUID;
  step_id: UUID;
  assignee_id: UUID;
  is_urgent_notified: boolean;
  created_at?: ISODateString;
}

/**
 * Interface pour la table profiles dans Supabase
 */
export interface SupabaseProfile {
  id: UUID;
  name: string;
  avatar_url?: string;
  created_at?: ISODateString;
}

/**
 * Interface pour la table notifications dans Supabase
 */
export interface SupabaseNotification {
  id: UUID;
  user_id: UUID;
  step_id: UUID;
  created_by: UUID;
  message: string;
  read: boolean;
  created_at: ISODateString;
}

/**
 * Interface pour la table pings dans Supabase
 */
export interface SupabasePing {
  id: UUID;
  from_user_id: UUID;
  to_user_id: UUID;
  message: string;
  read: boolean;
  created_at: ISODateString;
}

/**
 * Interface pour la table compliance_documents dans Supabase
 */
export interface SupabaseComplianceDocument {
  id: UUID;
  client_id: UUID;
  document_type: string;
  status: 'manquant' | 'transmis' | 'signe_valide';
  created_at?: ISODateString;
  updated_at?: ISODateString;
}

/**
 * Interface pour la table data_access_logs dans Supabase
 */
export interface SupabaseDataAccessLog {
  id: UUID;
  operation: string;
  table_name: string;
  record_id: UUID | null;
  user_id: UUID | null;
  details: string | null;
  timestamp: ISODateString;
  ip_address: string | null;
}

/**
 * Types pour les réponses Supabase
 */
export interface SupabaseResponse<T> {
  data: T | null;
  error: SupabaseError | null;
}

export interface SupabaseError {
  message: string;
  details?: string;
  hint?: string;
  code?: string;
}

/**
 * Types pour les requêtes Supabase
 */
export interface SupabaseInsert<T> {
  [K in keyof T]?: T[K];
}

export interface SupabaseUpdate<T> {
  [K in keyof T]?: T[K];
}

/**
 * Types pour les jointures Supabase
 */
export interface SupabaseStepWithAssignments extends SupabaseStep {
  step_assignments: Array<{
    assignee_id: UUID;
    assignees: SupabaseAssignee;
  }>;
}

export interface SupabaseClientWithSteps extends SupabaseClient {
  steps: SupabaseStepWithAssignments[];
}

/**
 * Types pour les fonctions Supabase
 */
export interface SupabasePurgeResult {
  notifications_purged: number;
  pings_purged: number;
  logs_purged: number;
  clients_anonymized: number;
}

/**
 * Types pour les filtres Supabase
 */
export type SupabaseFilter = {
  column: string;
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'ilike' | 'in' | 'is';
  value: any;
};

export type SupabaseOrderBy = {
  column: string;
  ascending?: boolean;
  nullsFirst?: boolean;
};

/**
 * Types pour les souscriptions temps réel
 */
export type SupabaseRealtimeEvent = 'INSERT' | 'UPDATE' | 'DELETE';

export interface SupabaseRealtimePayload<T = any> {
  eventType: SupabaseRealtimeEvent;
  new: T;
  old: T;
  errors: any[];
}

export type SupabaseRealtimeCallback<T = any> = (payload: SupabaseRealtimePayload<T>) => void;

/**
 * Configuration pour les souscriptions
 */
export interface SupabaseSubscriptionConfig {
  table: string;
  event?: SupabaseRealtimeEvent | '*';
  filter?: string;
  callback: SupabaseRealtimeCallback;
}

/**
 * Types pour les politiques RLS
 */
export interface SupabaseRLSPolicy {
  name: string;
  table: string;
  command: 'SELECT' | 'INSERT' | 'UPDATE' | 'DELETE' | 'ALL';
  roles: string[];
  using?: string;
  with_check?: string;
}

/**
 * Types pour les migrations
 */
export interface SupabaseMigration {
  version: string;
  name: string;
  sql: string;
  applied_at?: ISODateString;
}

/**
 * Types pour la configuration de sécurité
 */
export interface SupabaseSecurityConfig {
  rls_enabled: boolean;
  policies: SupabaseRLSPolicy[];
  jwt_secret?: string;
  anon_key: string;
  service_role_key?: string;
}

/**
 * Types pour les métriques de performance Supabase
 */
export interface SupabasePerformanceMetrics {
  query_count: number;
  average_response_time: number;
  error_rate: number;
  connection_count: number;
  cache_hit_rate: number;
}

/**
 * Types pour la gestion des erreurs Supabase
 */
export type SupabaseErrorCode = 
  | 'PGRST116' // Row not found
  | 'PGRST301' // Ambiguous column
  | '23505'    // Unique violation
  | '23503'    // Foreign key violation
  | '42P01'    // Undefined table
  | '42703';   // Undefined column

export interface SupabaseErrorDetails {
  code: SupabaseErrorCode;
  message: string;
  details: string;
  hint?: string;
}
