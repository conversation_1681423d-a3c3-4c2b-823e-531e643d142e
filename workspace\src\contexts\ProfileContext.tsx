/**
 * Contexte pour la gestion des profils utilisateurs
 * Fournit des fonctions pour créer, supprimer et sélectionner des profils
 */

import React, { createContext, useState, useContext, ReactNode, useEffect } from 'react';
import { Profile } from '@/types';
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

import { handleError, ErrorCode, createAppError } from "@/services/errorService";
import { logCreate, logRead, logDelete } from "@/utils/secure-logging";

interface ProfileContextProps {
  profiles: Profile[];
  currentProfile: Profile | null;
  loading: boolean;
  createProfile: (name: string) => Promise<void>;
  deleteProfile: (profileId: string) => Promise<boolean>;
  selectProfile: (profile: Profile) => void;
}

const ProfileContext = createContext<ProfileContextProps | undefined>(undefined);

/**
 * Provider pour le contexte des profils
 */
export const ProfileProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [profiles, setProfiles] = useState<Profile[]>([]);
  const [currentProfile, setCurrentProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);



  /**
   * Charge les profils depuis la base de données
   */
  useEffect(() => {
    const loadProfiles = async () => {
      try {
        setLoading(true);

        // Journaliser l'opération
        logRead('assignees', null, null, 'Chargement des profils');

        // Charger les profils depuis la table assignees
        const { data: assigneesData, error: assigneesError } = await supabase
          .from('assignees')
          .select('*')
          .order('name', { ascending: true });

        if (assigneesError) {
          throw createAppError(
            `Erreur lors du chargement des profils: ${assigneesError.message}`,
            ErrorCode.DB_QUERY_ERROR
          );
        }

        // Les noms des profils ne sont pas chiffrés selon la configuration
        const processedProfiles = assigneesData.map(a => ({
          id: a.id,
          name: a.name,
          created_at: a.created_at
        }));

        setProfiles(processedProfiles as Profile[]);

        // Restaurer le profil sélectionné
        const lastProfileId = localStorage.getItem('currentProfileId');
        if (lastProfileId) {
          const savedProfile = processedProfiles.find(p => p.id === lastProfileId);
          if (savedProfile) {
            setCurrentProfile(savedProfile);
          }
        }
      } catch (error) {
        handleError(error, 'ProfileContext.loadProfiles');
      } finally {
        setLoading(false);
      }
    };

    loadProfiles();
  }, []);

  /**
   * Crée un nouveau profil
   * @param name - Nom du profil
   */
  const createProfile = async (name: string) => {
    try {
      // Journaliser l'opération
      logCreate('assignees', null, currentProfile?.id, 'Création d\'un profil');

      // Importer la fonction de création de profil avec fetch
      const { createProfileWithFetch } = await import('@/api/supabase');

      // Créer le profil en utilisant la méthode qui gère correctement le chiffrement
      const newProfile = await createProfileWithFetch(name);

      if (!newProfile) {
        throw createAppError(
          `Erreur lors de la création du profil`,
          ErrorCode.DB_QUERY_ERROR
        );
      }

      setProfiles(prev => [...prev, newProfile]);
      setCurrentProfile(newProfile);
      localStorage.setItem('currentProfileId', newProfile.id);

      toast.success("Profil créé avec succès");
    } catch (error) {
      handleError(error, 'ProfileContext.createProfile');
    }
  };

  /**
   * Supprime un profil
   * @param profileId - ID du profil à supprimer
   * @returns true si la suppression a réussi, false sinon
   */
  const deleteProfile = async (profileId: string): Promise<boolean> => {
    try {
      // Journaliser l'opération
      logDelete('assignees', profileId, currentProfile?.id, 'Suppression d\'un profil');

      // Supprimer le profil de la base de données
      const { error } = await supabase
        .from('assignees')
        .delete()
        .eq('id', profileId);

      if (error) {
        throw createAppError(
          `Erreur lors de la suppression du profil: ${error.message}`,
          ErrorCode.DB_QUERY_ERROR
        );
      }

      setProfiles(prev => prev.filter(p => p.id !== profileId));

      // Si le profil supprimé était le profil courant, réinitialiser
      if (currentProfile?.id === profileId) {
        setCurrentProfile(null);
        localStorage.removeItem('currentProfileId');
      }

      toast.success("Profil supprimé avec succès");
      return true;
    } catch (error) {
      handleError(error, 'ProfileContext.deleteProfile');
      return false;
    }
  };

  /**
   * Sélectionne un profil
   * @param profile - Profil à sélectionner
   */
  const selectProfile = (profile: Profile) => {
    setCurrentProfile(profile);
    localStorage.setItem('currentProfileId', profile.id);
    toast.success(`Profil de ${profile.name} sélectionné`);
  };

  return (
    <ProfileContext.Provider
      value={{
        profiles,
        currentProfile,
        loading,
        createProfile,
        deleteProfile,
        selectProfile
      }}
    >
      {children}
    </ProfileContext.Provider>
  );
};

/**
 * Hook pour utiliser le contexte des profils
 * @returns Contexte des profils
 * @throws {Error} Si utilisé en dehors d'un ProfileProvider
 */
export const useProfileContext = () => {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfileContext must be used within a ProfileProvider');
  }
  return context;
};
