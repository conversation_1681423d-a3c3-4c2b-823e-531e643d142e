/**
 * Service optimisé pour les mises à jour temps réel
 * Remplace les rechargements complets par des mises à jour granulaires
 */

import { supabase } from "@/integrations/supabase/client";
import { Client, Step, Assignee } from "@/types";
import { safeDecryptText, getEncryptionKey } from "@/utils/encryption";

/**
 * Types pour les mises à jour granulaires
 */
export interface RealtimeUpdate {
  table: string;
  action: 'INSERT' | 'UPDATE' | 'DELETE';
  record: any;
  old_record?: any;
}

/**
 * Callbacks pour les mises à jour
 */
export interface RealtimeCallbacks {
  onClientUpdate: (clients: Client[]) => void;
  onError: (error: Error) => void;
}

/**
 * Service optimisé pour les mises à jour temps réel
 */
export class RealtimeOptimizedService {
  private clients: Client[] = [];
  private callbacks: RealtimeCallbacks | null = null;
  private subscription: any = null;

  /**
   * Initialise le service avec les données actuelles
   */
  initialize(clients: Client[], callbacks: RealtimeCallbacks): void {
    this.clients = [...clients];
    this.callbacks = callbacks;
    this.setupRealtimeSubscription();
  }

  /**
   * Met à jour les données locales
   */
  updateLocalData(clients: Client[]): void {
    this.clients = [...clients];
  }

  /**
   * Configure les souscriptions temps réel optimisées
   */
  private setupRealtimeSubscription(): void {
    try {
      // Créer un canal unique pour toutes les tables
      this.subscription = supabase
        .channel('optimized-client-updates')
        .on('postgres_changes', 
          { event: '*', schema: 'public', table: 'clients' },
          (payload) => this.handleClientChange(payload)
        )
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'steps' },
          (payload) => this.handleStepChange(payload)
        )
        .on('postgres_changes',
          { event: '*', schema: 'public', table: 'step_assignments' },
          (payload) => this.handleAssignmentChange(payload)
        )
        .subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            console.log('✅ Souscriptions temps réel optimisées actives');
          } else if (status === 'CHANNEL_ERROR') {
            console.error('❌ Erreur de souscription temps réel');
            this.callbacks?.onError(new Error('Erreur de connexion temps réel'));
          }
        });
    } catch (error) {
      console.error('Erreur lors de la configuration des souscriptions:', error);
      this.callbacks?.onError(error as Error);
    }
  }

  /**
   * Gère les changements de clients de manière granulaire
   */
  private async handleClientChange(payload: any): Promise<void> {
    try {
      const { eventType, new: newRecord, old: oldRecord } = payload;
      
      switch (eventType) {
        case 'INSERT':
          await this.handleClientInsert(newRecord);
          break;
        case 'UPDATE':
          await this.handleClientUpdate(newRecord, oldRecord);
          break;
        case 'DELETE':
          this.handleClientDelete(oldRecord);
          break;
      }
    } catch (error) {
      console.error('Erreur lors du traitement du changement client:', error);
      this.callbacks?.onError(error as Error);
    }
  }

  /**
   * Gère l'insertion d'un nouveau client
   */
  private async handleClientInsert(record: any): Promise<void> {
    try {
      const encryptionKey = getEncryptionKey();
      const decryptedName = await safeDecryptText(record.name, encryptionKey);
      
      const newClient: Client = {
        id: record.id,
        name: decryptedName,
        dueDate: record.due_date,
        completed: record.completed ?? false,
        steps: [],
        created_at: record.created_at
      };

      // Charger les étapes pour ce client
      const { data: stepsData } = await supabase
        .from('steps')
        .select('*')
        .eq('client_id', record.id);

      if (stepsData) {
        newClient.steps = await this.mapStepsData(stepsData, encryptionKey);
      }

      this.clients.unshift(newClient); // Ajouter au début
      this.notifyUpdate();
    } catch (error) {
      console.error('Erreur lors de l\'insertion du client:', error);
      throw error;
    }
  }

  /**
   * Gère la mise à jour d'un client
   */
  private async handleClientUpdate(newRecord: any, oldRecord: any): Promise<void> {
    try {
      const clientIndex = this.clients.findIndex(c => c.id === newRecord.id);
      if (clientIndex === -1) {
        console.warn(`Client ${newRecord.id} non trouvé pour mise à jour`);
        return;
      }

      const encryptionKey = getEncryptionKey();
      const decryptedName = await safeDecryptText(newRecord.name, encryptionKey);

      // Mettre à jour seulement les champs modifiés
      this.clients[clientIndex] = {
        ...this.clients[clientIndex],
        name: decryptedName,
        dueDate: newRecord.due_date,
        completed: newRecord.completed ?? false
      };

      this.notifyUpdate();
    } catch (error) {
      console.error('Erreur lors de la mise à jour du client:', error);
      throw error;
    }
  }

  /**
   * Gère la suppression d'un client
   */
  private handleClientDelete(record: any): void {
    const clientIndex = this.clients.findIndex(c => c.id === record.id);
    if (clientIndex !== -1) {
      this.clients.splice(clientIndex, 1);
      this.notifyUpdate();
    }
  }

  /**
   * Gère les changements d'étapes
   */
  private async handleStepChange(payload: any): Promise<void> {
    try {
      const { eventType, new: newRecord, old: oldRecord } = payload;
      
      switch (eventType) {
        case 'INSERT':
          await this.handleStepInsert(newRecord);
          break;
        case 'UPDATE':
          await this.handleStepUpdate(newRecord);
          break;
        case 'DELETE':
          this.handleStepDelete(oldRecord);
          break;
      }
    } catch (error) {
      console.error('Erreur lors du traitement du changement d\'étape:', error);
      this.callbacks?.onError(error as Error);
    }
  }

  /**
   * Gère l'insertion d'une nouvelle étape
   */
  private async handleStepInsert(record: any): Promise<void> {
    const client = this.clients.find(c => c.id === record.client_id);
    if (!client) return;

    const encryptionKey = getEncryptionKey();
    const decryptedName = await safeDecryptText(record.name, encryptionKey);
    const decryptedComment = await safeDecryptText(record.comment || '', encryptionKey);

    const newStep: Step = {
      id: record.id,
      name: decryptedName,
      status: record.status,
      receivedDate: record.received_date,
      comment: decryptedComment,
      assignees: []
    };

    client.steps.push(newStep);
    this.notifyUpdate();
  }

  /**
   * Gère la mise à jour d'une étape
   */
  private async handleStepUpdate(record: any): Promise<void> {
    const client = this.clients.find(c => c.id === record.client_id);
    if (!client) return;

    const stepIndex = client.steps.findIndex(s => s.id === record.id);
    if (stepIndex === -1) return;

    const encryptionKey = getEncryptionKey();
    const decryptedName = await safeDecryptText(record.name, encryptionKey);
    const decryptedComment = await safeDecryptText(record.comment || '', encryptionKey);

    client.steps[stepIndex] = {
      ...client.steps[stepIndex],
      name: decryptedName,
      status: record.status,
      receivedDate: record.received_date,
      comment: decryptedComment
    };

    this.notifyUpdate();
  }

  /**
   * Gère la suppression d'une étape
   */
  private handleStepDelete(record: any): void {
    const client = this.clients.find(c => c.id === record.client_id);
    if (!client) return;

    const stepIndex = client.steps.findIndex(s => s.id === record.id);
    if (stepIndex !== -1) {
      client.steps.splice(stepIndex, 1);
      this.notifyUpdate();
    }
  }

  /**
   * Gère les changements d'assignations
   */
  private async handleAssignmentChange(payload: any): Promise<void> {
    // Pour les assignations, on recharge les assignees de l'étape concernée
    // C'est plus simple et évite la complexité de la gestion granulaire
    const { eventType, new: newRecord, old: oldRecord } = payload;
    const stepId = newRecord?.step_id || oldRecord?.step_id;
    
    if (stepId) {
      await this.reloadStepAssignees(stepId);
    }
  }

  /**
   * Recharge les assignees d'une étape spécifique
   */
  private async reloadStepAssignees(stepId: string): Promise<void> {
    try {
      // Trouver l'étape
      let targetStep: Step | null = null;
      for (const client of this.clients) {
        const step = client.steps.find(s => s.id === stepId);
        if (step) {
          targetStep = step;
          break;
        }
      }

      if (!targetStep) return;

      // Recharger les assignees
      const { data: assignmentsData } = await supabase
        .from('step_assignments')
        .select(`
          assignee_id,
          assignees!inner(id, name, created_at)
        `)
        .eq('step_id', stepId);

      if (assignmentsData) {
        const encryptionKey = getEncryptionKey();
        targetStep.assignees = await Promise.all(
          assignmentsData.map(async (assignment: any) => ({
            id: assignment.assignees.id,
            name: await safeDecryptText(assignment.assignees.name, encryptionKey),
            createdAt: assignment.assignees.created_at
          }))
        );

        this.notifyUpdate();
      }
    } catch (error) {
      console.error('Erreur lors du rechargement des assignees:', error);
    }
  }

  /**
   * Mappe les données d'étapes
   */
  private async mapStepsData(stepsData: any[], encryptionKey: string): Promise<Step[]> {
    return Promise.all(
      stepsData.map(async (step) => ({
        id: step.id,
        name: await safeDecryptText(step.name, encryptionKey),
        status: step.status,
        receivedDate: step.received_date,
        comment: await safeDecryptText(step.comment || '', encryptionKey),
        assignees: [] // Chargées séparément si nécessaire
      }))
    );
  }

  /**
   * Notifie les callbacks des mises à jour
   */
  private notifyUpdate(): void {
    if (this.callbacks) {
      this.callbacks.onClientUpdate([...this.clients]);
    }
  }

  /**
   * Nettoie les souscriptions
   */
  cleanup(): void {
    if (this.subscription) {
      supabase.removeChannel(this.subscription);
      this.subscription = null;
    }
    this.callbacks = null;
  }

  /**
   * Obtient les statistiques du service
   */
  getStats() {
    return {
      clientCount: this.clients.length,
      isSubscribed: !!this.subscription,
      hasCallbacks: !!this.callbacks
    };
  }
}
