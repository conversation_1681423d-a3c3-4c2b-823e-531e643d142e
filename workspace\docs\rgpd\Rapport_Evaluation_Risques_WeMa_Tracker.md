# Rapport d'évaluation des risques - WeMa Tracker
**Version 1.0 - Mai 2025**

## 1. Introduction

### 1.1 Contexte
Ce rapport présente l'évaluation préliminaire des risques pour les droits et libertés des personnes concernées dans le cadre de l'application WeMa Tracker, conformément aux exigences du Règlement Général sur la Protection des Données (RGPD).

### 1.2 Méthodologie
Cette évaluation s'inspire des principes de la méthodologie EBIOS Risk Manager, développée par l'ANSSI et la CNIL, adaptée à la taille et aux besoins spécifiques de notre organisation.

### 1.3 Périmètre
Cette évaluation couvre les principaux traitements de données personnelles effectués par l'application WeMa Tracker, incluant la collecte, le stockage et l'utilisation des données des clients et des utilisateurs internes.

## 2. Cartographie des traitements

### 2.1 Données traitées
- Données d'identification des utilisateurs (nom, prénom, adresse email professionnelle)
- Données de connexion (identifiants, journaux d'accès)
- Données relatives aux clients (nom, coordonnées, historique des dossiers)
- Données de suivi des tâches et commentaires associés
- Notifications et messages entre utilisateurs

### 2.2 Flux de données
- Collecte des données utilisateurs lors de la création des profils
- Stockage des données dans la base de données Supabase
- Échanges de données entre l'application cliente et le serveur
- Partage de données entre utilisateurs autorisés de l'application

### 2.3 Destinataires
- Utilisateurs internes de l'application (collaborateurs autorisés)
- Administrateurs système pour la maintenance
- Aucun transfert à des tiers externes n'est prévu

## 3. Analyse des risques

### 3.1 Identification des menaces

| ID | Menace | Description |
|----|--------|-------------|
| M1 | Accès non autorisé | Accès illégitime aux données par une personne non habilitée |
| M2 | Perte de données | Destruction ou indisponibilité des données client |
| M3 | Divulgation accidentelle | Exposition non intentionnelle de données à des personnes non autorisées |
| M4 | Modification non autorisée | Altération des données sans autorisation |
| M5 | Usurpation d'identité | Utilisation frauduleuse des identifiants d'un utilisateur |

### 3.2 Évaluation des vulnérabilités

| ID | Vulnérabilité | Description | Niveau |
|----|---------------|-------------|--------|
| V1 | Authentification insuffisante | Mécanismes de contrôle d'accès potentiellement contournables | Faible |
| V2 | Chiffrement incomplet | Certaines données pourraient ne pas être chiffrées au repos | Moyen |
| V3 | Absence de journalisation | Difficulté à tracer certaines actions utilisateurs | Faible |
| V4 | Sauvegarde incomplète | Processus de sauvegarde non exhaustif | Faible |
| V5 | Formation insuffisante | Utilisateurs potentiellement mal formés aux bonnes pratiques | Moyen |

### 3.3 Évaluation des impacts

| Impact | Description | Gravité |
|--------|-------------|---------|
| I1 | Atteinte à la confidentialité des données clients | Élevée |
| I2 | Perte d'intégrité des dossiers clients | Moyenne |
| I3 | Indisponibilité temporaire du service | Faible |
| I4 | Atteinte à la réputation | Moyenne |
| I5 | Sanctions administratives | Moyenne |

### 3.4 Matrice des risques

| ID | Scénario de risque | Probabilité | Impact | Niveau de risque |
|----|-------------------|-------------|--------|-----------------|
| R1 | Accès non autorisé aux données clients suite à une compromission d'identifiants | Faible | Élevé | Moyen |
| R2 | Perte de données suite à un incident technique | Faible | Moyen | Faible |
| R3 | Divulgation accidentelle de données par un utilisateur | Moyen | Moyen | Moyen |
| R4 | Modification non autorisée des dossiers clients | Faible | Moyen | Faible |
| R5 | Usurpation d'identité d'un utilisateur | Faible | Élevé | Moyen |

## 4. Mesures de sécurité envisagées

### 4.1 Mesures techniques prévues

| Mesure | Description | Risques adressés | Statut |
|--------|-------------|-----------------|--------|
| MT1 | Authentification avec politique de mots de passe | R1, R5 | En place (basique) |
| MT2 | Chiffrement des données sensibles | R1, R3 | Partiellement en place |
| MT3 | Journalisation des actions principales | R1, R4, R5 | À améliorer |
| MT4 | Sauvegardes régulières | R2 | En place |
| MT5 | Contrôles d'accès simples | R1, R3, R4 | En place |
| MT6 | Mise à jour des composants logiciels | R1, R5 | À planifier |
| MT7 | Séparation des environnements | R1, R4 | À mettre en place |

### 4.2 Mesures organisationnelles prévues

| Mesure | Description | Risques adressés | Statut |
|--------|-------------|-----------------|--------|
| MO1 | Sensibilisation des utilisateurs | R1, R3, R5 | À formaliser |
| MO2 | Procédure de gestion des incidents | R1, R2, R3, R4, R5 | À développer |
| MO3 | Politique de mots de passe | R1, R5 | À formaliser |
| MO4 | Revue des droits d'accès | R1, R4, R5 | À mettre en place |
| MO5 | Procédure de départ des collaborateurs | R1, R5 | À développer |
| MO6 | Documentation sur la protection des données | R3 | À créer |

## 5. Évaluation des risques

Évaluation actuelle des risques, avant mise en œuvre complète des mesures de sécurité prévues.

| ID | Scénario de risque | Niveau actuel | Niveau cible après mesures |
|----|-------------------|---------------|---------------------------|
| R1 | Accès non autorisé aux données clients | Moyen | Faible |
| R2 | Perte de données | Moyen | Très faible |
| R3 | Divulgation accidentelle de données | Moyen | Faible |
| R4 | Modification non autorisée des dossiers | Moyen | Très faible |
| R5 | Usurpation d'identité | Moyen | Faible |

## 6. Plan d'action proposé

### 6.1 Actions prioritaires (0-3 mois)
- Formaliser une politique de mots de passe et renforcer l'authentification
- Mettre en place une sensibilisation basique à la sécurité des données pour les utilisateurs
- Compléter le chiffrement des données sensibles dans l'application

### 6.2 Actions à moyen terme (3-6 mois)
- Développer une procédure simple de gestion des incidents
- Améliorer la journalisation des actions utilisateurs
- Mettre en place une procédure de revue des droits d'accès

### 6.3 Actions à plus long terme (6-12 mois)
- Évaluer la faisabilité d'une authentification renforcée
- Formaliser les procédures de sauvegarde et de restauration
- Réaliser une nouvelle évaluation des risques

## 7. Conclusion

Cette évaluation préliminaire des risques a permis d'identifier les principales vulnérabilités liées au traitement des données personnelles dans l'application WeMa Tracker. Les mesures de sécurité proposées, une fois mises en œuvre, devraient permettre de réduire ces risques à un niveau acceptable.

Ce document constitue une première étape dans notre démarche de mise en conformité avec le RGPD. Il sera complété et affiné au fur et à mesure de l'avancement du plan d'action.


