/**
 * Utilitaire de pseudonymisation des données
 * Conforme aux recommandations de la CNIL pour la protection des données
 */

/**
 * Pseudonymise une chaîne de caractères en conservant sa structure
 * @param text - Texte à pseudonymiser
 * @param preserveLength - Conserver la longueur originale (par défaut: true)
 * @returns Texte pseudonymisé
 */
export function pseudonymizeText(text: string, preserveLength = true): string {
  if (!text) return text;
  
  // Pour les textes courts (noms, prénoms), remplacer par des initiales suivies d'astérisques
  if (text.length < 20) {
    const words = text.split(' ');
    return words.map(word => {
      if (word.length === 0) return '';
      if (preserveLength) {
        return word[0] + '*'.repeat(word.length - 1);
      }
      return word[0] + '***';
    }).join(' ');
  }
  
  // Pour les textes plus longs (commentaires, messages), conserver la structure
  // mais remplacer les mots par des placeholders
  const words = text.split(' ');
  return words.map(word => {
    // Conserver la ponctuation et les caractères spéciaux
    const punctuation = word.match(/[^\w]$/)?.[0] || '';
    const cleanWord = punctuation ? word.slice(0, -1) : word;
    
    // Conserver certains mots courants
    const commonWords = ['le', 'la', 'les', 'un', 'une', 'des', 'et', 'ou', 'à', 'de', 'du', 'au', 'aux'];
    if (commonWords.includes(cleanWord.toLowerCase())) {
      return word;
    }
    
    // Pseudonymiser le mot
    if (preserveLength) {
      return 'x'.repeat(cleanWord.length) + punctuation;
    }
    return 'xxxx' + punctuation;
  }).join(' ');
}

/**
 * Pseudonymise un objet contenant des données personnelles
 * @param data - Objet à pseudonymiser
 * @param sensitiveFields - Liste des champs sensibles à pseudonymiser
 * @returns Objet pseudonymisé
 */
export function pseudonymizeObject<T extends Record<string, any>>(
  data: T, 
  sensitiveFields: string[]
): T {
  if (!data) return data;
  
  const result = { ...data };
  
  for (const field of sensitiveFields) {
    if (field in result && typeof result[field] === 'string') {
      result[field] = pseudonymizeText(result[field]);
    }
  }
  
  return result;
}

/**
 * Pseudonymise un tableau d'objets contenant des données personnelles
 * @param dataArray - Tableau d'objets à pseudonymiser
 * @param sensitiveFields - Liste des champs sensibles à pseudonymiser
 * @returns Tableau d'objets pseudonymisés
 */
export function pseudonymizeArray<T extends Record<string, any>>(
  dataArray: T[], 
  sensitiveFields: string[]
): T[] {
  if (!dataArray || !Array.isArray(dataArray)) return dataArray;
  
  return dataArray.map(item => pseudonymizeObject(item, sensitiveFields));
}

/**
 * Liste des champs sensibles par défaut dans l'application
 */
export const DEFAULT_SENSITIVE_FIELDS = [
  'name',           // Noms des clients et utilisateurs
  'comment',        // Commentaires sur les étapes
  'message'         // Messages dans les pings et notifications
];

/**
 * Pseudonymise les données pour l'export ou les statistiques
 * @param data - Données à pseudonymiser
 * @returns Données pseudonymisées
 */
export function pseudonymizeForExport<T>(data: T): T {
  if (!data) return data;
  
  if (Array.isArray(data)) {
    return pseudonymizeArray(data, DEFAULT_SENSITIVE_FIELDS) as unknown as T;
  }
  
  if (typeof data === 'object' && data !== null) {
    return pseudonymizeObject(data as Record<string, any>, DEFAULT_SENSITIVE_FIELDS) as unknown as T;
  }
  
  return data;
}
