import { supabase } from "@/integrations/supabase/client";

// Clé pour stocker la date de la dernière requête dans le localStorage
const LAST_PING_KEY = 'wema_tracker_last_ping';

// Intervalle en millisecondes (3 jours)
const PING_INTERVAL = 3 * 24 * 60 * 60 * 1000;

/**
 * Vérifie si une requête keep-alive doit être envoyée
 * @returns {boolean} True si une requête doit être envoyée
 */
export const shouldSendKeepAlive = (): boolean => {
  try {
    const lastPingStr = localStorage.getItem(LAST_PING_KEY);

    if (!lastPingStr) {
      // Aucune requête n'a encore été envoyée
      return true;
    }

    const lastPing = new Date(lastPingStr).getTime();
    const now = new Date().getTime();

    // Vérifier si l'intervalle s'est écoulé
    return now - lastPing >= PING_INTERVAL;
  } catch (error) {
    console.error('Erreur lors de la vérification de la date de la dernière requête:', error);
    return true; // En cas d'erreur, envoyer une requête par précaution
  }
};

/**
 * Envoie une requête keep-alive à Supabase
 * @returns {Promise<boolean>} True si la requête a réussi
 */
export const sendKeepAlive = async (): Promise<boolean> => {
  try {
    console.log('Envoi d\'une requête keep-alive à Supabase...');

    // Effectuer une requête simple qui ne modifie pas les données
    const { error } = await supabase
      .from('assignees')
      .select('*', { count: 'exact', head: true });

    if (error) {
      throw error;
    }

    // Mettre à jour la date de la dernière requête
    localStorage.setItem(LAST_PING_KEY, new Date().toISOString());

    console.log('Requête keep-alive envoyée avec succès à', new Date().toISOString());
    console.log('Prochaine requête dans 3 jours');

    return true;
  } catch (error) {
    console.error('Erreur lors de l\'envoi de la requête keep-alive:', error);
    return false;
  }
};

/**
 * Configure le système de keep-alive
 */
export const setupKeepAlive = (): void => {
  // Vérifier immédiatement si une requête doit être envoyée
  if (shouldSendKeepAlive()) {
    sendKeepAlive().catch(error => {
      console.error('Erreur lors de l\'envoi de la requête keep-alive initiale:', error);
    });
  }

  // Vérifier périodiquement si une requête doit être envoyée (toutes les 12 heures)
  // Nous vérifions plus souvent que l'intervalle réel pour s'assurer de ne pas manquer la fenêtre
  const checkInterval = 12 * 60 * 60 * 1000; // 12 heures

  setInterval(() => {
    if (shouldSendKeepAlive()) {
      sendKeepAlive().catch(error => {
        console.error('Erreur lors de l\'envoi de la requête keep-alive périodique:', error);
      });
    }
  }, checkInterval);
};
