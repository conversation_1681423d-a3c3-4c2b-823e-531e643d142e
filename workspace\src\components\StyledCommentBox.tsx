
import * as React from "react";
import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { AutoResizeTextarea } from "@/components/ui/auto-resize-textarea";
import { useClientContext } from '@/contexts/ClientContext';
import { Mention, MentionsInput, SuggestionDataItem } from 'react-mentions';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { sendDesktopNotification } from "@/utils/notifications";

interface StyledCommentBoxProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  mentionable?: boolean;
  onMention?: (userId: string) => void;
  stepId?: string;
}

const StyledCommentBox = React.forwardRef<HTMLTextAreaElement, StyledCommentBoxProps>(
  ({ className, mentionable = true, onMention, stepId, ...props }, ref) => {
    const { getAssignees, currentProfile, createNotification } = useClientContext();
    const [assignees, setAssignees] = useState<SuggestionDataItem[]>([]);

    useEffect(() => {
      const fetchAssignees = async () => {
        const fetchedAssignees = await getAssignees();
        setAssignees(
          fetchedAssignees.map(assignee => ({
            id: assignee.id,
            display: assignee.name
          }))
        );
      };

      if (mentionable) {
        fetchAssignees();
      }
    }, [getAssignees, mentionable]);

    const handleAddMention = (id: string, display: string) => {
      if (onMention) {
        onMention(id);
      }

      // Create notification for mentioned user
      if (currentProfile && stepId) {
        const message = `${currentProfile.name} vous a mentionné(e) dans un commentaire`;
        createNotification(id, stepId, message);

        // Also send desktop notification
        sendDesktopNotification(
          "Nouvelle mention",
          `${currentProfile.name} vous a mentionné(e) dans un commentaire`,
          "/lovable-uploads/wema-logo.png"
        );
      }
    };

    if (!mentionable) {
      return (
        <AutoResizeTextarea
          ref={ref}
          className={cn(
            "w-full min-h-[32px] max-h-[120px] bg-white/50 backdrop-blur-sm rounded-md border-0",
            "shadow-sm ring-1 ring-gray-100 focus:ring-2 focus:ring-blue-500/40",
            "placeholder:text-gray-400 text-gray-600 text-sm leading-relaxed",
            "transition-all duration-200 ease-in-out py-1.5 px-3",
            "resize-none scrollbar-none focus:shadow-md hover:ring-gray-200",
            className
          )}
          {...props}
        />
      );
    }

    // Custom styling for the mentionable input
    const mentionInputStyle = {
      control: {
        minHeight: '32px',
        maxHeight: '120px',
        backgroundColor: 'rgba(255,255,255,0.5)',
        borderRadius: '0.375rem',
        border: '0',
        boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        width: '100%',
        padding: '0.375rem 0.75rem',
        fontSize: '0.875rem',
        lineHeight: '1.5',
        resize: 'none',
        transition: 'all 0.2s ease-in-out'
      },
      highlighter: {
        padding: '0.375rem 0.75rem',
        boxSizing: 'border-box' as const
      },
      input: {
        padding: '0.375rem 0.75rem',
        outline: 'none',
        border: '0',
        boxSizing: 'border-box' as const
      },
      suggestions: {
        list: {
          backgroundColor: 'white',
          border: '1px solid rgba(0, 0, 0, 0.1)',
          borderRadius: '0.375rem',
          boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
          marginTop: '0.25rem'
        },
        item: {
          padding: '0.5rem',
          borderBottom: '1px solid rgba(0, 0, 0, 0.05)',
          '&focused': {
            backgroundColor: 'rgba(59, 130, 246, 0.1)'
          }
        }
      }
    };

    const renderSuggestion = (
      suggestion: SuggestionDataItem,
      search: string,
      highlightedDisplay: React.ReactNode,
    ) => (
      <div className="flex items-center space-x-2 p-1">
        <Avatar className="h-6 w-6">
          <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
            {suggestion.display.charAt(0)}
          </AvatarFallback>
        </Avatar>
        <span>{highlightedDisplay}</span>
      </div>
    );

    return (
      <div className={className}>
        <MentionsInput
          value={props.value as string}
          onChange={props.onChange}
          style={mentionInputStyle}
          placeholder={props.placeholder}
          className={cn("mentions-input")}
        >
          <Mention
            trigger="@"
            data={assignees}
            renderSuggestion={renderSuggestion}
            className="bg-blue-50 text-blue-700 rounded px-0.5"
            appendSpaceOnAdd
            onAdd={handleAddMention}
          />
        </MentionsInput>
      </div>
    );
  }
);

StyledCommentBox.displayName = "StyledCommentBox";

export { StyledCommentBox };
