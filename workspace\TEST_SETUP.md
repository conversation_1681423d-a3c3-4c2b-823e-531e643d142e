# Guide de Configuration des Tests - WeMa Tracker

## 🚀 Configuration Rapide

Pour configurer automatiquement tous les tests dans ce projet :

```bash
node scripts/setup-tests.js
```

## 📋 Commandes de Test Disponibles

### Tests Unitaires (Vitest)
```bash
npm test                    # Lance tous les tests unitaires
npm run test:watch          # Mode surveillance (développement)
npm run test:ui             # Interface graphique Vitest
npm run test:coverage       # Tests avec couverture de code
npm run test:unit           # Tests unitaires seulement
```

### Tests d'Intégration
```bash
npm run test:integration    # Tests d'intégration avec MSW
```

### Tests End-to-End (Playwright)
```bash
npm run test:e2e            # Tests E2E tous navigateurs
npm run test:e2e:ui         # Tests E2E avec interface graphique
```

### Tests Complets
```bash
npm run test:all            # Tous les types de tests
```

## 🛠️ Technologies de Test

### Tests Unitaires
- **Vitest** : Framework de test rapide compatible Vite
- **@testing-library/react** : Tests de composants React
- **@testing-library/jest-dom** : Matchers DOM personnalisés
- **jsdom** : Environnement DOM pour les tests

### Tests d'Intégration
- **MSW (Mock Service Worker)** : Mock des API REST
- **Vitest** : Exécution des tests d'intégration

### Tests E2E
- **Playwright** : Automation des navigateurs
- **Support multi-navigateurs** : Chrome, Firefox, Safari
- **Tests mobiles** : iOS Safari, Android Chrome

## 📁 Structure des Tests

```
src/
├── test/
│   ├── setup.ts                 # Configuration globale Vitest
│   ├── integration-setup.ts     # Configuration tests d'intégration
│   └── mocks/
│       └── server.ts            # Serveur MSW
├── components/
│   └── __tests__/
│       └── *.test.tsx           # Tests unitaires composants
├── services/
│   └── __tests__/
│       ├── *.test.ts            # Tests unitaires services
│       └── *.integration.test.ts # Tests d'intégration
└── utils/
    └── __tests__/
        └── *.test.ts            # Tests utilitaires

e2e/
└── *.spec.ts                   # Tests Playwright

coverage/                       # Rapports de couverture
test-results/                   # Résultats Playwright
playwright-report/              # Rapports Playwright
```

## 🎯 Types de Tests

### 1. Tests Unitaires
- Tests de composants React isolés
- Tests de fonctions utilitaires
- Tests de services (logique métier)
- Mocks des dépendances externes

**Exemple :**
```typescript
// src/components/__tests__/Logo.test.tsx
import { describe, it, expect } from 'vitest'
import { render, screen } from '@testing-library/react'
import { Logo } from '../Logo'

describe('Logo', () => {
  it('should render correctly', () => {
    render(<Logo />)
    expect(screen.getByRole('img')).toBeInTheDocument()
  })
})
```

### 2. Tests d'Intégration
- Tests d'interaction entre composants
- Tests d'API avec MSW
- Tests de flux de données complets

**Exemple :**
```typescript
// src/services/__tests__/clientService.integration.test.ts
import { describe, it, expect } from 'vitest'
import { clientService } from '../clientService'

describe('ClientService Integration', () => {
  it('should fetch clients', async () => {
    const clients = await clientService.getClients()
    expect(clients).toHaveLength(1)
  })
})
```

### 3. Tests E2E
- Tests utilisateur complets
- Tests multi-navigateurs
- Tests de responsive design

**Exemple :**
```typescript
// e2e/example.spec.ts
import { test, expect } from '@playwright/test'

test('should load homepage', async ({ page }) => {
  await page.goto('/')
  await expect(page).toHaveTitle(/WeMa Tracker/)
})
```

## ⚙️ Configuration

### Vitest (vitest.config.ts)
- Environnement jsdom
- Coverage V8
- Mocks automatiques Tauri/Supabase
- Seuils de couverture : 60%

### Playwright (playwright.config.ts)
- Tests multi-navigateurs
- Screenshots sur échec
- Traces sur retry
- Serveur de développement automatique

## 🔧 Développement

### Ajout de nouveaux tests
1. Créer le fichier test à côté du code source
2. Utiliser la convention `*.test.tsx` ou `*.spec.ts`
3. Importer les utilities de test appropriées

### Mocks personnalisés
- API externes : Utiliser MSW dans `src/test/mocks/`
- Tauri APIs : Pré-configurés dans `setup.ts`
- Supabase : Pré-configuré dans `setup.ts`

### Debugging
```bash
npm run test:ui        # Interface graphique Vitest
npm run test:e2e:ui    # Interface graphique Playwright
```

## 📊 Rapports

### Couverture de Code
```bash
npm run test:coverage
# Ouvre automatiquement coverage/index.html
```

### Rapports Playwright
```bash
npm run test:e2e
npx playwright show-report
```

## 🚨 CI/CD

Les tests sont prêts pour l'intégration continue :

```yaml
# .github/workflows/tests.yml
- name: Run tests
  run: |
    npm run test:unit
    npm run test:integration
    npm run test:e2e
```

## 📝 Bonnes Pratiques

1. **Tests unitaires** : Isolés, rapides, nombreux
2. **Tests d'intégration** : Interactions importantes
3. **Tests E2E** : Parcours utilisateur critiques
4. **Nommage** : Descriptif et en français
5. **Arrange-Act-Assert** : Structure claire des tests

## 🐛 Résolution de Problèmes

### Erreurs communes
- **Timeout** : Augmenter `testTimeout` dans la config
- **Mocks** : Vérifier les imports dans `setup.ts`
- **Playwright** : Réinstaller navigateurs `npx playwright install`

### Debug
```bash
# Tests unitaires
npm run test:watch -- --reporter=verbose

# Tests E2E
npm run test:e2e -- --debug
```

---

**✅ Ce projet est maintenant entièrement configuré pour les tests !**

Commandes principales :
- `npm test` - Tests unitaires
- `npm run test:e2e` - Tests E2E  
- `npm run test:all` - Tous les tests 