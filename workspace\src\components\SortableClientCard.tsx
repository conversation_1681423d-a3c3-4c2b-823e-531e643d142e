import React from 'react';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Card, CardContent } from '@/components/ui/card';
import { ProgressBar } from '@/components/ProgressBar';
import { AlertCircle, TrendingUp } from 'lucide-react';
import { Link } from 'react-router-dom';
import { Client } from '@/types';
import { DueDateDisplay } from '@/components/DueDateDisplay';
import { formatPatrimoineCompact } from '@/utils/patrimoine';

interface SortableClientCardProps {
  client: Client;
  progress: { completed: number; total: number };
  complianceProgress: { completed: number; total: number; percentage: number };
  hasUrgent: boolean;
}

const SortableClientCard: React.FC<SortableClientCardProps> = ({
  client,
  progress,
  complianceProgress,
  hasUrgent,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: client.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 'auto',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className="mb-6 touch-manipulation"
      {...attributes}
    >
      <Link
        to={`/client/${client.id}`}
        className={isDragging ? 'pointer-events-none' : ''}
        onClick={(e) => isDragging && e.preventDefault()}
      >
        <Card
          className={`h-full transition-all duration-300 responsive-card cursor-grab active:cursor-grabbing ${
            hasUrgent ? 'border-pink-300 shadow-md' : 'hover:border-blue-300'
          } ${isDragging ? 'shadow-xl ring-2 ring-blue-400' : ''}`}
          {...listeners}
        >
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium truncate flex-1">{client.name}</h3>
              <div className="flex items-center gap-2 ml-3">
                {client.totalPatrimoine && client.totalPatrimoine > 0 && (
                  <div className="flex items-center gap-1 bg-green-50 px-2 py-1 rounded-md">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-xs font-medium text-green-700">
                      {formatPatrimoineCompact(client.totalPatrimoine)}
                    </span>
                  </div>
                )}
                {hasUrgent && (
                  <div className="bg-pink-100 p-0.5 rounded-full">
                    <AlertCircle className="h-3 w-3 text-pink-600" />
                  </div>
                )}
              </div>
            </div>

            {/* Date de rendu du dossier */}
            {client.dueDate && (
              <div className="mb-4">
                <DueDateDisplay dueDate={client.dueDate} variant="card" />
              </div>
            )}

            {/* Barre de progression des étapes */}
            <div className="mb-3">
              <div className="text-xs text-gray-500 mb-1 font-medium">Étapes</div>
              <ProgressBar
                completed={progress.completed}
                total={progress.total}
                className="text-xs"
              />
            </div>

            {/* Barre de progression de conformité */}
            <div>
              <div className="text-xs text-gray-500 mb-1 font-medium">Conformité</div>
              <ProgressBar
                completed={complianceProgress.completed}
                total={complianceProgress.total}
                className="text-xs"
              />
            </div>
          </CardContent>
        </Card>
      </Link>
    </div>
  );
};

export default SortableClientCard;
