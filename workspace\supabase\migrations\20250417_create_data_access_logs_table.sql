-- Migration pour créer la table de journalisation des accès aux données personnelles
-- Conforme aux recommandations de la CNIL pour la protection des données

-- Création de la table de journalisation
CREATE TABLE IF NOT EXISTS public.data_access_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  operation TEXT NOT NULL, -- 'read', 'create', 'update', 'delete', 'export', 'anonymize'
  table_name TEXT NOT NULL,
  record_id UUID, -- ID de l'enregistrement concerné (peut être NULL pour les opérations sur plusieurs enregistrements)
  user_id UUID, -- ID de l'utilisateur qui a effectué l'opération (peut être NULL pour les opérations système)
  details TEXT, -- Détails supplémentaires sur l'opération (pseudonymisés)
  timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  ip_address TEXT -- Adresse IP de l'utilisateur (peut être NULL)
);

-- Commentaires sur la table et les colonnes
COMMENT ON TABLE public.data_access_logs IS 'Journal des accès aux données personnelles pour conformité RGPD';
COMMENT ON COLUMN public.data_access_logs.operation IS 'Type d''opération: read, create, update, delete, export, anonymize';
COMMENT ON COLUMN public.data_access_logs.table_name IS 'Nom de la table concernée par l''opération';
COMMENT ON COLUMN public.data_access_logs.record_id IS 'ID de l''enregistrement concerné (NULL pour les opérations sur plusieurs enregistrements)';
COMMENT ON COLUMN public.data_access_logs.user_id IS 'ID de l''utilisateur qui a effectué l''opération (NULL pour les opérations système)';
COMMENT ON COLUMN public.data_access_logs.details IS 'Détails supplémentaires sur l''opération (pseudonymisés)';
COMMENT ON COLUMN public.data_access_logs.timestamp IS 'Date et heure de l''opération';
COMMENT ON COLUMN public.data_access_logs.ip_address IS 'Adresse IP de l''utilisateur (peut être NULL)';

-- Index pour améliorer les performances des requêtes
CREATE INDEX IF NOT EXISTS data_access_logs_timestamp_idx ON public.data_access_logs (timestamp);
CREATE INDEX IF NOT EXISTS data_access_logs_user_id_idx ON public.data_access_logs (user_id);
CREATE INDEX IF NOT EXISTS data_access_logs_table_name_idx ON public.data_access_logs (table_name);
CREATE INDEX IF NOT EXISTS data_access_logs_operation_idx ON public.data_access_logs (operation);

-- Politique de sécurité Row Level Security (RLS)
ALTER TABLE public.data_access_logs ENABLE ROW LEVEL SECURITY;

-- Seuls les administrateurs peuvent accéder aux logs
CREATE POLICY "Administrators can access logs" ON public.data_access_logs
  FOR ALL USING (auth.uid() IN (
    SELECT id FROM public.assignees WHERE name IN ('Tilian', 'Cyril') -- Administrateurs par défaut
  ));

-- Fonction pour purger automatiquement les logs anciens (conservation de 6 mois)
CREATE OR REPLACE FUNCTION public.purge_old_data_access_logs()
RETURNS void AS $$
BEGIN
  DELETE FROM public.data_access_logs
  WHERE timestamp < NOW() - INTERVAL '6 months';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Commentaire sur la fonction
COMMENT ON FUNCTION public.purge_old_data_access_logs IS 'Purge les logs d''accès aux données de plus de 6 mois pour conformité RGPD';

-- Créer un déclencheur pour exécuter la purge automatiquement chaque jour à minuit
CREATE EXTENSION IF NOT EXISTS pg_cron;

SELECT cron.schedule(
  'purge-data-access-logs',
  '0 0 * * *', -- Tous les jours à minuit
  $$SELECT public.purge_old_data_access_logs()$$
);
