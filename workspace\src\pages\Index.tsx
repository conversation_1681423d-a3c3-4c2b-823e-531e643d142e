import { useState, useCallback, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useClientContext } from '@/contexts/ClientContext';
import { ClientContextMenu } from '@/components/ClientContextMenu';
import { UrgentTasksList } from '@/components/UrgentTasksList';
import { GlobalPatrimoineTable } from '@/components/GlobalPatrimoineTable';
import { Loader2, Plus, Search, ArrowUpDown, AlertCircle, TrendingUp } from 'lucide-react';
import {
  Dialog, DialogContent, DialogHeader,
  DialogTitle, DialogFooter, DialogTrigger
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ProfileSelect } from '@/components/ProfileSelect';
import { Logo } from '@/components/Logo';
import { NotificationDropdown } from '@/components/NotificationDropdown';
import { PingDropdown } from '@/components/PingDropdown';
import SortableClientCard from '@/components/SortableClientCard';
import { DueDateDisplay } from '@/components/DueDateDisplay';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  rectSortingStrategy
} from '@dnd-kit/sortable';
import { ProgressBar } from '@/components/ProgressBar';

const Index = () => {
  const navigate = useNavigate();
  const { clients, loading, addClient, searchClients, getClientProgress, getComplianceProgress, getUrgentSteps, updateClientCompleted } = useClientContext();
  const [searchQuery, setSearchQuery] = useState('');
  const [newClientName, setNewClientName] = useState('');
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isAdding, setIsAdding] = useState(false);
  const [orderedClients, setOrderedClients] = useState<string[]>([]);
  const [isDragging, setIsDragging] = useState(false);
  const [sortMode, setSortMode] = useState<'manual' | 'priority'>('manual');
  const [isGlobalPatrimoineOpen, setIsGlobalPatrimoineOpen] = useState(false);

  // Filtrer les clients en fonction de la recherche
  const filteredClients = searchQuery ? searchClients(searchQuery) : clients;

  // Séparer les clients actifs des clients terminés
  const activeClients = filteredClients.filter(client => !client.completed);
  const completedClients = filteredClients.filter(client => client.completed);

  // Fonction pour trier une liste de clients
  const sortClientsList = (clientsList: typeof filteredClients) => {
    return [...clientsList].sort((a, b) => {
      // Tri par priorité (date de rendu - plus urgent en premier)
      if (sortMode === 'priority') {
        // Gérer les clients sans date de rendu (les mettre à la fin)
        if (!a.dueDate && !b.dueDate) return 0;
        if (!a.dueDate) return 1;
        if (!b.dueDate) return -1;

        const dateA = new Date(a.dueDate).getTime();
        const dateB = new Date(b.dueDate).getTime();

        // Tri croissant (date la plus proche en premier = plus urgent)
        return dateA - dateB;
      }

      // Tri manuel (par défaut)
      const indexA = orderedClients.indexOf(a.id);
      const indexB = orderedClients.indexOf(b.id);

      // Si les deux clients ont un ordre personnalisé, les trier en fonction de cet ordre
      if (indexA !== -1 && indexB !== -1) {
        return indexA - indexB;
      }

      // Si seul le client A a un ordre personnalisé, le placer avant B
      if (indexA !== -1) {
        return -1;
      }

      // Si seul le client B a un ordre personnalisé, le placer avant A
      if (indexB !== -1) {
        return 1;
      }

      // Si aucun des deux n'a d'ordre personnalisé, conserver l'ordre d'origine
      return 0;
    });
  };

  // Trier les clients actifs et terminés
  const sortedActiveClients = sortClientsList(activeClients);
  const sortedCompletedClients = sortClientsList(completedClients);

  const urgentTasks = getUrgentSteps();

  // Charger l'ordre des clients et le mode de tri depuis le localStorage au chargement de la page
  useEffect(() => {
    try {
      // Charger l'ordre des clients
      const savedOrder = localStorage.getItem('clientOrder');
      if (savedOrder) {
        const parsedOrder = JSON.parse(savedOrder);
        if (Array.isArray(parsedOrder)) {
          setOrderedClients(parsedOrder);
        }
      }

      // Charger le mode de tri
      const savedSortMode = localStorage.getItem('clientSortMode');
      if (savedSortMode) {
        setSortMode(savedSortMode as 'manual' | 'priority');
      }
    } catch (error) {
      console.error('Erreur lors du chargement des préférences:', error);
    }
  }, []);

  // Sauvegarder le mode de tri lorsqu'il change
  useEffect(() => {
    localStorage.setItem('clientSortMode', sortMode);
  }, [sortMode]);

  // Configuration des capteurs pour dnd-kit
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // Activer le glisser-déposer après un déplacement de 8px
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // Fonction pour gérer le début du glisser-déposer
  const handleDragStart = useCallback(() => {
    setIsDragging(true);
  }, []);

  // Fonction pour gérer la fin du glisser-déposer avec dnd-kit
  const handleDragEnd = useCallback((event: DragEndEvent, clientsList: typeof sortedActiveClients) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // Obtenir l'ordre actuel des clients affichés
      const currentOrder = clientsList.map(client => client.id);

      // Trouver les index actuels
      const oldIndex = currentOrder.indexOf(active.id as string);
      const newIndex = currentOrder.indexOf(over.id as string);

      if (oldIndex !== -1 && newIndex !== -1) {
        // Utiliser arrayMove pour créer un nouvel ordre
        const newOrder = arrayMove(currentOrder, oldIndex, newIndex);

        // Mettre à jour l'état avec le nouvel ordre
        setOrderedClients(newOrder);

        // Sauvegarder l'ordre dans le localStorage pour le conserver entre les sessions
        localStorage.setItem('clientOrder', JSON.stringify(newOrder));
      }
    }

    setIsDragging(false);
  }, []);

  // Fonction pour ajouter un nouveau client
  const handleAddClient = async () => {
    if (newClientName.trim()) {
      setIsAdding(true);
      await addClient(newClientName.trim());
      setIsAdding(false);
      setNewClientName('');
      setIsDialogOpen(false);
    }
  };

  // Fonction pour rendre les cartes de clients
  const renderClientCards = (clientsList: typeof sortedActiveClients, enableDragDrop = false) => {
    if (clientsList.length === 0) {
      return (
        <div className="text-center py-8 text-gray-500">
          <p>Aucun client dans cette catégorie</p>
        </div>
      );
    }

    if (enableDragDrop && sortMode === 'manual') {
      return (
        <DndContext
          sensors={sensors}
          collisionDetection={closestCenter}
          onDragStart={handleDragStart}
          onDragEnd={(event) => handleDragEnd(event, clientsList)}
        >
          <SortableContext
            items={clientsList.map(client => client.id)}
            strategy={rectSortingStrategy}
          >
            <div className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 scrollbar-hide ${isDragging ? 'cursor-grabbing' : ''}`}>
              {clientsList.map((client) => {
                const progress = getClientProgress(client);
                const complianceProgress = getComplianceProgress(client.id);
                const hasUrgent = client.steps.some(step => step.status === 'urgent');

                return (
                  <ClientContextMenu
                    key={client.id}
                    client={client}
                    onToggleCompleted={updateClientCompleted}
                  >
                    <SortableClientCard
                      client={client}
                      progress={progress}
                      complianceProgress={complianceProgress}
                      hasUrgent={hasUrgent}
                    />
                  </ClientContextMenu>
                );
              })}
            </div>
          </SortableContext>
        </DndContext>
      );
    }

    // Mode sans glisser-déposer
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 scrollbar-hide">
        {clientsList.map((client) => {
          const progress = getClientProgress(client);
          const complianceProgress = getComplianceProgress(client.id);
          const hasUrgent = client.steps.some(step => step.status === 'urgent');

          return (
            <ClientContextMenu
              key={client.id}
              client={client}
              onToggleCompleted={updateClientCompleted}
            >
              <Link
                to={`/client/${client.id}`}
                className="h-full"
              >
                <div className={`h-full rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300 hover:border-blue-300 ${
                  hasUrgent ? 'border-pink-300 shadow-md' : ''
                }`}>
                  <div className="p-6">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-medium truncate">{client.name}</h3>
                      {hasUrgent && (
                        <div className="bg-pink-100 p-0.5 rounded-full">
                          <AlertCircle className="h-3 w-3 text-pink-600" />
                        </div>
                      )}
                    </div>

                    {/* Date de rendu du dossier */}
                    {client.dueDate && (
                      <div className="mb-4">
                        <DueDateDisplay dueDate={client.dueDate} variant="card" />
                      </div>
                    )}

                    {/* Barre de progression des étapes */}
                    <div className="mb-3">
                      <div className="text-xs text-gray-500 mb-1 font-medium">Étapes</div>
                      <ProgressBar
                        completed={progress.completed}
                        total={progress.total}
                        className="text-xs"
                      />
                    </div>

                    {/* Barre de progression de conformité */}
                    <div>
                      <div className="text-xs text-gray-500 mb-1 font-medium">Conformité</div>
                      <ProgressBar
                        completed={complianceProgress.completed}
                        total={complianceProgress.total}
                        className="text-xs"
                      />
                    </div>
                  </div>
                </div>
              </Link>
            </ClientContextMenu>
          );
        })}
      </div>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto py-16 px-4 flex flex-col items-center justify-center">
        <Loader2 className="h-10 w-10 text-blue-600 animate-spin mb-4" />
        <p className="text-lg text-gray-600">Chargement des données...</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 max-w-5xl scrollbar-hide bg-white">
      <div className="flex items-center justify-between mb-8 responsive-header bg-white border-b pb-4">
        <div className="flex items-center">
          <Logo size="lg" className="mr-4" />
          <h1 className="text-3xl font-bold text-blue-600">
            Suivi des Dossiers Clients
          </h1>
        </div>
        <div className="flex items-center gap-4">
          {/* Toujours afficher le sélecteur de profil et le sélecteur de thème */}
          <div className="flex items-center mr-4">
            <ProfileSelect />
          </div>

          {/* Toujours afficher les boutons de notification et ping */}
          <div className="flex items-center gap-3">
            <NotificationDropdown />
            <PingDropdown />
          </div>
        </div>
      </div>

      {urgentTasks.length > 0 && (
        <UrgentTasksList urgentTasks={urgentTasks} />
      )}

      <div className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4 responsive-form-row">
        <div className="relative flex-1 w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={18} />
          <Input
            id="client-search"
            name="client-search"
            placeholder="Rechercher un client..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
          />
        </div>

        <div className="flex items-center gap-3">
          <Select value={sortMode} onValueChange={(value) => setSortMode(value as 'manual' | 'priority')}>
            <SelectTrigger className="w-[200px] border-gray-200 hover:border-blue-300 transition-colors">
              <div className="flex items-center">
                <ArrowUpDown className="mr-2 h-4 w-4 text-blue-500" />
                <SelectValue placeholder="Trier par..." />
              </div>
            </SelectTrigger>
            <SelectContent className="bg-white shadow-lg border-gray-200">
              <SelectItem value="manual" className="focus:bg-blue-50">
                <div className="flex items-center">
                  <span className="font-medium">Ordre personnalisé</span>
                </div>
              </SelectItem>
              <SelectItem value="priority" className="focus:bg-blue-50">
                <div className="flex items-center">
                  <span className="font-medium">Par priorité</span>
                </div>
              </SelectItem>
            </SelectContent>
          </Select>

          <Button
            onClick={() => setIsGlobalPatrimoineOpen(true)}
            variant="outline"
            className="whitespace-nowrap border-green-200 text-green-700 hover:bg-green-50 hover:border-green-300"
          >
            <TrendingUp size={18} className="mr-2" />
            Patrimoine Global
          </Button>

          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button className="whitespace-nowrap bg-blue-600 hover:bg-blue-700 text-white">
              <Plus size={18} className="mr-2" />
              Nouveau client
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Ajouter un nouveau client</DialogTitle>
            </DialogHeader>
            <div className="py-4">
              <Input
                placeholder="Nom du client"
                value={newClientName}
                onChange={(e) => setNewClientName(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleAddClient()}
                autoFocus
              />
            </div>
            <DialogFooter>
              <Button
                onClick={handleAddClient}
                disabled={!newClientName.trim() || isAdding}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isAdding ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Ajout en cours...
                  </>
                ) : (
                  <>Ajouter</>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        </div>
      </div>

      {filteredClients.length === 0 ? (
        <div className="text-center py-12 bg-gray-50 rounded-lg border border-gray-200">
          <div className="flex flex-col items-center justify-center">
            <div className="bg-gray-100 p-3 rounded-full mb-4">
              <Search className="h-6 w-6 text-gray-500" />
            </div>
            <p className="text-lg font-medium text-gray-700 mb-1">
              {searchQuery ? 'Aucun client ne correspond à votre recherche' : 'Aucun client enregistré'}
            </p>
            <p className="text-gray-500 max-w-md mx-auto">
              {searchQuery
                ? 'Essayez un autre terme de recherche ou ajoutez un nouveau client.'
                : 'Commencez par ajouter votre premier client en cliquant sur le bouton "Nouveau client".'}
            </p>
          </div>
        </div>
      ) : (
        <Tabs defaultValue="active" className="w-full">
          <TabsList className="grid w-full grid-cols-2 mb-6">
            <TabsTrigger value="active" className="flex items-center gap-2">
              Clients actifs
              {sortedActiveClients.length > 0 && (
                <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
                  {sortedActiveClients.length}
                </span>
              )}
            </TabsTrigger>
            <TabsTrigger value="completed" className="flex items-center gap-2">
              Clients terminés
              {sortedCompletedClients.length > 0 && (
                <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-full">
                  {sortedCompletedClients.length}
                </span>
              )}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="active">
            {renderClientCards(sortedActiveClients, true)}
          </TabsContent>

          <TabsContent value="completed">
            {renderClientCards(sortedCompletedClients, false)}
          </TabsContent>
        </Tabs>
      )}

      {/* Tableau Global du Patrimoine */}
      <GlobalPatrimoineTable
        isOpen={isGlobalPatrimoineOpen}
        onClose={() => setIsGlobalPatrimoineOpen(false)}
      />
    </div>
  );
};

export default Index;
