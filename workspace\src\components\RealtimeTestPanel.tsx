import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { Wifi, WifiOff, Activity, Users, BarChart3 } from 'lucide-react';

interface RealtimeEvent {
  id: string;
  timestamp: string;
  table: string;
  event: string;
  payload: any;
}

export const RealtimeTestPanel: React.FC = () => {
  const [events, setEvents] = useState<RealtimeEvent[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<Record<string, string>>({});

  useEffect(() => {
    console.log('🔧 Initialisation du panel de test temps réel...');

    // Canal pour patrimoine_evolution
    const channel1 = supabase
      .channel('realtime-test-patrimoine-evolution')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'patrimoine_evolution'
      }, (payload) => {
        console.log('📊 Événement patrimoine_evolution:', payload);
        const newEvent: RealtimeEvent = {
          id: `${Date.now()}-patrimoine`,
          timestamp: new Date().toLocaleTimeString('fr-FR'),
          table: 'patrimoine_evolution',
          event: payload.eventType,
          payload: payload
        };
        setEvents(prev => [newEvent, ...prev].slice(0, 10));
      })
      .subscribe((status) => {
        console.log('📊 Statut patrimoine_evolution:', status);
        setConnectionStatus(prev => ({ ...prev, patrimoine_evolution: status }));
        if (status === 'SUBSCRIBED') {
          setIsConnected(true);
        }
      });

    // Canal pour patrimoine_evolution_fournisseurs
    const channel2 = supabase
      .channel('realtime-test-patrimoine-evolution-fournisseurs')
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: 'patrimoine_evolution_fournisseurs'
      }, (payload) => {
        console.log('👥 Événement patrimoine_evolution_fournisseurs:', payload);
        const newEvent: RealtimeEvent = {
          id: `${Date.now()}-fournisseurs`,
          timestamp: new Date().toLocaleTimeString('fr-FR'),
          table: 'patrimoine_evolution_fournisseurs',
          event: payload.eventType,
          payload: payload
        };
        setEvents(prev => [newEvent, ...prev].slice(0, 10));
      })
      .subscribe((status) => {
        console.log('👥 Statut patrimoine_evolution_fournisseurs:', status);
        setConnectionStatus(prev => ({ ...prev, patrimoine_evolution_fournisseurs: status }));
      });

    return () => {
      console.log('🧹 Nettoyage des canaux de test...');
      supabase.removeChannel(channel1);
      supabase.removeChannel(channel2);
    };
  }, []);

  const testInsertSnapshot = async () => {
    try {
      const { data, error } = await supabase
        .from('patrimoine_evolution')
        .insert({
          date_snapshot: new Date().toISOString().split('T')[0],
          total_sous_gestion: 'test_' + Date.now(),
          devise: 'EUR',
          commentaire: 'Test temps réel depuis interface'
        })
        .select()
        .single();

      if (error) throw error;
      console.log('✅ Test insertion snapshot réussie:', data);
    } catch (error) {
      console.error('❌ Erreur test insertion snapshot:', error);
    }
  };

  const testInsertFournisseur = async () => {
    try {
      const { data, error } = await supabase
        .from('patrimoine_evolution_fournisseurs')
        .insert({
          date_snapshot: new Date().toISOString().split('T')[0],
          fournisseur_id: 'test-fournisseur-' + Date.now(),
          montant_chiffre: 'test_' + Date.now(),
          devise: 'EUR',
          commentaire: 'Test temps réel fournisseur depuis interface'
        })
        .select()
        .single();

      if (error) throw error;
      console.log('✅ Test insertion fournisseur réussie:', data);
    } catch (error) {
      console.error('❌ Erreur test insertion fournisseur:', error);
    }
  };

  const getEventIcon = (table: string) => {
    if (table === 'patrimoine_evolution') return <BarChart3 className="h-3 w-3" />;
    if (table === 'patrimoine_evolution_fournisseurs') return <Users className="h-3 w-3" />;
    return <Activity className="h-3 w-3" />;
  };

  const getEventColor = (event: string) => {
    switch (event) {
      case 'INSERT': return 'bg-green-100 text-green-800';
      case 'UPDATE': return 'bg-blue-100 text-blue-800';
      case 'DELETE': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          {isConnected ? (
            <Wifi className="h-5 w-5 text-green-600" />
          ) : (
            <WifiOff className="h-5 w-5 text-red-600" />
          )}
          Test Temps Réel
          <Badge variant={isConnected ? "default" : "destructive"} className="ml-2">
            {isConnected ? 'Connecté' : 'Déconnecté'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Statuts de connexion */}
        <div className="grid grid-cols-2 gap-2">
          {Object.entries(connectionStatus).map(([table, status]) => (
            <div key={table} className="flex items-center gap-2 text-xs">
              <div className={`w-2 h-2 rounded-full ${
                status === 'SUBSCRIBED' ? 'bg-green-500' : 
                status === 'CLOSED' ? 'bg-red-500' : 'bg-yellow-500'
              }`} />
              <span className="truncate">{table}</span>
              <Badge variant="outline" className="text-xs">
                {status}
              </Badge>
            </div>
          ))}
        </div>

        {/* Boutons de test */}
        <div className="flex gap-2">
          <Button onClick={testInsertSnapshot} size="sm" variant="outline">
            <BarChart3 className="h-4 w-4 mr-1" />
            Test Snapshot
          </Button>
          <Button onClick={testInsertFournisseur} size="sm" variant="outline">
            <Users className="h-4 w-4 mr-1" />
            Test Fournisseur
          </Button>
          <Button onClick={() => setEvents([])} size="sm" variant="ghost">
            Effacer
          </Button>
        </div>

        {/* Événements en temps réel */}
        <div className="space-y-2 max-h-64 overflow-y-auto">
          <div className="text-sm font-medium">Événements récents :</div>
          {events.length === 0 ? (
            <div className="text-xs text-gray-500 text-center py-4">
              Aucun événement détecté. Utilisez les boutons de test ou modifiez des données.
            </div>
          ) : (
            events.map((event) => (
              <div key={event.id} className="flex items-center gap-2 p-2 bg-gray-50 rounded text-xs">
                {getEventIcon(event.table)}
                <span className="font-mono">{event.timestamp}</span>
                <Badge className={getEventColor(event.event)}>
                  {event.event}
                </Badge>
                <span className="truncate flex-1">{event.table}</span>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};
