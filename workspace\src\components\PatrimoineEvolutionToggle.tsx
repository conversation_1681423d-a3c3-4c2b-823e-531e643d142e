import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ChevronDown, ChevronUp, Save, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { usePatrimoineEvolutionSimple } from '@/hooks/usePatrimoineEvolutionSimple';

interface PatrimoineEvolutionToggleProps {
  currentTotal: number;
  fournisseurs: Array<{ id: string; nom: string; total: number }>;
}

export const PatrimoineEvolutionToggle: React.FC<PatrimoineEvolutionToggleProps> = ({
  currentTotal,
  fournisseurs
}) => {
  const [showEvolutionTable, setShowEvolutionTable] = useState(false);
  const [editingSnapshot, setEditingSnapshot] = useState<{ 
    index: number; 
    date: string; 
    total: string; 
    commentaire: string 
  } | null>(null);
  const [editingField, setEditingField] = useState<'date' | 'total' | null>(null);

  const {
    snapshots,
    upsertSnapshot,
    deleteSnapshot,
    formatMontant: formatMontantEvolution
  } = usePatrimoineEvolutionSimple();

  const handleSaveSnapshotEdit = async () => {
    if (!editingSnapshot) return;

    const total = parseFloat(editingSnapshot.total);
    if (isNaN(total) || total < 0) {
      toast.error('Le total doit être un nombre positif valide');
      return;
    }

    if (!editingSnapshot.date) {
      toast.error('Veuillez sélectionner une date');
      return;
    }

    const success = await upsertSnapshot(
      editingSnapshot.date,
      total,
      editingSnapshot.commentaire || undefined
    );

    if (success) {
      setEditingSnapshot(null);
      setEditingField(null);
    }
  };

  const handleDeleteSnapshot = async (snapshotId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce snapshot ?')) return;
    await deleteSnapshot(snapshotId);
  };

  return (
    <div className="mt-4 border-t border-gray-200 pt-3">
      <div className="flex items-center justify-between">
        <button
          onClick={() => setShowEvolutionTable(!showEvolutionTable)}
          className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-800 transition-colors"
        >
          {showEvolutionTable ? (
            <ChevronUp className="h-3 w-3" />
          ) : (
            <ChevronDown className="h-3 w-3" />
          )}
          <span>Évolution temporelle</span>
          {snapshots.length > 0 && (
            <span className="bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded-full text-xs">
              {snapshots.length}
            </span>
          )}
        </button>
        
        {showEvolutionTable && (
          <button
            onClick={async () => {
              const today = new Date().toISOString().split('T')[0];
              const success = await upsertSnapshot(today, currentTotal);
              if (success) {
                toast.success('Total actuel figé dans l\'évolution');
              }
            }}
            className="flex items-center gap-1 text-xs text-green-600 hover:text-green-800 bg-green-50 hover:bg-green-100 px-2 py-1 rounded transition-colors"
          >
            <Save className="h-3 w-3" />
            <span>Figer total actuel</span>
          </button>
        )}
      </div>

      {showEvolutionTable && (
        <div className="mt-3 bg-blue-50/50 border border-blue-200 rounded-lg overflow-hidden">
          <div className="bg-blue-100 px-3 py-2 border-b border-blue-200">
            <h4 className="text-xs font-medium text-blue-800">Points d'évolution du patrimoine</h4>
          </div>
          
          <div className="p-3 space-y-2">
            {Array.from({ length: 4 }, (_, index) => {
              const snapshot = snapshots[index];
              return (
                <div key={index} className="grid grid-cols-12 gap-2 items-center py-1">
                  {/* Date */}
                  <div className="col-span-3">
                    {editingSnapshot && editingField === 'date' && editingSnapshot.index === index ? (
                      <Input
                        type="date"
                        value={editingSnapshot.date}
                        onChange={(e) => setEditingSnapshot({ ...editingSnapshot, date: e.target.value })}
                        className="w-full text-xs border-blue-300 focus:border-blue-500"
                        autoFocus
                        onBlur={() => {
                          handleSaveSnapshotEdit();
                          setEditingField(null);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleSaveSnapshotEdit();
                            setEditingField(null);
                          }
                          if (e.key === 'Escape') {
                            setEditingSnapshot(null);
                            setEditingField(null);
                          }
                        }}
                      />
                    ) : (
                      <div
                        className="text-xs text-blue-700 cursor-pointer hover:bg-blue-100 px-2 py-1 rounded"
                        onClick={() => {
                          if (snapshot) {
                            setEditingSnapshot({
                              index,
                              date: snapshot.date,
                              total: snapshot.total.toString(),
                              commentaire: snapshot.commentaire || ''
                            });
                            setEditingField('date');
                          } else {
                            const today = new Date().toISOString().split('T')[0];
                            setEditingSnapshot({ index, date: today, total: '0', commentaire: '' });
                            setEditingField('date');
                          }
                        }}
                      >
                        {snapshot ? new Date(snapshot.date).toLocaleDateString('fr-FR') : 'Cliquer pour date'}
                      </div>
                    )}
                  </div>

                  {/* Colonnes fournisseurs */}
                  {fournisseurs.map((fournisseur) => (
                    <div key={fournisseur.id} className="col-span-1 text-center">
                      <div
                        className="text-xs text-gray-500 cursor-pointer hover:bg-blue-100 px-1 py-1 rounded"
                        onDoubleClick={() => {
                          toast.info(`Édition ${fournisseur.nom} - À implémenter`);
                        }}
                      >
                        -
                      </div>
                    </div>
                  ))}

                  {/* Total */}
                  <div className="col-span-2">
                    {editingSnapshot && editingField === 'total' && editingSnapshot.index === index ? (
                      <Input
                        type="number"
                        value={editingSnapshot.total}
                        onChange={(e) => setEditingSnapshot({ ...editingSnapshot, total: e.target.value })}
                        className="w-full text-xs border-blue-300 focus:border-blue-500"
                        autoFocus
                        onBlur={() => {
                          handleSaveSnapshotEdit();
                          setEditingField(null);
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleSaveSnapshotEdit();
                            setEditingField(null);
                          }
                          if (e.key === 'Escape') {
                            setEditingSnapshot(null);
                            setEditingField(null);
                          }
                        }}
                      />
                    ) : (
                      <div
                        className="text-xs font-medium text-blue-700 cursor-pointer hover:bg-blue-100 px-2 py-1 rounded text-center"
                        onDoubleClick={() => {
                          if (snapshot) {
                            setEditingSnapshot({
                              index,
                              date: snapshot.date,
                              total: snapshot.total.toString(),
                              commentaire: snapshot.commentaire || ''
                            });
                            setEditingField('total');
                          } else {
                            const today = new Date().toISOString().split('T')[0];
                            setEditingSnapshot({ index, date: today, total: '0', commentaire: '' });
                            setEditingField('total');
                          }
                        }}
                      >
                        {snapshot ? formatMontantEvolution(snapshot.total) : 'Double-clic'}
                      </div>
                    )}
                  </div>

                  {/* Actions */}
                  <div className="col-span-1 text-center">
                    {snapshot && (
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteSnapshot(snapshot.id)}
                        className="text-red-500 hover:bg-red-50 h-6 w-6 p-0"
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              );
            })}

            {/* En-têtes des colonnes */}
            <div className="grid grid-cols-12 gap-2 items-center py-1 border-t border-blue-200 pt-2 mt-2">
              <div className="col-span-3 text-xs font-medium text-blue-600">Date</div>
              {fournisseurs.map((fournisseur) => (
                <div key={fournisseur.id} className="col-span-1 text-xs font-medium text-blue-600 text-center truncate" title={fournisseur.nom}>
                  {fournisseur.nom.substring(0, 3)}
                </div>
              ))}
              <div className="col-span-2 text-xs font-medium text-blue-600 text-center">Total</div>
              <div className="col-span-1"></div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
