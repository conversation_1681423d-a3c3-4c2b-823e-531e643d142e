import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ChevronDown, ChevronUp, Save, Trash2 } from 'lucide-react';
import { toast } from 'sonner';
import { usePatrimoineEvolutionSimple } from '@/hooks/usePatrimoineEvolutionSimple';
import { usePatrimoineEvolutionFournisseurs } from '@/hooks/usePatrimoineEvolutionFournisseurs';

interface PatrimoineEvolutionToggleProps {
  currentTotal: number;
  fournisseurs: Array<{ id: string; nom: string; total: number }>;
}

export const PatrimoineEvolutionToggle: React.FC<PatrimoineEvolutionToggleProps> = ({
  currentTotal,
  fournisseurs
}) => {
  const [showEvolutionTable, setShowEvolutionTable] = useState(false);
  const [editingCell, setEditingCell] = useState<{
    index: number;
    date: string;
    fournisseurId?: string;
    field: 'date' | 'total' | 'fournisseur';
    value: string;
  } | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const {
    snapshots,
    upsertSnapshot,
    deleteSnapshot,
    formatMontant: formatMontantEvolution
  } = usePatrimoineEvolutionSimple();

  const {
    evolutions,
    upsertEvolution,
    getEvolutionsByDate,
    formatMontant: formatMontantFournisseur,
    loading: loadingFournisseurs,
    error: errorFournisseurs
  } = usePatrimoineEvolutionFournisseurs();

  const handleSaveEdit = async () => {
    if (!editingCell || isLoading) return;

    setIsLoading(true);
    try {
      if (editingCell.field === 'date') {
        if (!editingCell.value) {
          toast.error('Veuillez sélectionner une date');
          return;
        }
        // Pour l'instant, on ne fait que fermer l'édition de date
        setEditingCell(null);
        return;
      }

      if (editingCell.field === 'fournisseur' && editingCell.fournisseurId) {
        const montant = parseFloat(editingCell.value);
        if (isNaN(montant) || montant < 0) {
          toast.error('Le montant doit être un nombre positif valide');
          return;
        }

        const success = await upsertEvolution(
          editingCell.date,
          editingCell.fournisseurId,
          montant
        );

        if (success) {
          setEditingCell(null);
          toast.success('💰 Montant sauvegardé avec succès');
        }
        return;
      }

      if (editingCell.field === 'total') {
        const total = parseFloat(editingCell.value);
        if (isNaN(total) || total < 0) {
          toast.error('Le total doit être un nombre positif valide');
          return;
        }

        const success = await upsertSnapshot(
          editingCell.date,
          total
        );

        if (success) {
          setEditingCell(null);
          toast.success('📊 Total sauvegardé avec succès');
        }
      }
    } catch (error) {
      console.error('Erreur lors de la sauvegarde:', error);
      toast.error('Erreur lors de la sauvegarde');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteSnapshot = async (snapshotId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce snapshot ?')) return;
    await deleteSnapshot(snapshotId);
  };

  // Obtenir les données combinées pour l'affichage
  const getCombinedData = () => {
    const evolutionsByDate = getEvolutionsByDate();
    const allDates = new Set([
      ...snapshots.map(s => s.date),
      ...evolutionsByDate.map(e => e.date)
    ]);

    return Array.from(allDates)
      .sort((a, b) => new Date(a).getTime() - new Date(b).getTime())
      .slice(0, 4) // Limiter à 4 lignes
      .map(date => {
        const snapshot = snapshots.find(s => s.date === date);
        const evolution = evolutionsByDate.find(e => e.date === date);

        return {
          date,
          snapshot,
          evolution,
          fournisseurMontants: evolution?.fournisseurs || {},
          totalCalcule: evolution?.total || 0,
          totalSnapshot: snapshot?.total || 0
        };
      });
  };

  return (
    <div className="p-4 bg-white">
      <div className="flex items-center justify-between mb-3">
        <button
          onClick={() => setShowEvolutionTable(!showEvolutionTable)}
          className="flex items-center gap-3 text-sm font-medium text-blue-700 hover:text-blue-900 transition-colors group"
        >
          <div className="flex items-center gap-2">
            {showEvolutionTable ? (
              <ChevronUp className="h-4 w-4 group-hover:scale-110 transition-transform" />
            ) : (
              <ChevronDown className="h-4 w-4 group-hover:scale-110 transition-transform" />
            )}
            <span className="font-semibold">Évolution Temporelle</span>
          </div>
          {snapshots.length > 0 && (
            <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium border border-blue-200">
              {snapshots.length} point{snapshots.length > 1 ? 's' : ''}
            </span>
          )}
        </button>
        
        {showEvolutionTable && (
          <Button
            onClick={async () => {
              if (isLoading) return;
              setIsLoading(true);
              try {
                const today = new Date().toISOString().split('T')[0];
                const success = await upsertSnapshot(today, currentTotal);
                if (success) {
                  toast.success('🎯 Total actuel figé dans l\'évolution');
                }
              } catch (error) {
                console.error('Erreur lors du figement:', error);
                toast.error('Erreur lors du figement du total');
              } finally {
                setIsLoading(false);
              }
            }}
            variant="outline"
            size="sm"
            disabled={isLoading}
            className="flex items-center gap-2 text-green-700 border-green-200 hover:bg-green-50 hover:border-green-300 transition-all disabled:opacity-50"
          >
            {isLoading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
            ) : (
              <Save className="h-4 w-4" />
            )}
            <span className="font-medium">
              {isLoading ? 'Figement...' : 'Figer Total Actuel'}
            </span>
          </Button>
        )}
      </div>

      {showEvolutionTable && (
        <div className="mt-3 bg-blue-50/50 border border-blue-200 rounded-lg overflow-hidden">
          <div className="bg-blue-100 px-3 py-2 border-b border-blue-200">
            <h4 className="text-sm font-medium text-blue-800">Points d'évolution du patrimoine par fournisseur</h4>
          </div>

          <div className="p-3 max-h-96 overflow-y-auto space-y-2">
            {/* Indicateur d'erreur */}
            {errorFournisseurs && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-3">
                <div className="text-red-800 text-sm font-medium">⚠️ Erreur de chargement</div>
                <div className="text-red-600 text-xs mt-1">{errorFournisseurs}</div>
              </div>
            )}

            {/* Indicateur de chargement */}
            {loadingFournisseurs && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                <div className="flex items-center gap-2 text-blue-800 text-sm">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  <span>Chargement des données d'évolution...</span>
                </div>
              </div>
            )}

            {/* En-tête du tableau */}
            <div className="grid gap-2 items-center py-2 border-b-2 border-blue-300 bg-blue-50 font-medium text-xs" style={{ gridTemplateColumns: `2fr ${fournisseurs.map(() => '1fr').join(' ')} 2fr 1fr` }}>
              <div className="text-blue-800">📅 Date</div>
              {fournisseurs.map((fournisseur) => (
                <div key={fournisseur.id} className="text-center text-blue-800 truncate" title={fournisseur.nom}>
                  💼 {fournisseur.nom}
                </div>
              ))}
              <div className="text-center text-blue-800">💰 Total</div>
              <div className="text-center text-blue-800">⚙️ Actions</div>
            </div>

            {getCombinedData().map((rowData, index) => (
              <div key={rowData.date || `empty-${index}`} className="grid gap-2 items-center py-2 border-b border-blue-100 last:border-b-0" style={{ gridTemplateColumns: `2fr ${fournisseurs.map(() => '1fr').join(' ')} 2fr 1fr` }}>
                {/* Date */}
                <div className="text-xs">
                  {editingCell && editingCell.field === 'date' && editingCell.index === index ? (
                    <Input
                      type="date"
                      value={editingCell.value}
                      onChange={(e) => setEditingCell({ ...editingCell, value: e.target.value })}
                      className="w-full text-xs border-blue-300 focus:border-blue-500"
                      autoFocus
                      onBlur={handleSaveEdit}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSaveEdit();
                        if (e.key === 'Escape') setEditingCell(null);
                      }}
                    />
                  ) : (
                    <div
                      className={`text-blue-700 cursor-pointer hover:bg-blue-100 px-2 py-1 rounded font-medium transition-all duration-200 transform hover:scale-105 ${
                        isLoading ? 'opacity-50 pointer-events-none' : ''
                      }`}
                      onClick={() => {
                        if (isLoading) return;
                        const dateValue = rowData.date || new Date().toISOString().split('T')[0];
                        setEditingCell({
                          index,
                          date: dateValue,
                          field: 'date',
                          value: dateValue
                        });
                      }}
                      title="Cliquez pour modifier la date"
                    >
                      {rowData.date ? `📅 ${new Date(rowData.date).toLocaleDateString('fr-FR')}` : '📅 Cliquer pour date'}
                    </div>
                  )}
                </div>

                {/* Colonnes fournisseurs */}
                {fournisseurs.map((fournisseur) => {
                  const montant = rowData.fournisseurMontants[fournisseur.id] || 0;
                  const isEditing = editingCell &&
                    editingCell.field === 'fournisseur' &&
                    editingCell.fournisseurId === fournisseur.id &&
                    editingCell.index === index;

                  return (
                    <div key={fournisseur.id} className="text-center">
                      {isEditing ? (
                        <Input
                          type="number"
                          value={editingCell.value}
                          onChange={(e) => setEditingCell({ ...editingCell, value: e.target.value })}
                          className="w-full text-xs border-blue-300 focus:border-blue-500"
                          autoFocus
                          onBlur={handleSaveEdit}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') handleSaveEdit();
                            if (e.key === 'Escape') setEditingCell(null);
                          }}
                        />
                      ) : (
                        <div
                          className={`text-xs cursor-pointer hover:bg-blue-100 px-2 py-1 rounded transition-all duration-200 transform hover:scale-105 ${
                            montant > 0 ? 'text-green-700 font-medium bg-green-50 border border-green-200' : 'text-gray-400 hover:text-blue-600'
                          } ${isLoading ? 'opacity-50 pointer-events-none' : ''}`}
                          onDoubleClick={() => {
                            if (isLoading) return;
                            if (!rowData.date) {
                              toast.error('⚠️ Veuillez d\'abord définir une date');
                              return;
                            }
                            setEditingCell({
                              index,
                              date: rowData.date,
                              field: 'fournisseur',
                              fournisseurId: fournisseur.id,
                              value: montant.toString()
                            });
                          }}
                          title={`Double-cliquez pour éditer le montant ${fournisseur.nom}`}
                        >
                          {montant > 0 ? formatMontantFournisseur(montant) : '💰 Double-clic'}
                        </div>
                      )}
                    </div>
                  );
                })}

                {/* Total */}
                <div className="text-center">
                  {editingCell && editingCell.field === 'total' && editingCell.index === index ? (
                    <Input
                      type="number"
                      value={editingCell.value}
                      onChange={(e) => setEditingCell({ ...editingCell, value: e.target.value })}
                      className="w-full text-xs border-blue-300 focus:border-blue-500"
                      autoFocus
                      onBlur={handleSaveEdit}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') handleSaveEdit();
                        if (e.key === 'Escape') setEditingCell(null);
                      }}
                    />
                  ) : (
                    <div
                      className={`text-xs font-medium cursor-pointer hover:bg-blue-100 px-2 py-1 rounded transition-all duration-200 transform hover:scale-105 ${
                        rowData.totalSnapshot > 0 ? 'text-green-700 bg-green-50 border border-green-200' :
                        rowData.totalCalcule > 0 ? 'text-blue-700 bg-blue-50 border border-blue-200' : 'text-gray-400 hover:text-blue-600'
                      } ${isLoading ? 'opacity-50 pointer-events-none' : ''}`}
                      onDoubleClick={() => {
                        if (isLoading) return;
                        if (!rowData.date) {
                          toast.error('⚠️ Veuillez d\'abord définir une date');
                          return;
                        }
                        setEditingCell({
                          index,
                          date: rowData.date,
                          field: 'total',
                          value: (rowData.totalSnapshot || rowData.totalCalcule || 0).toString()
                        });
                      }}
                      title="Double-cliquez pour éditer le total global"
                    >
                      {rowData.totalSnapshot > 0
                        ? `🎯 ${formatMontantEvolution(rowData.totalSnapshot)}`
                        : rowData.totalCalcule > 0
                        ? `📊 ${formatMontantFournisseur(rowData.totalCalcule)}`
                        : '💰 Double-clic'
                      }
                    </div>
                  )}
                </div>

                {/* Actions */}
                <div className="text-center">
                  {rowData.snapshot && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteSnapshot(rowData.snapshot!.id)}
                      className="text-red-500 hover:bg-red-50 h-6 w-6 p-0"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            ))}

            {/* Message d'aide */}
            {getCombinedData().length === 0 && !loadingFournisseurs && (
              <div className="text-center py-8 text-gray-500">
                <div className="text-sm mb-2">🎯 Aucun point d'évolution défini</div>
                <div className="text-xs">
                  Utilisez le bouton "Figer Total Actuel" pour commencer à suivre l'évolution
                </div>
              </div>
            )}

            {/* Légende */}
            {getCombinedData().length > 0 && (
              <div className="mt-4 pt-3 border-t border-blue-200 bg-blue-50/30 rounded-lg p-2">
                <div className="text-xs text-blue-600 space-y-1">
                  <div className="font-medium mb-2">💡 Guide d'utilisation :</div>
                  <div>• 📅 <strong>Clic sur date</strong> : Modifier la date</div>
                  <div>• 💰 <strong>Double-clic sur fournisseur</strong> : Éditer le montant</div>
                  <div>• 📊 <strong>Double-clic sur total</strong> : Éditer le total global</div>
                  <div>• 🎯 <strong>Total vert</strong> : Saisi manuellement</div>
                  <div>• 📊 <strong>Total bleu</strong> : Calculé automatiquement</div>
                </div>
              </div>
            )}


          </div>
        </div>
      )}
    </div>
  );
};
