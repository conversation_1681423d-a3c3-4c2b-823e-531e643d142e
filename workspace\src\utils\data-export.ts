/**
 * Utilitaire d'exportation sécurisée des données personnelles
 * Conforme aux recommandations de la CNIL pour la protection des données
 */

import { supabase } from "@/integrations/supabase/client";
import { Client, Step, Assignee, Profile, Notification, Ping } from "@/types";
import { logDataAccess, DataOperationType } from "./secure-logging";

/**
 * Interface pour les données exportées d'un utilisateur
 */
export interface UserDataExport {
  profile: Profile;
  assignedSteps: {
    step: Step;
    client: {
      id: string;
      name: string;
    };
  }[];
  notifications: Notification[];
  sentPings: Ping[];
  receivedPings: Ping[];
  exportDate: string;
  exportFormat: string;
}

/**
 * Exporte toutes les données personnelles d'un utilisateur
 * @param userId - ID de l'utilisateur
 * @returns Données exportées au format JSON
 */
export async function exportUserData(userId: string): Promise<UserDataExport | null> {
  try {
    // Journaliser l'opération d'export
    await logDataAccess(
      DataOperationType.EXPORT,
      'all',
      undefined,
      userId,
      'Export complet des données utilisateur'
    );

    // Récupérer le profil de l'utilisateur
    const { data: profileData, error: profileError } = await supabase
      .from('assignees')
      .select('*')
      .eq('id', userId)
      .single();

    if (profileError) {
      console.error('Erreur lors de la récupération du profil:', profileError);
      return null;
    }

    const profile: Profile = {
      id: profileData.id,
      name: profileData.name,
      created_at: profileData.created_at
    };

    // Récupérer les étapes assignées à l'utilisateur
    const { data: assignmentsData, error: assignmentsError } = await supabase
      .from('step_assignments')
      .select('*, steps(*), steps:steps(client_id)')
      .eq('assignee_id', userId);

    if (assignmentsError) {
      console.error('Erreur lors de la récupération des assignations:', assignmentsError);
      return null;
    }

    // Récupérer les informations des clients pour les étapes assignées
    const clientIds = [...new Set(assignmentsData.map(a => a.steps.client_id))];
    const { data: clientsData, error: clientsError } = await supabase
      .from('clients')
      .select('id, name')
      .in('id', clientIds);

    if (clientsError) {
      console.error('Erreur lors de la récupération des clients:', clientsError);
      return null;
    }

    // Mapper les clients par ID pour un accès facile
    const clientsById = clientsData.reduce((acc, client) => {
      acc[client.id] = client;
      return acc;
    }, {} as Record<string, { id: string; name: string }>);

    // Construire la liste des étapes assignées avec les informations des clients
    const assignedSteps = assignmentsData.map(assignment => ({
      step: {
        id: assignment.steps.id,
        name: assignment.steps.name,
        status: assignment.steps.status,
        receivedDate: assignment.steps.received_date,
        comment: assignment.steps.comment || ''
      },
      client: clientsById[assignment.steps.client_id] || { id: assignment.steps.client_id, name: 'Client inconnu' }
    }));

    // Récupérer les notifications de l'utilisateur
    const { data: notificationsData, error: notificationsError } = await supabase
      .from('notifications')
      .select('*, created_by_profile:assignees!created_by(name)')
      .eq('user_id', userId);

    if (notificationsError) {
      console.error('Erreur lors de la récupération des notifications:', notificationsError);
      return null;
    }

    // Récupérer les pings envoyés par l'utilisateur
    const { data: sentPingsData, error: sentPingsError } = await supabase
      .from('pings')
      .select('*, from_user:assignees!from_user_id(name)')
      .eq('from_user_id', userId);

    if (sentPingsError) {
      console.error('Erreur lors de la récupération des pings envoyés:', sentPingsError);
      return null;
    }

    // Récupérer les pings reçus par l'utilisateur
    const { data: receivedPingsData, error: receivedPingsError } = await supabase
      .from('pings')
      .select('*, from_user:assignees!from_user_id(name)')
      .eq('to_user_id', userId);

    if (receivedPingsError) {
      console.error('Erreur lors de la récupération des pings reçus:', receivedPingsError);
      return null;
    }

    // Construire l'objet d'export
    const exportData: UserDataExport = {
      profile,
      assignedSteps,
      notifications: notificationsData || [],
      sentPings: sentPingsData || [],
      receivedPings: receivedPingsData || [],
      exportDate: new Date().toISOString(),
      exportFormat: 'JSON'
    };

    return exportData;
  } catch (error) {
    console.error('Erreur lors de l\'export des données utilisateur:', error);
    return null;
  }
}

/**
 * Télécharge les données exportées sous forme de fichier JSON
 * @param data - Données à exporter
 * @param filename - Nom du fichier (par défaut: export_donnees_utilisateur.json)
 */
export function downloadExportedData(
  data: any,
  filename = 'export_donnees_utilisateur.json'
): void {
  try {
    // Convertir les données en JSON formaté
    const jsonString = JSON.stringify(data, null, 2);

    // Créer un blob avec les données
    const blob = new Blob([jsonString], { type: 'application/json' });

    // Créer une URL pour le blob
    const url = URL.createObjectURL(blob);

    // Créer un élément <a> pour le téléchargement
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;

    // Ajouter l'élément au DOM, cliquer dessus, puis le supprimer
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);

    // Libérer l'URL
    URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Erreur lors du téléchargement des données exportées:', error);
  }
}
