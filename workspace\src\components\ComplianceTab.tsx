import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { FileText, Loader2 } from 'lucide-react';
import { Client, SupabaseComplianceDocument } from '@/types';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Types pour les documents de conformité
type ComplianceStatus = 'manquant' | 'transmis' | 'signe_valide';

interface ComplianceDocument {
  id: string;
  name: string;
  status: ComplianceStatus;
  description: string;
}

interface ComplianceTabProps {
  client: Client;
}

// Documents de conformité par défaut
const DEFAULT_COMPLIANCE_DOCS: ComplianceDocument[] = [
  {
    id: 'lettre_mission',
    name: 'Lettre de mission',
    status: 'manquant',
    description: 'Document définissant la mission et les responsabilités'
  },
  {
    id: 'declaration_adequation',
    name: 'Déclaration adéquation',
    status: 'manquant',
    description: 'Déclaration de conformité et d\'adéquation'
  },
  {
    id: 'document_entree_relation',
    name: 'Document entrée en relation',
    status: 'manquant',
    description: 'Document d\'entrée en relation client'
  }
];

export const ComplianceTab: React.FC<ComplianceTabProps> = ({ client }) => {
  const [complianceDocs, setComplianceDocs] = useState<ComplianceDocument[]>(DEFAULT_COMPLIANCE_DOCS);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState<string | null>(null);

  // Charger les données de conformité depuis Supabase
  const loadComplianceData = useCallback(async () => {
    try {
      setLoading(true);

      // Récupérer les documents de conformité existants
      const { data: existingDocs, error } = await supabase
        .from('compliance_documents')
        .select('*')
        .eq('client_id', client.id);

      if (error) throw error;

      // Créer une map des documents existants
      const existingDocsMap = new Map(
        (existingDocs || []).map(doc => [doc.document_type, doc])
      );

      // Fusionner avec les documents par défaut
      const mergedDocs = DEFAULT_COMPLIANCE_DOCS.map(defaultDoc => {
        const existing = existingDocsMap.get(defaultDoc.id);
        return existing ? {
          ...defaultDoc,
          status: existing.status as ComplianceStatus
        } : defaultDoc;
      });

      // Créer les documents manquants dans la base
      for (const defaultDoc of DEFAULT_COMPLIANCE_DOCS) {
        if (!existingDocsMap.has(defaultDoc.id)) {
          await supabase
            .from('compliance_documents')
            .insert({
              client_id: client.id,
              document_type: defaultDoc.id,
              status: 'manquant'
            });
        }
      }

      setComplianceDocs(mergedDocs);
    } catch (error) {
      console.error('Erreur lors du chargement des données de conformité:', error);
      toast("Erreur lors du chargement des données de conformité");
    } finally {
      setLoading(false);
    }
  }, [client.id]);

  // Mettre à jour le statut d'un document
  const handleStatusChange = async (documentId: string, newStatus: ComplianceStatus) => {
    try {
      setUpdating(documentId);

      // Mettre à jour dans Supabase
      const { error } = await supabase
        .from('compliance_documents')
        .update({ status: newStatus })
        .eq('client_id', client.id)
        .eq('document_type', documentId);

      if (error) throw error;

      // Mettre à jour l'état local
      setComplianceDocs(prev =>
        prev.map(doc =>
          doc.id === documentId ? { ...doc, status: newStatus } : doc
        )
      );

      toast("Statut mis à jour avec succès");
    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      toast("Erreur lors de la mise à jour du statut");
    } finally {
      setUpdating(null);
    }
  };

  // Charger les données au montage du composant
  useEffect(() => {
    loadComplianceData();
  }, [client.id, loadComplianceData]);

  // Écouter les mises à jour temps réel
  useEffect(() => {
    const handleComplianceUpdate = (event: CustomEvent) => {
      const updatedDoc = event.detail as SupabaseComplianceDocument;

      // Vérifier si c'est pour ce client
      if (updatedDoc.client_id === client.id) {
        setComplianceDocs(prev =>
          prev.map(doc =>
            doc.id === updatedDoc.document_type
              ? { ...doc, status: updatedDoc.status as ComplianceStatus }
              : doc
          )
        );
      }
    };

    const handleComplianceDelete = (event: CustomEvent) => {
      const deletedDoc = event.detail as SupabaseComplianceDocument;

      // Vérifier si c'est pour ce client
      if (deletedDoc.client_id === client.id) {
        setComplianceDocs(prev =>
          prev.map(doc =>
            doc.id === deletedDoc.document_type
              ? { ...doc, status: 'manquant' as ComplianceStatus }
              : doc
          )
        );
      }
    };

    // Ajouter les écouteurs d'événements
    window.addEventListener('compliance-document-updated', handleComplianceUpdate as EventListener);
    window.addEventListener('compliance-document-deleted', handleComplianceDelete as EventListener);

    // Nettoyer les écouteurs au démontage
    return () => {
      window.removeEventListener('compliance-document-updated', handleComplianceUpdate as EventListener);
      window.removeEventListener('compliance-document-deleted', handleComplianceDelete as EventListener);
    };
  }, [client.id]);

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Chargement des données de conformité...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Liste des documents de conformité */}
      <div className="bg-white/80 backdrop-blur-sm rounded-xl border shadow-lg overflow-hidden">
        <div className="bg-gray-50/70 backdrop-blur-sm px-6 py-4 border-b">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            Documents de conformité
          </h3>
        </div>

        <div className="p-6">
          <div className="space-y-3">
            {complianceDocs.map((doc) => {
              const isUpdating = updating === doc.id;

              return (
                <div
                  key={doc.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50/80 transition-all duration-200"
                >
                  <div className="min-w-0 flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <h4 className="font-medium text-gray-900 truncate">{doc.name}</h4>
                    </div>
                    <p className="text-sm text-gray-600 truncate">{doc.description}</p>
                  </div>

                  <div className="ml-4 flex items-center gap-2 flex-shrink-0">
                    {isUpdating && (
                      <Loader2 className="h-4 w-4 animate-spin text-blue-600" />
                    )}
                    <Select
                      value={doc.status}
                      onValueChange={(value: ComplianceStatus) => handleStatusChange(doc.id, value)}
                      disabled={isUpdating}
                    >
                      <SelectTrigger className="w-40 border-gray-200 focus:border-blue-500 transition-colors">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="bg-white shadow-lg border-gray-200">
                        <SelectItem value="manquant" className="focus:bg-red-50">
                          <div className="flex items-center justify-center gap-2">
                            <span>🔴 Manquant</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="transmis" className="focus:bg-orange-50">
                          <div className="flex items-center justify-center gap-2">
                            <span>🟠 Transmis</span>
                          </div>
                        </SelectItem>
                        <SelectItem value="signe_valide" className="focus:bg-green-50">
                          <div className="flex items-center justify-center gap-2">
                            <span>🟢 Validé</span>
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};
