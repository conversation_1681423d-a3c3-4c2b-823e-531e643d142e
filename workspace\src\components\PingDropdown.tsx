import React, { useEffect, useState, useCallback } from 'react';
import { MessageSquare, Send } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Ping, Profile } from '@/types';
import { useProfileContext } from '@/contexts/ProfileContext';
import { usePingContext } from '@/contexts/PingContext';
import { formatDistance } from 'date-fns';
import { fr } from 'date-fns/locale';
import { Badge } from './ui/badge';
import { Input } from './ui/input';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from './ui/command';
import { useToast } from './ui/use-toast';
import { getReceivedPings, markPingAsRead as markPingAsReadService, sendPing as sendPingService } from '@/services';
import { handleError } from '@/services/errorService';

export const PingDropdown = () => {
  const { currentProfile } = useProfileContext();
  const { getPings, markPingRead, sendPing, pings: allPings, unreadCount } = usePingContext();
  const [pings, setPings] = useState<Ping[]>([]);
  const [loading, setLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [isPingOpen, setIsPingOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [selectedUser, setSelectedUser] = useState<Profile | null>(null);
  const [previousPingCount, setPreviousPingCount] = useState(0);
  const { toast, dismiss } = useToast();

  // Récupérer les profils depuis le contexte de profil
  const { profiles } = useProfileContext();

  const fetchPings = useCallback(async () => {
    setLoading(true);
    try {
      // Récupérer les pings seulement si un profil est sélectionné
      if (currentProfile) {
        await getPings();
        // Utiliser les pings du contexte
        setPings(allPings);
      } else {
        // Si aucun profil n'est sélectionné, afficher un tableau vide
        setPings([]);
      }
    } catch (error) {
      handleError(error, 'PingDropdown.fetchPings', true, 'Erreur lors de la récupération des messages');
      setPings([]);
    } finally {
      setLoading(false);
    }
  }, [currentProfile, getPings, allPings]);

  useEffect(() => {
    fetchPings();

    // Rafraîchir les pings toutes les 15 secondes au lieu de 5 secondes
    // pour réduire le nombre de requêtes et de messages dans la console
    const interval = setInterval(fetchPings, 15000);
    return () => clearInterval(interval);
  }, []);

  // Effet pour détecter les nouveaux messages et afficher une notification toast
  useEffect(() => {
    const unreadPings = pings.filter(p => !p.read);
    const unreadCount = unreadPings.length;

    // Si le nombre de pings non lus a augmenté, afficher une notification toast
    if (unreadCount > previousPingCount && !isOpen) {
      // Trouver les nouveaux pings (ceux qui n'existaient pas avant)
      const newestPing = unreadPings[0]; // Le premier est le plus récent

      if (newestPing) {
        // Afficher une notification toast qui reste visible un temps raisonnable
        toast({
          title: `Nouveau message de ${newestPing.from_user?.name || 'Un utilisateur'}`,
          description: newestPing.message,
          duration: 5000, // Reste affiché 5 secondes
          variant: "info", // Utiliser la variante info pour les nouveaux messages
          action: (
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setIsOpen(true);
                // Forcer un rafraîchissement immédiat des pings
                fetchPings();
                // Fermer toutes les notifications toast
                dismiss();
              }}
              className="mt-2"
            >
              Voir le message
            </Button>
          ),
        });
      }
    }

    // Mettre à jour le compteur précédent
    setPreviousPingCount(unreadCount);
  }, [pings, isOpen]);

  const handlePingClick = async (ping: Ping) => {
    try {
      // Marquer le ping comme lu en utilisant le contexte
      const success = await markPingRead(ping.id);

      if (success) {
        // Mettre à jour localement pour une meilleure réactivité
        setPings(pings.map(p =>
          p.id === ping.id ? { ...p, read: true } : p
        ));
      }
    } catch (error) {
      handleError(error, 'PingDropdown.handlePingClick', true, 'Erreur lors du marquage du message comme lu');
    }
  };

  // Fonction pour marquer tous les pings comme lus
  const markAllAsRead = async () => {
    try {
      // Marquer chaque ping non lu comme lu
      const unreadPings = pings.filter(p => !p.read);

      if (unreadPings.length === 0) return;

      // Mettre à jour localement immédiatement pour une meilleure réactivité
      setPings(pings.map(p => ({ ...p, read: true })));

      // Marquer les pings comme lus dans la base de données en utilisant le contexte
      const promises = unreadPings.map(ping => markPingRead(ping.id));
      await Promise.all(promises);

      // Rafraîchir les pings pour confirmer les changements
      fetchPings();
    } catch (error) {
      handleError(error, 'PingDropdown.markAllAsRead', true, 'Erreur lors du marquage de tous les messages comme lus');
      // En cas d'erreur, rafraîchir pour rétablir l'état correct
      fetchPings();
    }
  };

  const handleSendPing = async () => {
    if (!selectedUser || !message.trim() || !currentProfile) return;

    // Sauvegarder les informations du destinataire avant de réinitialiser le formulaire
    const recipientName = selectedUser.name;
    const recipientId = selectedUser.id;
    const messageContent = message.trim();

    // Afficher un toast pour indiquer que le message est en cours d'envoi
    toast({
      title: "Envoi du message en cours...",
      description: "Veuillez patienter",
      duration: 2000,
      variant: "default",
    });

    try {
      // Réinitialiser le formulaire immédiatement pour une meilleure expérience utilisateur
      setMessage('');
      setSelectedUser(null);
      setIsPingOpen(false);

      // Envoyer le message en utilisant le contexte
      const result = await sendPing(recipientId, messageContent);

      if (result) {
        // Afficher un toast pour indiquer que le message a été envoyé
        toast({
          title: "Message envoyé avec succès",
          description: `Votre message a été envoyé à ${recipientName}`,
          duration: 3000,
          variant: "success",
        });

        // Rafraîchir les pings après un court délai
        setTimeout(() => {
          fetchPings();
        }, 500);
      }
    } catch (error) {
      handleError(
        error,
        'PingDropdown.handleSendPing',
        true,
        "Erreur lors de l'envoi du message"
      );
    }
  };

  // Utiliser le compteur de notifications non lues du contexte au lieu de calculer localement
  // const localUnreadCount = pings.filter(p => !p.read).length;

  // Filtrer les profils pour ne pas inclure le profil courant
  const filteredProfiles = profiles.filter(p => p.id !== currentProfile?.id);

  return (
    <div className="flex items-center gap-2">
      <Popover open={isPingOpen} onOpenChange={setIsPingOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="default"
            size="sm"
            className="bg-blue-700 hover:bg-blue-800 text-white shadow-sm transition-all duration-200 hover:shadow-md"
          >
            <Send size={16} className="mr-1.5" />
            Ping
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-0" align="end">
          <div className="p-3 border-b">
            <h4 className="font-medium text-sm mb-2">Envoyer un message</h4>
            <div className="space-y-2">
              <div className="flex flex-col space-y-1">
                <label className="text-xs text-gray-500">Destinataire</label>
                <div className="rounded-lg border shadow-sm p-2">
                  <div className="grid grid-cols-2 gap-2 max-h-40 overflow-y-auto scrollbar-hide">
                    {filteredProfiles.map(profile => (
                      <div
                        key={profile.id}
                        onClick={() => setSelectedUser(profile)}
                        className={`flex items-center p-2 rounded-md cursor-pointer ${
                          selectedUser?.id === profile.id ? 'bg-blue-50 border border-blue-200' : 'hover:bg-gray-50'
                        }`}
                      >
                        <Avatar className="h-6 w-6 mr-2">
                          <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
                            {profile.name.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <span className="text-sm truncate">{profile.name}</span>
                        {selectedUser?.id === profile.id && (
                          <Badge className="ml-auto bg-blue-500 h-4 w-4 p-0 flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <polyline points="20 6 9 17 4 12"></polyline>
                            </svg>
                          </Badge>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="flex flex-col space-y-1">
                <label className="text-xs text-gray-500">Message</label>
                <Input
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Votre message..."
                  className="w-full"
                />
              </div>
              <Button
                onClick={handleSendPing}
                disabled={!selectedUser || !message.trim()}
                className="w-full"
              >
                Envoyer
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      <DropdownMenu open={isOpen} onOpenChange={(open) => {
        setIsOpen(open);

        // Si le menu s'ouvre, forcer un rafraîchissement immédiat des pings
        if (open) {
          console.log('Ouverture du menu des messages, rafraîchissement des pings...');
          setLoading(true); // Afficher l'indicateur de chargement

          // Forcer un rafraîchissement immédiat
          fetchPings()
            .then(() => {
              console.log('Pings rafraîchis avec succès');

              // Vérifier s'il y a des pings non lus après le rafraîchissement
              const unreadPingsCount = pings.filter(p => !p.read).length;
              console.log('Nombre de pings non lus après rafraîchissement:', unreadPingsCount);

              // Puis marquer tous les pings comme lus après un court délai
              // pour s'assurer que les pings sont bien chargés
              if (unreadPingsCount > 0) {
                setTimeout(() => {
                  console.log('Marquage de tous les pings comme lus...');
                  markAllAsRead();

                  // Fermer toutes les notifications toast actives
                  // pour éviter qu'elles restent affichées après avoir ouvert le menu
                  dismiss();
                }, 300);
              }
            })
            .catch(error => {
              console.error('Erreur lors du rafraîchissement des pings:', error);
            })
            .finally(() => {
              setLoading(false); // Masquer l'indicateur de chargement
            });
        }
      }}>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="sm"
            className="relative border-gray-200 hover:border-blue-300 hover:bg-blue-50 transition-colors"
          >
            <MessageSquare size={18} className="text-gray-600" />
            {unreadCount > 0 && (
              <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs w-5 h-5 flex items-center justify-center p-0 rounded-full shadow-sm">
                {unreadCount}
              </Badge>
            )}
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-80 p-0 shadow-lg border-gray-200 rounded-lg overflow-hidden">
          <div className="px-3 py-2 text-sm font-medium border-b flex justify-between items-center">
            <span>Messages</span>
            {pings.filter(p => !p.read).length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="text-xs h-6 px-2 py-0 hover:bg-gray-100"
              >
                Tout marquer comme lu
              </Button>
            )}
          </div>
          <div className="max-h-96 overflow-y-auto scrollbar-hide">
            {loading ? (
              <div className="px-4 py-6 text-center text-sm text-gray-500">
                <div className="flex flex-col justify-center items-center space-y-2">
                  <div className="animate-spin h-6 w-6 border-3 border-blue-500 rounded-full border-t-transparent"></div>
                  <span>Chargement des messages...</span>
                </div>
              </div>
            ) : pings.length === 0 ? (
              <div className="px-4 py-8 text-center">
                <div className="flex flex-col items-center space-y-2">
                  <MessageSquare size={32} className="text-gray-300" />
                  <p className="text-sm text-gray-500">Aucun message</p>
                </div>
              </div>
            ) : (
              pings.map((ping) => (
                <DropdownMenuItem
                  key={ping.id}
                  className={`px-3 py-2 cursor-pointer ${!ping.read ? 'bg-blue-50' : ''}`}
                  onClick={() => handlePingClick(ping)}
                >
                  <div className="flex items-start gap-3">
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">
                        {ping.from_user?.name?.charAt(0) || '?'}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="text-sm font-medium">
                        {ping.from_user?.name || 'Utilisateur inconnu'}
                      </p>
                      <p className="text-xs text-gray-700">
                        {ping.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-1">
                        {formatDistance(new Date(ping.created_at), new Date(), {
                          addSuffix: true,
                          locale: fr
                        })}
                      </p>
                    </div>
                  </div>
                </DropdownMenuItem>
              ))
            )}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
