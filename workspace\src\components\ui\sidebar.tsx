
import React from 'react';
import { Logo } from "@/components/Logo";
import { cn } from "@/lib/utils";

// Inside the SidebarHeader component, add the Logo
const SidebarHeader = React.forwardRef<
  HTMLDivElement,
  React.ComponentProps<"div">
>(({ className, ...props }, ref) => {
  return (
    <div
      ref={ref}
      data-sidebar="header"
      className={cn("flex flex-col gap-2 p-2 items-center", className)}
      {...props}
    >
      <Logo size="sm" className="mb-2 opacity-80" />
    </div>
  )
});

SidebarHeader.displayName = "SidebarHeader";

export { SidebarHeader };
