
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useProfileContext } from '@/contexts/ProfileContext';
import { Loader2, User, Trash2 } from 'lucide-react';
import { DeleteProfileDialog } from './DeleteProfileDialog';

export const ProfileSelect = () => {
  const navigate = useNavigate();
  const { profiles, loading, selectProfile, currentProfile } = useProfileContext();

  if (loading) {
    return <Loader2 className="h-4 w-4 animate-spin" />;
  }

  // Toujours afficher le bouton de création de profil, même si des profils existent
  if (!profiles?.length) {
    return (
      <Button
        variant="default"
        size="sm"
        onClick={() => navigate('/profile/new')}
        className="gap-2 bg-blue-600 hover:bg-blue-700 text-white"
      >
        <User size={16} />
        Créer un profil
      </Button>
    );
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant={currentProfile ? "outline" : "default"}
          size="sm"
          className={`gap-2 ${!currentProfile ? "bg-blue-600 hover:bg-blue-700 text-white" : ""}`}
        >
          {currentProfile ? (
            <>
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs bg-blue-100 text-blue-600">
                  {currentProfile.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <span>{currentProfile.name}</span>
            </>
          ) : (
            <>
              <User size={16} />
              <span className="font-medium">Sélectionner un profil</span>
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {profiles.map(profile => (
          <div key={profile.id} className="flex items-center justify-between px-2 py-2 hover:bg-gray-100 rounded-sm w-full group relative">
            <div
              className="flex items-center gap-2 cursor-pointer w-full"
              onClick={() => selectProfile(profile)}
            >
              <Avatar className="h-6 w-6">
                <AvatarFallback className="text-xs bg-blue-100 text-blue-600">
                  {profile.name.charAt(0)}
                </AvatarFallback>
              </Avatar>
              <span>{profile.name}</span>
            </div>
            <div className="absolute right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <DeleteProfileDialog profile={profile} />
            </div>
          </div>
        ))}
        <DropdownMenuItem
          onClick={() => navigate('/profile/new')}
          className="gap-2"
        >
          <User size={16} />
          Créer un nouveau profil
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
