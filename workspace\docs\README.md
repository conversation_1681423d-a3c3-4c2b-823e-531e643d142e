# Documents RGPD pour WeMa Tracker

Ce dossier contient les documents légaux nécessaires pour la conformité RGPD de l'application WeMa Tracker.

## Contenu

1. **Politique de Confidentialité** - Explique comment l'application collecte, utilise et protège les données des utilisateurs
   - [Version Markdown](politique-de-confidentialite.md)
   - [Version HTML](politique-de-confidentialite.html)

2. **Conditions d'Utilisation** - Définit les règles d'utilisation de l'application
   - [Version Markdown](conditions-utilisation.md)
   - [Version HTML](conditions-utilisation.html)

3. **Page d'accueil** - Point d'entrée pour accéder aux documents légaux
   - [index.html](index.html)

## Comment utiliser ces documents

### Option 1 : Intégration dans l'installateur

Vous pouvez intégrer ces documents dans le processus d'installation en modifiant le fichier `src-tauri/nsis/installer.nsi` pour afficher ces documents et demander l'acceptation des conditions avant l'installation.

### Option 2 : Intégration dans l'application

Vous pouvez ajouter une section "Mentions légales" dans l'application qui affiche ces documents. Pour cela :

1. Copiez les fichiers HTML dans le dossier `public` de votre projet
2. Ajoutez un lien vers ces documents dans l'interface utilisateur
3. Utilisez la fonction `window.open()` pour ouvrir ces documents dans le navigateur par défaut

### Option 3 : Hébergement en ligne

Vous pouvez héberger ces documents sur un site web et fournir des liens vers eux dans l'application.

## Personnalisation

Avant d'utiliser ces documents, assurez-vous de :

1. Remplacer l'adresse email de contact par votre adresse réelle
2. Mettre à jour la date d'entrée en vigueur
3. Personnaliser le contenu selon les spécificités de votre application
4. Remplacer le logo placeholder par le logo WeMa réel

## Note importante

Ces documents sont fournis à titre d'exemple et ne constituent pas un avis juridique. Il est recommandé de les faire examiner par un professionnel du droit avant de les utiliser dans un contexte commercial.
