/**
 * Script de réparation manuelle des données corrompues
 * À exécuter en cas de problèmes de déchiffrement
 */

import { createClient } from '@supabase/supabase-js';
import crypto from 'crypto';

// Configuration Supabase (à adapter selon votre environnement)
const SUPABASE_URL = process.env.VITE_SUPABASE_URL || 'your_supabase_url';
const SUPABASE_API_KEY = process.env.VITE_SUPABASE_API_KEY || 'your_supabase_api_key';

const supabase = createClient(SUPABASE_URL, SUPABASE_API_KEY);

// Fonctions de chiffrement (adaptées du code TypeScript)
function getEncryptionKey() {
  const dedicatedKey = process.env.VITE_ENCRYPTION_KEY;
  if (dedicatedKey && dedicatedKey.length >= 32) {
    return dedicatedKey;
  }
  
  const appKey = 'WeMaTracker-RGPD-Protection-Key-v2';
  const urlHash = SUPABASE_URL.split('//')[1]?.split('.')[0] || 'default';
  const derivedKey = `${appKey}-${urlHash}-${SUPABASE_URL.length}`;
  
  return derivedKey;
}

function getLegacyEncryptionKey() {
  const appKey = 'WeMaTracker-RGPD-Protection-Key-v1';
  const urlHash = SUPABASE_URL.split('//')[1]?.split('.')[0] || 'default';
  const derivedKey = `${appKey}-${urlHash}-${SUPABASE_URL.length}`;
  
  return derivedKey;
}

function isEncrypted(text) {
  if (!text || text.length === 0) return false;
  const base64Regex = /^[A-Za-z0-9+/=]+$/;
  return text.length > 24 && base64Regex.test(text);
}

async function generateKey(passphrase) {
  const encoder = new TextEncoder();
  const passphraseData = encoder.encode(passphrase);
  const salt = encoder.encode('WeMaTrackerSalt');
  
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    passphraseData,
    { name: 'PBKDF2' },
    false,
    ['deriveKey']
  );

  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt,
      iterations: 100000,
      hash: 'SHA-256'
    },
    keyMaterial,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt']
  );
}

async function decryptText(encryptedText, passphrase) {
  if (!encryptedText) return '';
  
  const key = await generateKey(passphrase);
  const encryptedData = Uint8Array.from(atob(encryptedText), c => c.charCodeAt(0));
  const iv = encryptedData.slice(0, 12);
  const ciphertext = encryptedData.slice(12);

  const decryptedData = await crypto.subtle.decrypt(
    { name: 'AES-GCM', iv },
    key,
    ciphertext
  );

  const decoder = new TextDecoder();
  return decoder.decode(decryptedData);
}

async function encryptText(text, passphrase) {
  if (!text) return '';
  
  const key = await generateKey(passphrase);
  const encoder = new TextEncoder();
  const data = encoder.encode(text);
  const iv = crypto.getRandomValues(new Uint8Array(12));

  const encryptedData = await crypto.subtle.encrypt(
    { name: 'AES-GCM', iv },
    key,
    data
  );

  const result = new Uint8Array(iv.length + encryptedData.byteLength);
  result.set(iv);
  result.set(new Uint8Array(encryptedData), iv.length);

  return btoa(String.fromCharCode(...result));
}

// Fonction principale de réparation
async function repairCorruptedData() {
  console.log('🚀 Début de la réparation des données corrompues...');
  
  const currentKey = getEncryptionKey();
  const legacyKey = getLegacyEncryptionKey();
  
  let totalRepaired = 0;
  let totalErrors = 0;

  // Réparer les clients
  console.log('🔧 Réparation des clients...');
  const { data: clients, error: clientsError } = await supabase
    .from('clients')
    .select('id, name');

  if (clientsError) {
    console.error('❌ Erreur lors de la récupération des clients:', clientsError);
    return;
  }

  for (const client of clients || []) {
    try {
      if (!isEncrypted(client.name)) continue;

      // Essayer avec la clé actuelle
      try {
        await decryptText(client.name, currentKey);
        console.log(`✅ Client ${client.id}: OK`);
        continue;
      } catch (error) {
        // Échec avec clé actuelle
      }

      // Essayer avec l'ancienne clé
      try {
        const decryptedName = await decryptText(client.name, legacyKey);
        const newEncryptedName = await encryptText(decryptedName, currentKey);
        
        const { error } = await supabase
          .from('clients')
          .update({ name: newEncryptedName })
          .eq('id', client.id);

        if (error) throw error;
        
        console.log(`🔄 Client ${client.id}: migré`);
        totalRepaired++;
        continue;
      } catch (error) {
        // Échec avec ancienne clé aussi
      }

      // Remplacer par nom par défaut
      const defaultName = `Client ${client.id.substring(0, 8)}`;
      const encryptedDefaultName = await encryptText(defaultName, currentKey);
      
      const { error } = await supabase
        .from('clients')
        .update({ name: encryptedDefaultName })
        .eq('id', client.id);

      if (error) {
        console.error(`❌ Erreur réparation client ${client.id}:`, error);
        totalErrors++;
      } else {
        console.log(`🔧 Client ${client.id}: réparé avec nom par défaut`);
        totalRepaired++;
      }

    } catch (error) {
      console.error(`❌ Erreur traitement client ${client.id}:`, error);
      totalErrors++;
    }
  }

  // Réparer les étapes
  console.log('🔧 Réparation des étapes...');
  const { data: steps, error: stepsError } = await supabase
    .from('steps')
    .select('id, name');

  if (stepsError) {
    console.error('❌ Erreur lors de la récupération des étapes:', stepsError);
    return;
  }

  for (const step of steps || []) {
    try {
      if (!isEncrypted(step.name)) continue;

      // Essayer avec la clé actuelle
      try {
        await decryptText(step.name, currentKey);
        console.log(`✅ Étape ${step.id}: OK`);
        continue;
      } catch (error) {
        // Échec avec clé actuelle
      }

      // Essayer avec l'ancienne clé
      try {
        const decryptedName = await decryptText(step.name, legacyKey);
        const newEncryptedName = await encryptText(decryptedName, currentKey);
        
        const { error } = await supabase
          .from('steps')
          .update({ name: newEncryptedName })
          .eq('id', step.id);

        if (error) throw error;
        
        console.log(`🔄 Étape ${step.id}: migrée`);
        totalRepaired++;
        continue;
      } catch (error) {
        // Échec avec ancienne clé aussi
      }

      // Remplacer par nom par défaut
      const defaultName = `Étape ${step.id.substring(0, 8)}`;
      const encryptedDefaultName = await encryptText(defaultName, currentKey);
      
      const { error } = await supabase
        .from('steps')
        .update({ name: encryptedDefaultName })
        .eq('id', step.id);

      if (error) {
        console.error(`❌ Erreur réparation étape ${step.id}:`, error);
        totalErrors++;
      } else {
        console.log(`🔧 Étape ${step.id}: réparée avec nom par défaut`);
        totalRepaired++;
      }

    } catch (error) {
      console.error(`❌ Erreur traitement étape ${step.id}:`, error);
      totalErrors++;
    }
  }

  console.log(`🎯 Réparation terminée: ${totalRepaired} éléments réparés, ${totalErrors} erreurs`);
}

// Exécuter le script
if (import.meta.url === `file://${process.argv[1]}`) {
  repairCorruptedData().catch(console.error);
}

export { repairCorruptedData };
