/* Thème WeMa - Mode clair */
:root {
  /* Couleurs principales de WeMa */
  --wema-primary: 210 100% 35%; /* Bleu <PERSON>a */
  --wema-secondary: 340 82% 52%; /* <PERSON> */
  --wema-accent: 25 95% 53%; /* Orange WeMa */
  --wema-background: 0 0% 100%;
  --wema-foreground: 222.2 84% 4.9%;

  /* Variables de base */
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 210 100% 35%; /* Bleu WeMa */
  --primary-foreground: 210 40% 98%;
  --secondary: 340 82% 52%; /* Rose <PERSON> */
  --secondary-foreground: 210 40% 98%;
  --muted: 210 40% 96.1%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 25 95% 53%; /* Orange WeMa */
  --accent-foreground: 222.2 47.4% 11.2%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 210 100% 35%; /* Bleu WeMa */
  --radius: 0.5rem;

  /* Sidebar */
  --sidebar-background: 210 100% 35%; /* Bleu WeMa */
  --sidebar-foreground: 0 0% 100%;
  --sidebar-primary: 0 0% 100%;
  --sidebar-primary-foreground: 210 100% 35%; /* Bleu WeMa */
  --sidebar-accent: 340 82% 52%; /* Rose WeMa */
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: 210 100% 40%;
  --sidebar-ring: 0 0% 100%;
}

/* Thème WeMa - Mode sombre */
.dark {
  /* Couleurs principales de WeMa en mode sombre */
  --wema-primary: 210 100% 60%; /* Bleu WeMa plus lumineux */
  --wema-secondary: 340 82% 65%; /* Rose WeMa plus lumineux */
  --wema-accent: 25 95% 65%; /* Orange WeMa plus lumineux */
  --wema-background: 220 40% 13%; /* Fond sombre moins agressif */
  --wema-foreground: 210 40% 98%;

  /* Variables de base */
  --background: 220 40% 13%; /* Fond sombre moins agressif */
  --foreground: 210 40% 98%;
  --card: 220 45% 16%; /* Cartes légèrement plus claires que le fond */
  --card-foreground: 210 40% 98%;
  --popover: 220 45% 16%;
  --popover-foreground: 210 40% 98%;
  --primary: 210 100% 60%; /* Bleu WeMa plus lumineux */
  --primary-foreground: 0 0% 100%;
  --secondary: 340 82% 65%; /* Rose WeMa plus lumineux */
  --secondary-foreground: 0 0% 100%;
  --muted: 220 40% 20%; /* Éléments atténués */
  --muted-foreground: 210 40% 80%;
  --accent: 25 95% 65%; /* Orange WeMa plus lumineux */
  --accent-foreground: 0 0% 100%;
  --destructive: 0 70% 50%; /* Rouge plus visible */
  --destructive-foreground: 210 40% 98%;
  --border: 220 40% 20%; /* Bordures plus visibles */
  --input: 220 40% 20%;
  --ring: 210 100% 60%; /* Bleu WeMa plus lumineux */

  /* Sidebar */
  --sidebar-background: 220 45% 10%; /* Sidebar légèrement plus foncée */
  --sidebar-foreground: 210 40% 98%;
  --sidebar-primary: 210 100% 60%; /* Bleu WeMa plus lumineux */
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 340 82% 65%; /* Rose WeMa plus lumineux */
  --sidebar-accent-foreground: 0 0% 100%;
  --sidebar-border: 220 40% 20%;
  --sidebar-ring: 210 100% 60%; /* Bleu WeMa plus lumineux */
}

/* Classes utilitaires pour les couleurs WeMa */
.bg-wema-primary {
  background-color: hsl(var(--wema-primary));
}

.bg-wema-secondary {
  background-color: hsl(var(--wema-secondary));
}

.bg-wema-accent {
  background-color: hsl(var(--wema-accent));
}

.text-wema-primary {
  color: hsl(var(--wema-primary));
}

.text-wema-secondary {
  color: hsl(var(--wema-secondary));
}

.text-wema-accent {
  color: hsl(var(--wema-accent));
}

.border-wema-primary {
  border-color: hsl(var(--wema-primary));
}

.border-wema-secondary {
  border-color: hsl(var(--wema-secondary));
}

.border-wema-accent {
  border-color: hsl(var(--wema-accent));
}

/* Améliorations pour le mode sombre */
.dark .card,
.dark .responsive-card {
  background-color: hsl(220 45% 16%);
  border-color: hsl(220 40% 20%);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
}

.dark .responsive-table-container {
  background-color: hsl(220 45% 16%);
  border-color: hsl(220 40% 20%);
}

.dark .responsive-table th {
  background-color: hsl(220 40% 18%);
  color: hsl(210 40% 90%);
}

.dark .responsive-table tr:hover {
  background-color: hsl(220 40% 20%);
}

.dark .responsive-table td {
  border-color: hsl(220 40% 20%);
}

.dark input,
.dark textarea,
.dark select {
  background-color: hsl(220 40% 15%);
  border-color: hsl(220 40% 25%);
  color: hsl(210 40% 98%);
}

.dark input:focus,
.dark textarea:focus,
.dark select:focus {
  border-color: hsl(var(--wema-primary));
  box-shadow: 0 0 0 2px hsla(var(--wema-primary), 0.2);
}

.dark .bg-pink-50\/70 {
  background-color: hsla(340, 82%, 20%, 0.7);
}

.dark .bg-pink-50\/70:hover {
  background-color: hsla(340, 82%, 25%, 0.9);
}

.dark .bg-pink-100 {
  background-color: hsla(340, 82%, 25%, 1);
}

.dark .text-pink-600 {
  color: hsl(340, 82%, 70%);
}

/* Styles des boutons en mode sombre */
.dark .bg-blue-600 {
  background-color: hsl(210, 100%, 50%);
}

.dark .bg-blue-600:hover {
  background-color: hsl(210, 100%, 55%);
}

.dark .hover\:bg-blue-50:hover {
  background-color: hsla(210, 100%, 30%, 0.3);
}

.dark .hover\:bg-blue-700:hover {
  background-color: hsl(210, 100%, 55%);
}

.dark .text-blue-600 {
  color: hsl(210, 100%, 70%);
}

.dark .border-blue-300 {
  border-color: hsl(210, 100%, 40%);
}

.dark .border-blue-500 {
  border-color: hsl(210, 100%, 50%);
}

/* Styles des éléments de navigation en mode sombre */
.dark .bg-gray-50\/70 {
  background-color: hsla(220, 40%, 20%, 0.7);
}

.dark .bg-gray-50\/80 {
  background-color: hsla(220, 40%, 20%, 0.8);
}

.dark .hover\:bg-gray-50\/80:hover {
  background-color: hsla(220, 40%, 25%, 0.8);
}

/* Styles des avatars en mode sombre */
.dark .bg-blue-100 {
  background-color: hsl(210, 100%, 25%);
}

.dark .border-white {
  border-color: hsl(220, 40%, 20%);
}

/* Styles spécifiques pour les statuts */
.status-manquant {
  background-color: #ea384c;
  color: white;
}

.status-transmis {
  background-color: #F97316;
  color: white;
}

.status-validation {
  background-color: #0EA5E9;
  color: white;
}

.status-valide {
  background-color: #4ADE80;
  color: white;
}

.status-refuse {
  background-color: #222222;
  color: white;
}

/* Styles des statuts en mode sombre - plus lumineux pour meilleure visibilité */
.dark .status-manquant {
  background-color: #f04c5f;
}

.dark .status-transmis {
  background-color: #fb8c42;
}

.dark .status-validation {
  background-color: #38bdf8;
}

.dark .status-valide {
  background-color: #6ee7a0;
}

.dark .status-refuse {
  background-color: #333333;
}
