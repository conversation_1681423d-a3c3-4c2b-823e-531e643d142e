-- Migration pour créer la table des totaux sous gestion pour l'évolution temporelle
-- Cette table stocke les totaux patrimoine à des dates données pour créer une courbe d'évolution simple

-- 1. Créer la table patrimoine_evolution
CREATE TABLE IF NOT EXISTS public.patrimoine_evolution (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  date_snapshot DATE NOT NULL,
  total_sous_gestion TEXT NOT NULL, -- Chiffré avec AES-256-GCM
  devise TEXT NOT NULL DEFAULT 'EUR',
  commentaire TEXT, -- Option<PERSON>, pour noter le contexte
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  
  -- Contrainte d'unicité sur la date pour éviter les doublons
  CONSTRAINT unique_date_snapshot UNIQUE (date_snapshot)
);

-- 2. Créer les index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_patrimoine_evolution_date ON public.patrimoine_evolution(date_snapshot);
CREATE INDEX IF NOT EXISTS idx_patrimoine_evolution_created_at ON public.patrimoine_evolution(created_at);

-- 3. Activer RLS sur la table
ALTER TABLE public.patrimoine_evolution ENABLE ROW LEVEL SECURITY;

-- 4. Créer la politique RLS
DROP POLICY IF EXISTS "Authenticated users can access patrimoine evolution" ON public.patrimoine_evolution;
CREATE POLICY "Authenticated users can access patrimoine evolution" ON public.patrimoine_evolution
  FOR ALL 
  USING (auth.uid() IS NOT NULL);

-- 5. Créer la fonction de mise à jour automatique du timestamp
CREATE OR REPLACE FUNCTION update_patrimoine_evolution_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 6. Créer le trigger pour la mise à jour automatique
DROP TRIGGER IF EXISTS trigger_update_patrimoine_evolution_updated_at ON public.patrimoine_evolution;
CREATE TRIGGER trigger_update_patrimoine_evolution_updated_at
  BEFORE UPDATE ON public.patrimoine_evolution
  FOR EACH ROW
  EXECUTE FUNCTION update_patrimoine_evolution_updated_at();

-- 7. Activer les souscriptions temps réel
ALTER PUBLICATION supabase_realtime ADD TABLE public.patrimoine_evolution;

-- 8. Insérer un point de départ avec la date d'aujourd'hui et un total de 0
-- (sera mis à jour manuellement par l'utilisateur)
INSERT INTO public.patrimoine_evolution (date_snapshot, total_sous_gestion, commentaire) 
VALUES (
  CURRENT_DATE, 
  '0', -- Sera chiffré côté client
  'Point de départ - À mettre à jour avec le total réel'
) ON CONFLICT (date_snapshot) DO NOTHING;

-- 9. Journaliser la création de la table
INSERT INTO public.data_access_logs (
  operation,
  table_name,
  details,
  timestamp
) VALUES (
  'create',
  'patrimoine_evolution',
  'Création de la table pour l''évolution temporelle simplifiée du patrimoine',
  NOW()
);

-- 10. Commentaires sur la table
COMMENT ON TABLE public.patrimoine_evolution IS 'Table des totaux sous gestion à des dates données pour créer l''évolution temporelle du patrimoine';
COMMENT ON COLUMN public.patrimoine_evolution.date_snapshot IS 'Date du snapshot (unique)';
COMMENT ON COLUMN public.patrimoine_evolution.total_sous_gestion IS 'Total sous gestion chiffré avec AES-256-GCM (format: enc:base64data)';
COMMENT ON COLUMN public.patrimoine_evolution.commentaire IS 'Commentaire optionnel pour le contexte du snapshot';
