-- Migration pour corriger les problèmes de Function Search Path Mutable
-- RÉSOUT : Avertissement sécurité Supabase "Function Search Path Mutable"
-- Date : 17 janvier 2025

-- ============================================================================
-- EXPLICATION DU PROBLÈME
-- ============================================================================
-- Quand une fonction PostgreSQL a search_path non défini, elle peut être
-- vulnérable à des attaques par manipulation du search_path.
-- La solution consiste à définir explicitement search_path = '' pour les 
-- fonctions SECURITY DEFINER.

-- ============================================================================
-- CORRECTION DES FONCTIONS EXISTANTES
-- ============================================================================

-- 1. Fonction : purge_old_data_access_logs
-- Cette fonction purge les logs d'accès anciens
DROP FUNCTION IF EXISTS public.purge_old_data_access_logs();

CREATE OR REPLACE FUNCTION public.purge_old_data_access_logs()
RETURNS void 
LANGUAGE plpgsql 
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  DELETE FROM public.data_access_logs
  WHERE timestamp < NOW() - INTERVAL '6 months';
END;
$$;

-- Commentaire sur la fonction corrigée
COMMENT ON FUNCTION public.purge_old_data_access_logs IS 'Purge les logs d''accès aux données de plus de 6 mois pour conformité RGPD - search_path sécurisé';

-- 2. Fonction : purge_old_notifications  
-- Cette fonction purge les anciennes notifications
DROP FUNCTION IF EXISTS public.purge_old_notifications();

CREATE OR REPLACE FUNCTION public.purge_old_notifications()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  DELETE FROM public.notifications
  WHERE created_at < NOW() - INTERVAL '1 year';
END;
$$;

-- Commentaire sur la fonction corrigée
COMMENT ON FUNCTION public.purge_old_notifications IS 'Purge les notifications de plus de 1 an pour conformité RGPD - search_path sécurisé';

-- 3. Fonction : purge_old_pings
-- Cette fonction purge les anciens messages directs (pings)
DROP FUNCTION IF EXISTS public.purge_old_pings();

CREATE OR REPLACE FUNCTION public.purge_old_pings()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  DELETE FROM public.pings
  WHERE created_at < NOW() - INTERVAL '1 year';
END;
$$;

-- Commentaire sur la fonction corrigée
COMMENT ON FUNCTION public.purge_old_pings IS 'Purge les messages directs (pings) de plus de 1 an pour conformité RGPD - search_path sécurisé';

-- 4. Fonction : anonymize_inactive_clients
-- Cette fonction anonymise les clients inactifs
DROP FUNCTION IF EXISTS public.anonymize_inactive_clients();

CREATE OR REPLACE FUNCTION public.anonymize_inactive_clients()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  inactive_client_id UUID;
  anonymized_name TEXT;
BEGIN
  -- Trouver les clients sans activité depuis 3 ans
  FOR inactive_client_id IN
    SELECT c.id
    FROM public.clients c
    LEFT JOIN public.steps s ON s.client_id = c.id
    WHERE (
      -- Aucune mise à jour du client depuis 3 ans
      c.created_at < NOW() - INTERVAL '3 years'
      AND NOT EXISTS (
        -- Aucune étape mise à jour depuis 3 ans
        SELECT 1 FROM public.steps
        WHERE client_id = c.id
        AND (
          created_at > NOW() - INTERVAL '3 years'
          OR received_date > NOW() - INTERVAL '3 years'
        )
      )
    )
    GROUP BY c.id
  LOOP
    -- Générer un nom anonymisé
    anonymized_name := 'Client anonymisé ' || TO_CHAR(NOW(), 'YYYY-MM-DD');
    
    -- Anonymiser le client
    UPDATE public.clients
    SET name = anonymized_name
    WHERE id = inactive_client_id;
    
    -- Anonymiser les commentaires des étapes
    UPDATE public.steps
    SET comment = 'Commentaire anonymisé'
    WHERE client_id = inactive_client_id
    AND comment IS NOT NULL
    AND comment != '';
    
    -- Journaliser l'anonymisation
    INSERT INTO public.data_access_logs (
      operation,
      table_name,
      record_id,
      details,
      timestamp
    ) VALUES (
      'anonymize',
      'clients',
      inactive_client_id,
      'Anonymisation automatique pour conformité RGPD',
      NOW()
    );
  END LOOP;
END;
$$;

-- Commentaire sur la fonction corrigée
COMMENT ON FUNCTION public.anonymize_inactive_clients IS 'Anonymise les clients inactifs depuis plus de 3 ans pour conformité RGPD - search_path sécurisé';

-- 5. Fonction : update_updated_at_column (si elle existe)
-- Cette fonction met à jour automatiquement la colonne updated_at
DROP FUNCTION IF EXISTS public.update_updated_at_column();

CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- Commentaire sur la fonction corrigée
COMMENT ON FUNCTION public.update_updated_at_column IS 'Trigger pour mettre à jour automatiquement updated_at - search_path sécurisé';

-- ============================================================================
-- NOUVELLE FONCTION UTILITAIRE SÉCURISÉE
-- ============================================================================

-- Fonction pour purger toutes les données expirées en une fois
CREATE OR REPLACE FUNCTION public.purge_all_expired_data()
RETURNS TABLE(
  notifications_purged BIGINT,
  pings_purged BIGINT,
  logs_purged BIGINT,
  clients_anonymized BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  notifications_count BIGINT := 0;
  pings_count BIGINT := 0;
  logs_count BIGINT := 0;
  clients_count BIGINT := 0;
BEGIN
  -- Compter et purger les notifications anciennes
  SELECT COUNT(*) INTO notifications_count
  FROM public.notifications
  WHERE created_at < NOW() - INTERVAL '1 year';
  
  PERFORM public.purge_old_notifications();
  
  -- Compter et purger les pings anciens
  SELECT COUNT(*) INTO pings_count
  FROM public.pings
  WHERE created_at < NOW() - INTERVAL '1 year';
  
  PERFORM public.purge_old_pings();
  
  -- Compter et purger les logs anciens
  SELECT COUNT(*) INTO logs_count
  FROM public.data_access_logs
  WHERE timestamp < NOW() - INTERVAL '6 months';
  
  PERFORM public.purge_old_data_access_logs();
  
  -- Compter les clients à anonymiser
  SELECT COUNT(DISTINCT c.id) INTO clients_count
  FROM public.clients c
  LEFT JOIN public.steps s ON s.client_id = c.id
  WHERE (
    c.created_at < NOW() - INTERVAL '3 years'
    AND NOT EXISTS (
      SELECT 1 FROM public.steps
      WHERE client_id = c.id
      AND (
        created_at > NOW() - INTERVAL '3 years'
        OR received_date > NOW() - INTERVAL '3 years'
      )
    )
  );
  
  -- Anonymiser les clients inactifs
  PERFORM public.anonymize_inactive_clients();
  
  -- Retourner les statistiques
  RETURN QUERY SELECT notifications_count, pings_count, logs_count, clients_count;
END;
$$;

-- Commentaire sur la nouvelle fonction
COMMENT ON FUNCTION public.purge_all_expired_data IS 'Purge toutes les données expirées selon RGPD et retourne les statistiques - search_path sécurisé';

-- ============================================================================
-- VÉRIFICATION DES CORRECTIONS
-- ============================================================================

-- Vérifier que toutes les fonctions ont maintenant un search_path défini
DO $$
DECLARE
  func_record RECORD;
  vulnerable_functions TEXT[] := '{}';
BEGIN
  FOR func_record IN 
    SELECT 
      n.nspname AS schema_name,
      p.proname AS function_name,
      p.prosecdef AS is_security_definer,
      pg_get_function_identity_arguments(p.oid) AS args,
      p.proconfig
    FROM pg_proc p
    LEFT JOIN pg_namespace n ON p.pronamespace = n.oid
    WHERE n.nspname = 'public'
    AND p.proname IN ('purge_old_data_access_logs', 'purge_old_notifications', 
                      'purge_old_pings', 'anonymize_inactive_clients',
                      'update_updated_at_column', 'purge_all_expired_data')
  LOOP
    IF func_record.is_security_definer AND 
       (func_record.proconfig IS NULL OR 
        NOT EXISTS(SELECT 1 FROM unnest(func_record.proconfig) AS config 
                   WHERE config LIKE 'search_path=%')) THEN
      vulnerable_functions := vulnerable_functions || 
        (func_record.schema_name || '.' || func_record.function_name);
    END IF;
  END LOOP;

  IF array_length(vulnerable_functions, 1) > 0 THEN
    RAISE WARNING 'Fonctions encore vulnérables (search_path manquant): %', 
      array_to_string(vulnerable_functions, ', ');
  ELSE
    RAISE NOTICE '✅ Toutes les fonctions ont un search_path sécurisé';
  END IF;
END $$; 