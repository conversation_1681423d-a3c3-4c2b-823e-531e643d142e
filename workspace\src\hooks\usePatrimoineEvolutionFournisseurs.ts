import { useState, useCallback, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { encryptText, safeDecrypt, isEncrypted } from '@/utils/encryption';
import { useSupabaseSubscription, onInsert, onUpdate, onDelete } from './useSupabaseSubscription';

interface FournisseurEvolution {
  id: string;
  date: string;
  fournisseurId: string;
  montant: number;
  devise: string;
  commentaire?: string;
}

interface EvolutionByDate {
  date: string;
  fournisseurs: Record<string, number>; // fournisseurId -> montant
  total: number;
}

/**
 * Hook pour gérer l'évolution temporelle du patrimoine par fournisseur
 */
export const usePatrimoineEvolutionFournisseurs = () => {
  const [data, setData] = useState<FournisseurEvolution[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Charger toutes les évolutions par fournisseur
  const loadEvolutions = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const { data: evolutionsData, error } = await supabase
        .from('patrimoine_evolution_fournisseurs')
        .select('*')
        .order('date_snapshot', { ascending: true });

      if (error) throw error;

      // Déchiffrer les montants
      const decryptedEvolutions = await Promise.all(
        (evolutionsData || []).map(async (evolution) => {
          let montantDecrypted = 0;

          if (evolution.montant_chiffre) {
            if (isEncrypted(evolution.montant_chiffre)) {
              const decrypted = await safeDecrypt(evolution.montant_chiffre);
              montantDecrypted = parseFloat(decrypted) || 0;
            } else {
              montantDecrypted = parseFloat(evolution.montant_chiffre) || 0;
            }
          }

          return {
            id: evolution.id,
            date: evolution.date_snapshot,
            fournisseurId: evolution.fournisseur_id,
            montant: montantDecrypted,
            devise: evolution.devise || 'EUR',
            commentaire: evolution.commentaire || undefined
          };
        })
      );

      setData(decryptedEvolutions);

    } catch (error) {
      console.error('Erreur lors du chargement des évolutions par fournisseur:', error);
      setError(error instanceof Error ? error.message : 'Erreur inconnue');
    } finally {
      setLoading(false);
    }
  }, []);

  // Sauvegarder un montant pour un fournisseur à une date donnée
  const upsertEvolution = useCallback(async (
    date: string,
    fournisseurId: string,
    montant: number,
    commentaire?: string,
    devise: string = 'EUR'
  ) => {
    try {
      // Chiffrer le montant
      const encryptedMontant = await encryptText(montant.toString());

      const { data: evolution, error } = await supabase
        .from('patrimoine_evolution_fournisseurs')
        .upsert({
          date_snapshot: date,
          fournisseur_id: fournisseurId,
          montant_chiffre: encryptedMontant,
          devise,
          commentaire: commentaire || null
        }, {
          onConflict: 'date_snapshot,fournisseur_id'
        })
        .select()
        .single();

      if (error) throw error;

      // Mettre à jour localement
      const newEvolution: FournisseurEvolution = {
        id: evolution.id,
        date: evolution.date_snapshot,
        fournisseurId: evolution.fournisseur_id,
        montant: montant,
        devise: evolution.devise,
        commentaire: evolution.commentaire || undefined
      };

      setData(prev => {
        const existingIndex = prev.findIndex(e => e.date === date && e.fournisseurId === fournisseurId);
        if (existingIndex >= 0) {
          // Mise à jour
          const updatedEvolutions = [...prev];
          updatedEvolutions[existingIndex] = newEvolution;
          return updatedEvolutions.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        } else {
          // Création
          return [...prev, newEvolution].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
        }
      });

      return true;

    } catch (error) {
      console.error('Erreur lors de la sauvegarde de l\'évolution:', error);
      toast.error('Erreur lors de la sauvegarde');
      return false;
    }
  }, []);

  // Supprimer une évolution
  const deleteEvolution = useCallback(async (id: string) => {
    try {
      const { error } = await supabase
        .from('patrimoine_evolution_fournisseurs')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setData(prev => prev.filter(e => e.id !== id));
      return true;

    } catch (error) {
      console.error('Erreur lors de la suppression de l\'évolution:', error);
      toast.error('Erreur lors de la suppression');
      return false;
    }
  }, []);

  // Obtenir les évolutions groupées par date
  const getEvolutionsByDate = useCallback((): EvolutionByDate[] => {
    const groupedByDate = data.reduce((acc, evolution) => {
      if (!acc[evolution.date]) {
        acc[evolution.date] = {
          date: evolution.date,
          fournisseurs: {},
          total: 0
        };
      }
      acc[evolution.date].fournisseurs[evolution.fournisseurId] = evolution.montant;
      return acc;
    }, {} as Record<string, EvolutionByDate>);

    // Calculer les totaux
    Object.values(groupedByDate).forEach(dateData => {
      dateData.total = Object.values(dateData.fournisseurs).reduce((sum, montant) => sum + montant, 0);
    });

    return Object.values(groupedByDate).sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }, [data]);

  // Obtenir les données pour un graphique multi-courbes
  const getChartData = useCallback((fournisseurIds: string[]) => {
    const evolutionsByDate = getEvolutionsByDate();
    
    return evolutionsByDate.map(dateData => ({
      date: dateData.date,
      ...fournisseurIds.reduce((acc, fournisseurId) => {
        acc[fournisseurId] = dateData.fournisseurs[fournisseurId] || 0;
        return acc;
      }, {} as Record<string, number>),
      total: dateData.total
    }));
  }, [getEvolutionsByDate]);

  // Formater les montants
  const formatMontant = useCallback((montant: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(montant);
  }, []);

  // Charger les données au montage
  useEffect(() => {
    loadEvolutions();
  }, [loadEvolutions]);

  // Temps réel : recharger sur changements
  const handleRealtimeUpdate = useCallback(() => {
    console.log('🔄 Évolution Fournisseurs: Changement détecté, rechargement...');
    loadEvolutions();
  }, [loadEvolutions]);

  useSupabaseSubscription(
    'patrimoine-evolution-fournisseurs-realtime',
    [
      onInsert('patrimoine_evolution_fournisseurs', handleRealtimeUpdate),
      onUpdate('patrimoine_evolution_fournisseurs', handleRealtimeUpdate),
      onDelete('patrimoine_evolution_fournisseurs', handleRealtimeUpdate)
    ]
  );

  return {
    // Données
    evolutions: data,
    loading,
    error,

    // Actions
    loadEvolutions,
    upsertEvolution,
    deleteEvolution,

    // Calculs
    getEvolutionsByDate,
    getChartData,
    formatMontant,

    // État
    hasData: data.length > 0
  };
};
