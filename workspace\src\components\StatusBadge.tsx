
import { Status } from '@/types';
import { cn } from '@/lib/utils';
import { AlertCircle, Circle } from 'lucide-react';

interface StatusBadgeProps {
  status: Status;
  showLabel?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const StatusBadge = ({ status, showLabel = true, size = 'md' }: StatusBadgeProps) => {
  const statusConfig = {
    manquant: {
      color: 'text-red-500',
      bgColor: 'bg-red-100',
      label: '🔴 Manquant',
      icon: <Circle className="fill-red-500 text-red-500" />
    },
    transmis: {
      color: 'text-orange-500',
      bgColor: 'bg-orange-100',
      label: '🟠 Transmis',
      icon: <Circle className="fill-orange-500 text-orange-500" />
    },
    validation: {
      color: 'text-blue-500',
      bgColor: 'bg-blue-100',
      label: '🔵 En validation',
      icon: <Circle className="fill-blue-500 text-blue-500" />
    },
    valide: {
      color: 'text-green-500',
      bgColor: 'bg-green-100',
      label: '🟢 Validé',
      icon: <Circle className="fill-green-500 text-green-500" />
    },
    refuse: {
      color: 'text-gray-800',
      bgColor: 'bg-gray-100',
      label: '⚫ Refusé',
      icon: <Circle className="fill-gray-800 text-gray-800" />
    },
    urgent: {
      color: 'text-pink-600',
      bgColor: 'bg-pink-100',
      label: '⚡ Urgent',
      icon: <AlertCircle className="fill-pink-600 text-white" />
    }
  };

  const config = statusConfig[status];
  const sizeClasses = {
    sm: 'text-xs py-0.5 px-2',
    md: 'text-sm py-1 px-2',
    lg: 'text-base py-1.5 px-3'
  };

  if (!showLabel) {
    return (
      <div className="flex items-center justify-center">
        {config.icon}
      </div>
    );
  }

  return (
    <div 
      className={cn(
        'flex items-center gap-1.5 rounded font-medium w-fit',
        config.bgColor,
        config.color,
        sizeClasses[size]
      )}
    >
      {config.label}
    </div>
  );
};
