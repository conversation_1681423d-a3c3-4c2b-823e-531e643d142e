-- Migration pour activer les souscriptions temps réel sur les tables patrimoine
-- CRITIQUE : Cette migration est nécessaire pour que les modifications de patrimoine
-- soient propagées en temps réel entre tous les postes utilisateurs

-- 1. Activer les souscriptions temps réel sur la table 'client_patrimoine'
-- Permet de détecter les ajouts, modifications et suppressions d'entrées patrimoine
ALTER PUBLICATION supabase_realtime ADD TABLE public.client_patrimoine;

-- 2. Activer les souscriptions temps réel sur la table 'patrimoine_fournisseurs'
-- Permet de détecter les ajouts, modifications et suppressions de fournisseurs
ALTER PUBLICATION supabase_realtime ADD TABLE public.patrimoine_fournisseurs;

-- 3. Journaliser l'activation du temps réel patrimoine
INSERT INTO public.data_access_logs (
  operation,
  table_name,
  details,
  timestamp
) VALUES (
  'update',
  'system',
  'Activation des souscriptions temps réel pour les tables patrimoine (client_patrimoine, patrimoine_fournisseurs)',
  NOW()
);

-- 4. Commentaires sur la migration
COMMENT ON TABLE public.client_patrimoine IS 'Table des entrées patrimoine avec souscriptions temps réel activées pour synchronisation multi-postes';
COMMENT ON TABLE public.patrimoine_fournisseurs IS 'Table des fournisseurs patrimoine avec souscriptions temps réel activées pour synchronisation multi-postes';
