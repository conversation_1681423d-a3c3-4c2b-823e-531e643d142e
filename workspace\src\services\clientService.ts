/**
 * Service pour la gestion des clients
 * Contient les fonctions liées aux opérations sur les clients
 */

import { supabase } from "@/integrations/supabase/client";
import { Client, Step, Status } from "@/types";

import { defaultSteps } from "@/data/defaultSteps";

/**
 * Récupère tous les clients avec leurs étapes
 * @returns Liste des clients avec leurs étapes
 */
export async function getAllClients(): Promise<Client[]> {
  try {
    // Récupérer les clients
    const { data: clientsData, error: clientsError } = await supabase
      .from('clients')
      .select('*')
      .order('name', { ascending: true });

    if (clientsError) throw clientsError;

    // Récupérer les étapes
    const { data: stepsData, error: stepsError } = await supabase
      .from('steps')
      .select('*');

    if (stepsError) throw stepsError;

    // Récupérer les assignations d'étapes
    const { data: assignmentsData, error: assignmentsError } = await supabase
      .from('step_assignments')
      .select('*, assignees(*)');

    if (assignmentsError) throw assignmentsError;

    // Obtenir la clé de chiffrement
    const encryptionKey = getEncryptionKey();

    // Traiter les données client par client
    const clients = await Promise.all(clientsData.map(async client => {
      // Déchiffrer le nom du client si nécessaire
      let clientName = client.name;
      if (isEncrypted(clientName)) {
        try {
          clientName = await decryptText(clientName, encryptionKey);
        } catch (error) {
          console.error(`Erreur lors du déchiffrement du nom du client ${client.id}:`, error);
        }
      }

      // Filtrer les étapes pour ce client
      const clientSteps = stepsData.filter(step => step.client_id === client.id);

      // Traiter les étapes
      const processedSteps = await Promise.all(clientSteps.map(async step => {
        // Déchiffrer le nom de l'étape si nécessaire
        let stepName = step.name;
        if (isEncrypted(stepName)) {
          try {
            stepName = await decryptText(stepName, encryptionKey);
          } catch (error) {
            console.error(`Erreur lors du déchiffrement du nom de l'étape ${step.id}:`, error);
          }
        }

        // Déchiffrer le commentaire si nécessaire
        let comment = step.comment || '';
        if (comment && isEncrypted(comment)) {
          try {
            comment = await decryptText(comment, encryptionKey);
          } catch (error) {
            console.error(`Erreur lors du déchiffrement du commentaire de l'étape ${step.id}:`, error);
          }
        }

        // Récupérer les assignations pour cette étape
        const stepAssignments = assignmentsData
          .filter(assignment => assignment.step_id === step.id)
          .map(assignment => ({
            id: assignment.assignees.id,
            name: assignment.assignees.name, // Les noms des assignees ne sont pas chiffrés
            createdAt: assignment.assignees.created_at
          }));

        return {
          id: step.id,
          name: stepName,
          status: step.status as Status,
          receivedDate: step.received_date ? new Date(step.received_date).toISOString().split('T')[0] : null,
          comment,
          assignees: stepAssignments
        };
      }));

      return {
        id: client.id,
        name: clientName,
        steps: processedSteps
      };
    }));

    return clients;
  } catch (error) {
    console.error('Erreur lors de la récupération des clients:', error);
    throw error;
  }
}

/**
 * Ajoute un nouveau client
 * @param name Nom du client
 * @returns Le client créé
 */
export async function addClient(name: string): Promise<Client> {
  try {
    // Chiffrer le nom du client
    const encryptionKey = getEncryptionKey();
    const encryptedName = await encryptText(name, encryptionKey);

    // Insérer le client
    const { data: newClient, error: clientError } = await supabase
      .from('clients')
      .insert({ name: encryptedName })
      .select()
      .single();

    if (clientError) throw clientError;

    // Chiffrer les noms des étapes par défaut
    const stepsToInsert = await Promise.all(defaultSteps.map(async stepName => {
      const encryptedStepName = await encryptText(stepName, encryptionKey);
      return {
        client_id: newClient.id,
        name: encryptedStepName,
        status: 'manquant' as Status,
        received_date: null,
        comment: ''
      };
    }));

    // Insérer les étapes
    const { data: newSteps, error: stepsError } = await supabase
      .from('steps')
      .insert(stepsToInsert)
      .select();

    if (stepsError) throw stepsError;

    // Construire le client avec ses étapes
    const client: Client = {
      id: newClient.id,
      name, // Utiliser le nom en clair pour l'affichage
      steps: (newSteps as any[]).map(step => ({
        id: step.id,
        name: defaultSteps[newSteps.indexOf(step)], // Utiliser le nom en clair
        status: step.status,
        receivedDate: step.received_date ? new Date(step.received_date).toISOString().split('T')[0] : null,
        comment: step.comment || '',
        assignees: []
      }))
    };

    return client;
  } catch (error) {
    console.error('Erreur lors de l\'ajout du client:', error);
    throw error;
  }
}

/**
 * Supprime un client
 * @param clientId ID du client
 */
export async function deleteClient(clientId: string): Promise<void> {
  try {
    // Supprimer le client (les étapes seront supprimées en cascade)
    const { error } = await supabase
      .from('clients')
      .delete()
      .eq('id', clientId);

    if (error) throw error;
  } catch (error) {
    console.error('Erreur lors de la suppression du client:', error);
    throw error;
  }
}

/**
 * Met à jour le nom d'un client
 * @param clientId ID du client
 * @param name Nouveau nom
 */
export async function updateClientName(clientId: string, name: string): Promise<void> {
  try {
    // Chiffrer le nom du client
    const encryptionKey = getEncryptionKey();
    const encryptedName = await encryptText(name, encryptionKey);

    // Mettre à jour le client
    const { error } = await supabase
      .from('clients')
      .update({ name: encryptedName })
      .eq('id', clientId);

    if (error) throw error;
  } catch (error) {
    console.error('Erreur lors de la mise à jour du nom du client:', error);
    throw error;
  }
}
