-- Migration pour modifier la table client_patrimoine pour supporter le chiffrement
-- Date : 18 janvier 2025
-- Objectif : Changer le type de la colonne montant de numeric vers text pour stocker les données chiffrées

-- ============================================================================
-- MODIFICATION DE LA TABLE CLIENT_PATRIMOINE POUR LE CHIFFREMENT
-- ============================================================================

-- 1. Vérifier si la table existe, sinon la créer
CREATE TABLE IF NOT EXISTS public.client_patrimoine (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID NOT NULL REFERENCES public.clients(id) ON DELETE CASCADE,
  fournisseur TEXT NOT NULL,
  montant TEXT NOT NULL, -- <PERSON>é de NUMERIC vers TEXT pour le chiffrement
  devise TEXT NOT NULL DEFAULT 'EUR',
  type_placement TEXT,
  notes TEXT, -- Sera chiffré
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 2. Si la table existe déjà avec montant en numeric, la modifier
DO $$
BEGIN
  -- Vérifier si la colonne montant existe et est de type numeric
  IF EXISTS (
    SELECT 1 
    FROM information_schema.columns 
    WHERE table_schema = 'public' 
    AND table_name = 'client_patrimoine' 
    AND column_name = 'montant' 
    AND data_type = 'numeric'
  ) THEN
    -- Sauvegarder les données existantes
    CREATE TEMP TABLE patrimoine_backup AS 
    SELECT id, client_id, fournisseur, montant::text as montant_text, devise, type_placement, notes, created_at, updated_at
    FROM public.client_patrimoine;
    
    -- Supprimer l'ancienne colonne
    ALTER TABLE public.client_patrimoine DROP COLUMN montant;
    
    -- Ajouter la nouvelle colonne en TEXT
    ALTER TABLE public.client_patrimoine ADD COLUMN montant TEXT NOT NULL DEFAULT '0';
    
    -- Restaurer les données
    UPDATE public.client_patrimoine 
    SET montant = patrimoine_backup.montant_text
    FROM patrimoine_backup 
    WHERE public.client_patrimoine.id = patrimoine_backup.id;
    
    -- Nettoyer
    DROP TABLE patrimoine_backup;
    
    RAISE NOTICE 'Colonne montant convertie de numeric vers text pour le chiffrement';
  END IF;
END $$;

-- 3. Créer les index pour optimiser les performances
CREATE INDEX IF NOT EXISTS idx_client_patrimoine_client_id ON public.client_patrimoine(client_id);
CREATE INDEX IF NOT EXISTS idx_client_patrimoine_fournisseur ON public.client_patrimoine(fournisseur);

-- 4. Activer RLS sur la table
ALTER TABLE public.client_patrimoine ENABLE ROW LEVEL SECURITY;

-- 5. Créer la politique RLS
DROP POLICY IF EXISTS "Authenticated users can access patrimoine" ON public.client_patrimoine;
CREATE POLICY "Authenticated users can access patrimoine" ON public.client_patrimoine
  FOR ALL 
  USING (auth.uid() IS NOT NULL);

-- 6. Activer les souscriptions temps réel
ALTER PUBLICATION supabase_realtime ADD TABLE public.client_patrimoine;

-- 7. Créer la fonction de mise à jour automatique du timestamp
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- 8. Créer le trigger pour la mise à jour automatique
DROP TRIGGER IF EXISTS update_client_patrimoine_updated_at ON public.client_patrimoine;
CREATE TRIGGER update_client_patrimoine_updated_at
  BEFORE UPDATE ON public.client_patrimoine
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

-- 9. Créer la table des fournisseurs de patrimoine si elle n'existe pas
CREATE TABLE IF NOT EXISTS public.patrimoine_fournisseurs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  nom TEXT NOT NULL UNIQUE,
  actif BOOLEAN NOT NULL DEFAULT true,
  ordre_affichage INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 10. Insérer les fournisseurs par défaut
INSERT INTO public.patrimoine_fournisseurs (nom, ordre_affichage) VALUES
  ('ODDO', 1),
  ('UBS', 2),
  ('Neuflize', 3),
  ('KMC', 4),
  ('Fiducenter', 5),
  ('Selancia', 6),
  ('Magellant', 7),
  ('Extandam', 8),
  ('EDD', 9),
  ('OCO', 10)
ON CONFLICT (nom) DO NOTHING;

-- 11. Activer RLS sur la table fournisseurs
ALTER TABLE public.patrimoine_fournisseurs ENABLE ROW LEVEL SECURITY;

-- 12. Créer la politique RLS pour les fournisseurs
DROP POLICY IF EXISTS "Authenticated users can access fournisseurs" ON public.patrimoine_fournisseurs;
CREATE POLICY "Authenticated users can access fournisseurs" ON public.patrimoine_fournisseurs
  FOR ALL 
  USING (auth.uid() IS NOT NULL);

-- 13. Journaliser la migration
INSERT INTO public.data_access_logs (
  operation,
  table_name,
  details,
  timestamp
) VALUES (
  'update',
  'client_patrimoine',
  'Migration pour supporter le chiffrement - colonne montant convertie en TEXT',
  NOW()
);

-- 14. Commentaires sur les tables
COMMENT ON TABLE public.client_patrimoine IS 'Table du patrimoine des clients avec données chiffrées (montants et notes)';
COMMENT ON COLUMN public.client_patrimoine.montant IS 'Montant chiffré avec AES-256-GCM (format: enc:base64data)';
COMMENT ON COLUMN public.client_patrimoine.notes IS 'Notes chiffrées avec AES-256-GCM (format: enc:base64data)';
COMMENT ON TABLE public.patrimoine_fournisseurs IS 'Table des fournisseurs de patrimoine configurables';
