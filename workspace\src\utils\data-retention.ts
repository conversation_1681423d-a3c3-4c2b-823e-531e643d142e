/**
 * Utilitaire de gestion des durées de conservation des données
 * Conforme aux recommandations de la CNIL pour la protection des données
 */

import { supabase } from "@/integrations/supabase/client";

/**
 * Durées de conservation par type de données (en jours)
 */
export const RETENTION_PERIODS = {
  // Données principales
  CLIENTS: 365 * 3, // 3 ans après la fin de la relation commerciale
  STEPS: 365 * 3,   // 3 ans après la fin de la relation commerciale
  
  // Données de communication
  NOTIFICATIONS: 365, // 1 an
  PINGS: 365,         // 1 an
  
  // Données techniques
  LOGS: 180,          // 6 mois
};

/**
 * Interface pour les résultats de purge
 */
export interface PurgeResults {
  notifications: number;
  pings: number;
  logs: number;
  clients: number;
}

/**
 * Vérifie si une date est antérieure à la durée de conservation spécifiée
 * @param date - Date à vérifier
 * @param retentionDays - Durée de conservation en jours
 * @returns true si la date est expirée
 */
export function isExpired(date: string | null, retentionDays: number): boolean {
  if (!date) return false;
  
  const dateObj = new Date(date);
  const now = new Date();
  
  // Calculer la différence en millisecondes
  const diffMs = now.getTime() - dateObj.getTime();
  
  // Convertir en jours
  const diffDays = diffMs / (1000 * 60 * 60 * 24);
  
  return diffDays > retentionDays;
}

/**
 * Purge les données expirées d'une table
 * @param table - Nom de la table
 * @param dateField - Nom du champ de date
 * @param retentionDays - Durée de conservation en jours
 * @returns Nombre d'enregistrements supprimés
 */
export async function purgeExpiredData(
  table: string,
  dateField: string = 'created_at',
  retentionDays: number
): Promise<number> {
  try {
    // Calculer la date limite
    const now = new Date();
    const limitDate = new Date(now.getTime() - retentionDays * 24 * 60 * 60 * 1000);
    const limitDateStr = limitDate.toISOString();
    
    // Supprimer les enregistrements expirés
    const { data, error, count } = await supabase
      .from(table)
      .delete({ count: 'exact' })
      .lt(dateField, limitDateStr);
    
    if (error) {
      console.error(`Erreur lors de la purge des données expirées de ${table}:`, error);
      return 0;
    }
    
    console.log(`Purge des données expirées de ${table} terminée:`, count, 'enregistrements supprimés');
    return count || 0;
  } catch (error) {
    console.error(`Erreur lors de la purge des données expirées de ${table}:`, error);
    return 0;
  }
}

/**
 * Purge toutes les données expirées de l'application
 * @returns Résultat de la purge
 */
export async function purgeAllExpiredData(): Promise<PurgeResults> {
  try {
    console.log('🗑️ Début de la purge automatique des données expirées...');

    // Appeler la fonction PostgreSQL sécurisée qui fait tout le travail
    const { data, error } = await supabase
      .rpc('purge_all_expired_data');

    if (error) {
      console.error('Erreur lors de la purge des données expirées:', error);
      throw error;
    }

    // La fonction retourne un tableau avec un seul élément contenant les statistiques
    const result = data && data.length > 0 ? data[0] : {
      notifications_purged: 0,
      pings_purged: 0,
      logs_purged: 0,
      clients_anonymized: 0
    };

    const purgeResults: PurgeResults = {
      notifications: Number(result.notifications_purged) || 0,
      pings: Number(result.pings_purged) || 0,
      logs: Number(result.logs_purged) || 0,
      clients: Number(result.clients_anonymized) || 0
    };

    console.log('✅ Purge automatique terminée:', {
      notifications: `${purgeResults.notifications} notifications supprimées`,
      pings: `${purgeResults.pings} messages directs supprimés`,
      logs: `${purgeResults.logs} logs d'accès supprimés`,
      clients: `${purgeResults.clients} clients anonymisés`
    });

    return purgeResults;
  } catch (error) {
    console.error('Erreur lors de la purge automatique des données expirées:', error);
    
    // Retourner des valeurs par défaut en cas d'erreur
    return {
      notifications: 0,
      pings: 0,
      logs: 0,
      clients: 0
    };
  }
}

/**
 * Purge uniquement les notifications anciennes (plus de 1 an)
 * @returns Nombre de notifications supprimées
 */
export async function purgeOldNotifications(): Promise<number> {
  try {
    const { data, error } = await supabase
      .rpc('purge_old_notifications');

    if (error) {
      console.error('Erreur lors de la purge des notifications:', error);
      return 0;
    }

    console.log('✅ Notifications anciennes purgées');
    return 0; // La fonction ne retourne pas de compteur, mais elle fait le travail
  } catch (error) {
    console.error('Erreur lors de la purge des notifications:', error);
    return 0;
  }
}

/**
 * Purge uniquement les pings anciens (plus de 1 an)
 * @returns Nombre de pings supprimés
 */
export async function purgeOldPings(): Promise<number> {
  try {
    const { data, error } = await supabase
      .rpc('purge_old_pings');

    if (error) {
      console.error('Erreur lors de la purge des pings:', error);
      return 0;
    }

    console.log('✅ Messages directs anciens purgés');
    return 0; // La fonction ne retourne pas de compteur, mais elle fait le travail
  } catch (error) {
    console.error('Erreur lors de la purge des pings:', error);
    return 0;
  }
}

/**
 * Purge uniquement les logs d'accès anciens (plus de 6 mois)
 * @returns Nombre de logs supprimés
 */
export async function purgeOldDataAccessLogs(): Promise<number> {
  try {
    const { data, error } = await supabase
      .rpc('purge_old_data_access_logs');

    if (error) {
      console.error('Erreur lors de la purge des logs d\'accès:', error);
      return 0;
    }

    console.log('✅ Logs d\'accès anciens purgés');
    return 0; // La fonction ne retourne pas de compteur, mais elle fait le travail
  } catch (error) {
    console.error('Erreur lors de la purge des logs d\'accès:', error);
    return 0;
  }
}

/**
 * Anonymise uniquement les clients inactifs (plus de 3 ans)
 * @returns Nombre de clients anonymisés
 */
export async function anonymizeInactiveClients(): Promise<number> {
  try {
    const { data, error } = await supabase
      .rpc('anonymize_inactive_clients');

    if (error) {
      console.error('Erreur lors de l\'anonymisation des clients:', error);
      return 0;
    }

    console.log('✅ Clients inactifs anonymisés');
    return 0; // La fonction ne retourne pas de compteur, mais elle fait le travail
  } catch (error) {
    console.error('Erreur lors de l\'anonymisation des clients:', error);
    return 0;
  }
}

/**
 * Vérifie si les fonctions de purge sont disponibles dans la base de données
 * @returns true si les fonctions sont disponibles
 */
export async function checkPurgeFunctionsAvailable(): Promise<boolean> {
  try {
    // Tester l'existence de la fonction principale
    const { data, error } = await supabase
      .rpc('purge_all_expired_data');

    // Si pas d'erreur sur le nom de fonction, elle existe
    return !error || !error.message.includes('function') || !error.message.includes('does not exist');
  } catch (error) {
    console.warn('Fonctions de purge RGPD non disponibles:', error);
    return false;
  }
}
