/**
 * Script pour assigner <PERSON> et Quentin à toutes les étapes existantes
 * Ce script doit être exécuté une seule fois pour mettre à jour les données existantes
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtenir le répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Charger les variables d'environnement depuis le fichier .env
function loadEnv() {
  const envPath = path.join(__dirname, '..', '.env');
  if (!fs.existsSync(envPath)) {
    console.error('Fichier .env non trouvé');
    process.exit(1);
  }

  const envContent = fs.readFileSync(envPath, 'utf8');
  const env = {};
  
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value) {
      env[key.trim()] = value.trim().replace(/^["']|["']$/g, '');
    }
  });
  
  return env;
}

// Fonction pour déchiffrer un texte (version simplifiée)
function isEncrypted(text) {
  // Un texte chiffré contient généralement des caractères non-ASCII ou est en base64
  return text && (text.includes('::') || /^[A-Za-z0-9+/=]+$/.test(text));
}

async function assignDefaultUsers() {
  try {
    console.log('🚀 Assignation de Patrick et Quentin à toutes les étapes existantes...\n');

    const env = loadEnv();
    const supabaseUrl = env.VITE_SUPABASE_URL;
    const supabaseKey = env.VITE_SUPABASE_API_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Variables d\'environnement Supabase manquantes dans .env');
    }

    console.log('📡 Connexion à Supabase...');
    const supabase = createClient(supabaseUrl, supabaseKey);

    // 1. Récupérer tous les assignees pour identifier Patrick et Quentin
    console.log('\n🔍 Recherche de Patrick et Quentin...');
    const { data: assigneesData, error: assigneesError } = await supabase
      .from('assignees')
      .select('id, name');

    if (assigneesError) {
      throw new Error(`Erreur lors de la récupération des assignees: ${assigneesError.message}`);
    }

    // IDs fixes de Patrick et Quentin (identifiés précédemment)
    const defaultAssigneeIds = [
      '562a92cf-519b-4854-9749-f9dd90fe5776', // Patrick
      '95a4a571-9940-4206-a22b-7004f6c77d43'  // Quentin
    ];

    // Vérifier que ces IDs existent toujours
    const existingAssignees = assigneesData.filter(assignee =>
      defaultAssigneeIds.includes(assignee.id)
    );

    if (existingAssignees.length === 0) {
      console.log('   ❌ Aucun des assignees par défaut trouvé dans la base de données');
      console.log('   💡 Voici tous les assignees disponibles:');

      assigneesData.forEach((assignee, index) => {
        console.log(`      ${index + 1}. ID: ${assignee.id}, Nom: ${assignee.name}`);
      });

      console.log('\n   📝 Vérifiez les IDs dans le code de l\'application.');
      return;
    }

    console.log(`   ✅ ${existingAssignees.length} assignees par défaut trouvés sur ${defaultAssigneeIds.length} attendus`);

    console.log(`\n📋 Assignees par défaut identifiés: ${defaultAssigneeIds.length} profils`);

    // 2. Récupérer toutes les étapes existantes
    console.log('\n📊 Récupération de toutes les étapes existantes...');
    const { data: stepsData, error: stepsError } = await supabase
      .from('steps')
      .select('id');

    if (stepsError) {
      throw new Error(`Erreur lors de la récupération des étapes: ${stepsError.message}`);
    }

    console.log(`   📈 ${stepsData.length} étapes trouvées`);

    // 3. Récupérer les assignations existantes pour éviter les doublons
    console.log('\n🔄 Vérification des assignations existantes...');
    const { data: existingAssignments, error: assignmentsError } = await supabase
      .from('step_assignments')
      .select('step_id, assignee_id');

    if (assignmentsError) {
      throw new Error(`Erreur lors de la récupération des assignations: ${assignmentsError.message}`);
    }

    console.log(`   📊 ${existingAssignments.length} assignations existantes trouvées`);

    // 4. Créer les assignations manquantes
    console.log('\n⚡ Création des assignations manquantes...');
    
    let assignationsCreated = 0;
    let assignationsSkipped = 0;

    for (const step of stepsData) {
      for (const assigneeId of defaultAssigneeIds) {
        // Vérifier si l'assignation existe déjà
        const existingAssignment = existingAssignments.find(
          assignment => assignment.step_id === step.id && assignment.assignee_id === assigneeId
        );

        if (existingAssignment) {
          assignationsSkipped++;
          continue;
        }

        // Créer l'assignation
        try {
          const { error: insertError } = await supabase
            .from('step_assignments')
            .insert({ step_id: step.id, assignee_id: assigneeId });

          if (insertError) {
            console.log(`   ⚠️  Erreur pour l'étape ${step.id} et l'assignee ${assigneeId}: ${insertError.message}`);
          } else {
            assignationsCreated++;
          }
        } catch (e) {
          console.log(`   ⚠️  Erreur pour l'étape ${step.id} et l'assignee ${assigneeId}: ${e.message}`);
        }
      }
    }

    // 5. Résumé
    console.log('\n🎉 ASSIGNATION TERMINÉE !');
    console.log('=====================================');
    console.log(`   ✅ Assignations créées: ${assignationsCreated}`);
    console.log(`   ⏭️  Assignations ignorées (déjà existantes): ${assignationsSkipped}`);
    console.log(`   📊 Total des étapes: ${stepsData.length}`);
    console.log(`   👥 Assignees par défaut: ${defaultAssigneeIds.length}`);
    
    if (assignationsCreated > 0) {
      console.log('\n🚀 Patrick et Quentin sont maintenant assignés à toutes les étapes !');
      console.log('   Redémarrez l\'application pour voir les changements.');
    } else {
      console.log('\n💡 Toutes les assignations étaient déjà en place.');
    }

  } catch (error) {
    console.error('\n❌ Erreur lors de l\'assignation:', error.message);
    process.exit(1);
  }
}

// Exécuter le script
assignDefaultUsers();
