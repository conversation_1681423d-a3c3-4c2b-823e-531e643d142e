// Script de test pour vérifier le temps réel Supabase
// Exécuter avec : node test-realtime.js

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = 'https://ijvqlpenrwncbnjcdwjc.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImlqdnFscGVucnduY2JuamNkd2pjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg5NTU5NzQsImV4cCI6MjA2NDUzMTk3NH0.Ej_Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8Ej8';

const supabase = createClient(supabaseUrl, supabaseKey);

console.log('🔧 Test de connexion temps réel Supabase...');

// Test 1: Écouter les changements sur patrimoine_evolution
const channel1 = supabase
  .channel('test-patrimoine-evolution')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'patrimoine_evolution'
  }, (payload) => {
    console.log('📊 Changement détecté sur patrimoine_evolution:', payload);
  })
  .subscribe((status) => {
    console.log('📊 Statut patrimoine_evolution:', status);
  });

// Test 2: Écouter les changements sur patrimoine_evolution_fournisseurs
const channel2 = supabase
  .channel('test-patrimoine-evolution-fournisseurs')
  .on('postgres_changes', {
    event: '*',
    schema: 'public',
    table: 'patrimoine_evolution_fournisseurs'
  }, (payload) => {
    console.log('👥 Changement détecté sur patrimoine_evolution_fournisseurs:', payload);
  })
  .subscribe((status) => {
    console.log('👥 Statut patrimoine_evolution_fournisseurs:', status);
  });

// Test 3: Insérer des données de test après 3 secondes
setTimeout(async () => {
  console.log('🧪 Insertion de données de test...');
  
  try {
    // Test insertion dans patrimoine_evolution
    const { data: snapshot, error: error1 } = await supabase
      .from('patrimoine_evolution')
      .insert({
        date_snapshot: '2025-01-15',
        total_sous_gestion: 'test_encrypted_value',
        devise: 'EUR',
        commentaire: 'Test temps réel'
      })
      .select()
      .single();
    
    if (error1) {
      console.error('❌ Erreur insertion patrimoine_evolution:', error1);
    } else {
      console.log('✅ Insertion patrimoine_evolution réussie:', snapshot);
    }

    // Test insertion dans patrimoine_evolution_fournisseurs
    const { data: evolution, error: error2 } = await supabase
      .from('patrimoine_evolution_fournisseurs')
      .insert({
        date_snapshot: '2025-01-15',
        fournisseur_id: 'test-fournisseur',
        montant_chiffre: 'test_encrypted_value',
        devise: 'EUR',
        commentaire: 'Test temps réel fournisseur'
      })
      .select()
      .single();
    
    if (error2) {
      console.error('❌ Erreur insertion patrimoine_evolution_fournisseurs:', error2);
    } else {
      console.log('✅ Insertion patrimoine_evolution_fournisseurs réussie:', evolution);
    }

  } catch (error) {
    console.error('❌ Erreur générale:', error);
  }
}, 3000);

// Nettoyer après 10 secondes
setTimeout(() => {
  console.log('🧹 Nettoyage des canaux...');
  supabase.removeChannel(channel1);
  supabase.removeChannel(channel2);
  console.log('✅ Test terminé');
  process.exit(0);
}, 10000);

console.log('⏳ Test en cours... (10 secondes)');
