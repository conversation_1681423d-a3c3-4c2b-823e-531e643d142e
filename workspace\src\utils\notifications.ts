/**
 * Send a desktop notification with sound
 *
 * @param title The notification title
 * @param body The notification body text
 * @param icon Optional icon path
 * @returns Promise that resolves when notification is sent
 */
export async function sendDesktopNotification(
  title: string,
  body: string,
  icon?: string
): Promise<void> {
  try {
    console.log('Sending notification:', title, body);

    // Utiliser les notifications du navigateur
    if ("Notification" in window) {
      const permission = Notification.permission;

      if (permission === "granted") {
        new Notification(title, { body, icon });
        playNotificationSound();
      } else if (permission !== "denied") {
        const requestedPermission = await Notification.requestPermission();
        if (requestedPermission === "granted") {
          new Notification(title, { body, icon });
          playNotificationSound();
        }
      }
    }
  } catch (error) {
    console.error('Error sending notification:', error);
  }
}

/**
 * Play a notification sound
 */
function playNotificationSound(): void {
  try {
    // Create an audio element
    const audio = new Audio('/sounds/notification.mp3');

    // Set volume (0.0 to 1.0)
    audio.volume = 0.7;

    // Play the sound
    audio.play().catch(error => {
      console.error('Error playing notification sound:', error);
    });
  } catch (error) {
    console.error('Error creating notification sound:', error);
  }
}
