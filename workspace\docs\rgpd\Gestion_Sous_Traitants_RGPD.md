# 5.7. Registre des sous-traitants

WeMa a établi un registre des sous-traitants qui recense les prestataires ayant accès aux données personnelles traitées dans le cadre de l'application WeMa Tracker. Ce registre comprend pour chaque sous-traitant les informations suivantes :

- Identité et coordonnées du sous-traitant
- Catégories de traitements effectués pour le compte de WeMa
- Type de données concernées
- Garanties offertes en matière de protection des données
- Localisation des données
- Base contractuelle de la relation (conditions générales, contrat spécifique, etc.)
- Date de la dernière évaluation de conformité

## Sous-traitant principal : Supabase

### Évaluation préalable

Avant de sélectionner Supabase comme solution de stockage et de gestion de base de données, WeMa a procédé à une évaluation préalable des garanties offertes :

- **Certifications et conformité** : Supabase s'engage à respecter les principes du RGPD et dispose d'une politique de confidentialité publique détaillant ses engagements en matière de protection des données.
- **Mesures de sécurité** : Supabase utilise PostgreSQL, une base de données reconnue pour sa robustesse et sa sécurité, et met en œuvre des mesures de chiffrement des données au repos et en transit.
- **Politique de confidentialité** : Supabase dispose d'une politique de confidentialité accessible publiquement qui détaille ses pratiques en matière de traitement des données.
- **Réputation et fiabilité** : Supabase est une solution largement adoptée par la communauté des développeurs et bénéficie d'une bonne réputation en matière de sécurité et de fiabilité.

### Due diligence

WeMa a vérifié les éléments suivants concernant Supabase :

- **Conformité au RGPD** : Supabase s'engage à respecter les principes du RGPD et à faciliter la conformité de ses clients.
- **Localisation des données** : Les données sont hébergées dans des centres de données situés dans l'Union Européenne (région eu-central-1).
- **Transferts hors UE** : Aucun transfert systématique de données hors UE n'est prévu dans le cadre normal des opérations. Les accès techniques éventuels depuis des pays tiers sont encadrés par des garanties appropriées.
- **Sous-traitants ultérieurs** : Supabase utilise AWS comme fournisseur d'infrastructure cloud, qui dispose de certifications de sécurité reconnues (ISO 27001, SOC 2, etc.).

### Contractualisation

WeMa utilise Supabase avec un compte standard soumis aux conditions générales d'utilisation et à la politique de confidentialité de Supabase. Ces documents constituent le cadre contractuel de la relation de sous-traitance :

- Les [Conditions d'utilisation de Supabase](https://supabase.com/terms) définissent les responsabilités générales des parties
- La [Politique de confidentialité de Supabase](https://supabase.com/privacy) précise les engagements en matière de protection des données

WeMa a analysé ces documents et vérifié qu'ils abordent les points essentiels requis par l'article 28 du RGPD, notamment :

- L'engagement de Supabase à respecter la confidentialité des données
- La mise en œuvre de mesures de sécurité appropriées
- La notification des violations de données
- La possibilité de supprimer les données à la demande du client

WeMa prévoit, dans le cadre de l'évolution de son utilisation de Supabase, d'évaluer l'opportunité de passer à une offre payante qui pourrait inclure des garanties contractuelles supplémentaires.

### Audits périodiques

L'article 28 du RGPD prévoit que le responsable de traitement dispose d'un droit d'audit sur ses sous-traitants. Dans le cadre de l'utilisation d'un compte standard Supabase, ce droit d'audit s'exerce de manière adaptée :

1. **Droit d'audit contractuel** :
   - Les conditions d'utilisation de Supabase ne prévoient pas explicitement un droit d'audit pour les utilisateurs de comptes gratuits
   - Pour les comptes payants, Supabase peut proposer des garanties supplémentaires en matière d'audit

2. **Mesures alternatives mises en œuvre par WeMa** :
   - Examen régulier des certifications et attestations publiques de Supabase
   - Suivi des rapports de transparence et des communications officielles sur la sécurité
   - Veille sur les mises à jour des conditions d'utilisation et de la politique de confidentialité
   - Évaluation des incidents de sécurité éventuellement rapportés publiquement
   - Tests techniques de base sur la sécurité des données stockées (sans compromettre le service)

WeMa a réalisé une première évaluation documentaire de Supabase en mai 2025 et s'engage à effectuer un suivi annuel de la conformité du sous-traitant, en adaptant sa démarche d'audit en fonction de l'évolution de sa relation contractuelle avec Supabase.

### Gestion des incidents

En cas d'incident de sécurité impliquant Supabase, WeMa suivra la procédure suivante :

1. Réception et enregistrement de la notification d'incident de la part de Supabase
2. Évaluation de l'impact potentiel sur les données des personnes concernées
3. Mise en œuvre des mesures d'atténuation appropriées
4. Notification à la CNIL si nécessaire (en cas de risque pour les droits et libertés des personnes)
5. Information des personnes concernées si nécessaire (en cas de risque élevé)
6. Documentation de l'incident et des mesures prises
7. Suivi avec Supabase des mesures correctives mises en place

## Autres sous-traitants

### Services d'infrastructure

**Fournisseur d'hébergement de l'application** : Microsoft (via GitHub Pages) et Tauri (pour l'application desktop)

- **Évaluation préalable** : Microsoft dispose de certifications reconnues en matière de sécurité (ISO 27001, SOC 2) et propose des garanties solides en matière de protection des données. Tauri est un framework open-source qui permet de créer des applications desktop sans accès aux données utilisateur.
- **Due diligence** : L'application étant principalement exécutée localement sur les postes des utilisateurs, les données ne transitent que vers Supabase. GitHub Pages n'héberge que le code de l'application, sans données personnelles.
- **Contractualisation** : Utilisation des conditions standard de GitHub pour l'hébergement du code source. Aucun contrat spécifique n'est nécessaire pour Tauri puisqu'il s'agit d'un framework local qui ne collecte pas de données.
- **Suivi** : Veille sur les mises à jour de sécurité de Tauri et GitHub.

### Services de développement et de maintenance

**Prestataire de développement** : Équipe interne WeMa

- Le développement est réalisé en interne, ce qui limite les risques liés à la sous-traitance.
- Les développeurs ont signé des engagements de confidentialité et ont reçu une sensibilisation aux principes du RGPD.

## Mise à jour du registre

Le registre des sous-traitants sera mis à jour dans les cas suivants :

- Engagement d'un nouveau sous-traitant
- Modification substantielle des traitements confiés à un sous-traitant existant
- Changement dans les garanties offertes par un sous-traitant (modifications des conditions d'utilisation)
- Changement de formule d'abonnement (passage à une offre payante)
- Fin de l'utilisation d'un service

Le registre a été initialement établi en mai 2025 et fera l'objet d'une revue annuelle.
