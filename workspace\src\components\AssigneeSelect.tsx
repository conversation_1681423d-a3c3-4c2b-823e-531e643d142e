import React, { useState, useEffect } from 'react';
import { Assignee } from '@/types';
import { useClientContext } from '@/contexts/ClientContext';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { UserPlus, X } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuLabel
} from '@/components/ui/dropdown-menu';

interface AssigneeSelectProps {
  clientId: string;
  stepId: string;
  currentAssignees?: Assignee[];
}

export const AssigneeSelect: React.FC<AssigneeSelectProps> = ({
  clientId,
  stepId,
  currentAssignees = []
}) => {
  const { getAssignees, assignStepToUser, removeStepAssignment } = useClientContext();
  const [assignees, setAssignees] = useState<Assignee[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAssignees = async () => {
      try {
        setIsLoading(true);
        setError(null);
        console.log(`Chargement des assignees pour l'étape ${stepId}...`);

        // S'assurer que currentAssignees est toujours un tableau
        const safeCurrentAssignees = Array.isArray(currentAssignees) ? currentAssignees : [];
        console.log('Assignees actuels:', safeCurrentAssignees);

        const fetchedAssignees = await getAssignees();
        console.log('Tous les assignees disponibles:', fetchedAssignees);

        // Filter out already assigned users
        const filteredAssignees = fetchedAssignees.filter(a =>
          !safeCurrentAssignees.some(ca => ca.id === a.id)
        );
        console.log('Assignees filtrés (non assignés):', filteredAssignees);
        
        // Diagnostic détaillé
        console.group('📋 Diagnostic AssigneeSelect:');
        console.log(`Total assignees disponibles: ${fetchedAssignees.length}`);
        console.log('Liste complète:', fetchedAssignees.map(a => `${a.name} (${a.id})`));
        console.log(`Assignees actuellement assignés: ${safeCurrentAssignees.length}`);
        console.log('Assignés:', safeCurrentAssignees.map(a => `${a.name} (${a.id})`));
        console.log(`Assignees restants à assigner: ${filteredAssignees.length}`);
        console.log('Restants:', filteredAssignees.map(a => `${a.name} (${a.id})`));
        console.groupEnd();

        setAssignees(filteredAssignees);
      } catch (err) {
        console.error('Erreur lors du chargement des assignees:', err);
        setError('Impossible de charger les personnes');
      } finally {
        setIsLoading(false);
      }
    };

    fetchAssignees();
  }, [clientId, stepId, currentAssignees, getAssignees]);

  const handleAddAssignee = async (assigneeId: string) => {
    await assignStepToUser(clientId, stepId, assigneeId);
  };

  const handleRemoveAssignee = async (assigneeId: string) => {
    await removeStepAssignment(clientId, stepId, assigneeId);
  };

  // Get the first initial for the avatar fallback
  const getInitials = (name: string | undefined) => {
    if (!name) return '?';
    
    // Nettoyer le nom de tout caractère étrange
    const cleanName = name.replace(/[^a-zA-ZÀ-ÿ0-9\s]/g, '');
    if (!cleanName) return '?';
    
    // Prendre la première lettre du nom nettoyé
    const firstChar = cleanName.charAt(0).toUpperCase();
    return firstChar || '?';
  };

  // Nettoyer et valider le nom pour l'affichage
  const getDisplayName = (name: string | undefined) => {
    if (!name) return 'Nom non défini';
    
    // Si le nom contient uniquement des chiffres ou des caractères suspects
    if (/^[0-9]+$/.test(name)) {
      return `Utilisateur ${name}`;
    }
    
    // Nettoyer les caractères de contrôle et autres caractères problématiques
    const cleanName = name.replace(/[\x00-\x1F\x7F-\x9F]/g, '').trim();
    if (!cleanName) {
      return `Utilisateur (${name.substring(0, 3)}...)`;
    }
    
    return cleanName;
  };

  return (
    <div className="flex items-center space-x-2">
      <div className="flex space-x-1">
        {currentAssignees?.map(assignee => (
          <div
            key={assignee.id}
            className="relative group"
          >
            <Avatar className="h-8 w-8 border-2 border-white hover:scale-105 transition-transform bg-blue-100">
              <AvatarFallback className="bg-blue-100 text-blue-600 font-medium">
                {getInitials(assignee.name)}
              </AvatarFallback>
            </Avatar>
            <Button
              size="icon"
              variant="destructive"
              className="absolute -top-1 -right-1 h-4 w-4 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              onClick={(e) => {
                e.stopPropagation();
                handleRemoveAssignee(assignee.id);
              }}
            >
              <X className="h-2 w-2" />
            </Button>
          </div>
        ))}
      </div>

      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="outline"
            size="icon"
            className="rounded-full h-8 w-8 border-dashed hover:border-blue-500 hover:text-blue-500"
          >
            <UserPlus className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="bg-white">
          <DropdownMenuLabel>Assigner une personne</DropdownMenuLabel>
          {isLoading ? (
            <div className="px-2 py-1.5 text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <div className="animate-spin h-4 w-4 border-2 border-blue-500 rounded-full border-t-transparent"></div>
                <span>Chargement...</span>
              </div>
            </div>
          ) : error ? (
            <div className="px-2 py-1.5 text-sm text-red-500">{error}</div>
          ) : assignees.length === 0 ? (
            <div className="px-2 py-1.5 text-sm text-gray-500">Toutes les personnes sont assignées</div>
          ) : (
            assignees.map(assignee => (
              <DropdownMenuItem
                key={assignee.id}
                onClick={() => handleAddAssignee(assignee.id)}
                className="cursor-pointer hover:bg-blue-50"
              >
                <Avatar className="h-6 w-6 mr-2">
                  <AvatarFallback className="bg-blue-100 text-blue-600 text-xs font-medium">
                    {getInitials(assignee.name)}
                  </AvatarFallback>
                </Avatar>
                {getDisplayName(assignee.name)}
              </DropdownMenuItem>
            ))
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
};
