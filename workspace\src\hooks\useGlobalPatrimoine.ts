import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { encryptText, safeDecrypt, isEncrypted } from '@/utils/encryption';

interface PatrimoineEntry {
  id: string;
  client_id: string;
  fournisseur: string;
  montant: number;
  devise: string;
  type_placement?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

interface GlobalPatrimoineData {
  entries: PatrimoineEntry[];
  loading: boolean;
  error: string | null;
}

export const useGlobalPatrimoine = () => {
  const [data, setData] = useState<GlobalPatrimoineData>({
    entries: [],
    loading: false,
    error: null
  });

  // Charger toutes les données de patrimoine
  const loadAllPatrimoine = useCallback(async () => {
    try {
      setData(prev => ({ ...prev, loading: true, error: null }));

      const { data: patrimoineData, error } = await supabase
        .from('client_patrimoine')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Déchiffrer les montants et notes
      const decryptedEntries = await Promise.all(
        (patrimoineData || []).map(async (entry) => {
          let montantDecrypted = 0;
          let notesDecrypted = null;

          // Déchiffrer le montant
          if (entry.montant) {
            if (isEncrypted(entry.montant.toString())) {
              const decrypted = await safeDecrypt(entry.montant.toString());
              montantDecrypted = parseFloat(decrypted) || 0;
            } else {
              montantDecrypted = parseFloat(entry.montant.toString()) || 0;
            }
          }

          // Déchiffrer les notes
          if (entry.notes) {
            notesDecrypted = await safeDecrypt(entry.notes);
          }

          return {
            ...entry,
            montant: montantDecrypted,
            notes: notesDecrypted
          };
        })
      );

      setData({
        entries: decryptedEntries,
        loading: false,
        error: null
      });

    } catch (error) {
      console.error('Erreur lors du chargement du patrimoine global:', error);
      setData(prev => ({
        ...prev,
        loading: false,
        error: 'Erreur lors du chargement des données'
      }));
    }
  }, []);

  // Mettre à jour une entrée existante
  const updatePatrimoineEntry = useCallback(async (
    entryId: string,
    clientId: string,
    fournisseurNom: string,
    newMontant: number
  ) => {
    try {
      // Chiffrer le nouveau montant
      const encryptedMontant = await encryptText(newMontant.toString());

      const { error } = await supabase
        .from('client_patrimoine')
        .update({ montant: encryptedMontant })
        .eq('id', entryId);

      if (error) throw error;

      // Mettre à jour localement
      setData(prev => ({
        ...prev,
        entries: prev.entries.map(entry =>
          entry.id === entryId
            ? { ...entry, montant: newMontant }
            : entry
        )
      }));

      toast.success('Montant mis à jour');
      return true;

    } catch (error) {
      console.error('Erreur lors de la mise à jour:', error);
      toast.error('Erreur lors de la mise à jour');
      return false;
    }
  }, []);

  // Créer une nouvelle entrée
  const createPatrimoineEntry = useCallback(async (
    clientId: string,
    fournisseurNom: string,
    montant: number,
    devise: string = 'EUR'
  ) => {
    try {
      // Chiffrer le montant
      const encryptedMontant = await encryptText(montant.toString());

      const { data: newEntry, error } = await supabase
        .from('client_patrimoine')
        .insert({
          client_id: clientId,
          fournisseur: fournisseurNom,
          montant: encryptedMontant,
          devise
        })
        .select()
        .single();

      if (error) throw error;

      // Ajouter localement avec le montant déchiffré
      const decryptedEntry = {
        ...newEntry,
        montant: montant
      };

      setData(prev => ({
        ...prev,
        entries: [decryptedEntry, ...prev.entries]
      }));

      toast.success('Entrée créée');
      return true;

    } catch (error) {
      console.error('Erreur lors de la création:', error);
      toast.error('Erreur lors de la création');
      return false;
    }
  }, []);

  // Supprimer une entrée
  const deletePatrimoineEntry = useCallback(async (entryId: string) => {
    try {
      const { error } = await supabase
        .from('client_patrimoine')
        .delete()
        .eq('id', entryId);

      if (error) throw error;

      // Supprimer localement
      setData(prev => ({
        ...prev,
        entries: prev.entries.filter(entry => entry.id !== entryId)
      }));

      toast.success('Entrée supprimée');
      return true;

    } catch (error) {
      console.error('Erreur lors de la suppression:', error);
      toast.error('Erreur lors de la suppression');
      return false;
    }
  }, []);

  // Mettre à jour ou créer une entrée (upsert)
  const upsertPatrimoineEntry = useCallback(async (
    clientId: string,
    fournisseurNom: string,
    montant: number,
    devise: string = 'EUR'
  ) => {
    // Chercher si une entrée existe déjà
    const existingEntry = data.entries.find(
      entry => entry.client_id === clientId && entry.fournisseur === fournisseurNom
    );

    if (existingEntry) {
      // Mettre à jour l'entrée existante
      return await updatePatrimoineEntry(existingEntry.id, clientId, fournisseurNom, montant);
    } else {
      // Créer une nouvelle entrée
      return await createPatrimoineEntry(clientId, fournisseurNom, montant, devise);
    }
  }, [data.entries, updatePatrimoineEntry, createPatrimoineEntry]);

  // Obtenir les totaux par client
  const getClientTotals = useCallback(() => {
    const totals = new Map<string, number>();
    
    data.entries.forEach(entry => {
      const currentTotal = totals.get(entry.client_id) || 0;
      totals.set(entry.client_id, currentTotal + entry.montant);
    });

    return totals;
  }, [data.entries]);

  // Obtenir les totaux par fournisseur
  const getFournisseurTotals = useCallback(() => {
    const totals = new Map<string, number>();
    
    data.entries.forEach(entry => {
      const currentTotal = totals.get(entry.fournisseur) || 0;
      totals.set(entry.fournisseur, currentTotal + entry.montant);
    });

    return totals;
  }, [data.entries]);

  // Obtenir le total général
  const getGrandTotal = useCallback(() => {
    return data.entries.reduce((total, entry) => total + entry.montant, 0);
  }, [data.entries]);

  return {
    // État
    entries: data.entries,
    loading: data.loading,
    error: data.error,

    // Actions
    loadAllPatrimoine,
    updatePatrimoineEntry,
    createPatrimoineEntry,
    deletePatrimoineEntry,
    upsertPatrimoineEntry,

    // Calculs
    getClientTotals,
    getFournisseurTotals,
    getGrandTotal
  };
};
