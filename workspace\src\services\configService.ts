/**
 * Service de configuration pour l'application WeMa Tracker
 * Gère les paramètres configurables comme les assignations par défaut
 */

import { supabase } from "@/integrations/supabase/client";
import { Assignee } from "@/types";

/**
 * Interface pour la configuration de l'application
 */
export interface AppConfig {
  defaultAssignees: string[]; // IDs des assignees par défaut
  autoAssignNewSteps: boolean; // Assigner automatiquement les nouvelles étapes
}

/**
 * Configuration par défaut de l'application
 */
const DEFAULT_CONFIG: AppConfig = {
  defaultAssignees: [
    'ece3b34a-a36d-4334-b907-4cacbe147a70', // Patrick
    '1d2e5e60-1014-45f0-8a17-0101ee032b69'  // Quentin
  ],
  autoAssignNewSteps: true
};

/**
 * Clé pour stocker la configuration dans localStorage
 */
const CONFIG_STORAGE_KEY = 'wema-tracker-config';

/**
 * Récupère la configuration de l'application
 * @returns Configuration actuelle
 */
export function getAppConfig(): AppConfig {
  try {
    const stored = localStorage.getItem(CONFIG_STORAGE_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      return { ...DEFAULT_CONFIG, ...parsed };
    }
  } catch (error) {
    console.error('Erreur lors de la lecture de la configuration:', error);
  }

  return DEFAULT_CONFIG;
}

/**
 * Sauvegarde la configuration de l'application
 * @param config Configuration à sauvegarder
 */
export function saveAppConfig(config: Partial<AppConfig>): void {
  try {
    const currentConfig = getAppConfig();
    const newConfig = { ...currentConfig, ...config };
    localStorage.setItem(CONFIG_STORAGE_KEY, JSON.stringify(newConfig));
  } catch (error) {
    console.error('Erreur lors de la sauvegarde de la configuration:', error);
  }
}

/**
 * Récupère les assignees par défaut configurés
 * @returns Liste des assignees par défaut
 */
export async function getDefaultAssignees(): Promise<Assignee[]> {
  try {
    const config = getAppConfig();

    if (config.defaultAssignees.length === 0) {
      return [];
    }

    const { data: assigneesData, error } = await supabase
      .from('assignees')
      .select('*')
      .in('id', config.defaultAssignees);

    if (error) {
      console.error('Erreur lors de la récupération des assignees par défaut:', error);
      return [];
    }

    return assigneesData.map(assignee => ({
      id: assignee.id,
      name: assignee.name,
      createdAt: assignee.created_at
    }));
  } catch (error) {
    console.error('Erreur lors de la récupération des assignees par défaut:', error);
    return [];
  }
}

/**
 * Définit les assignees par défaut
 * @param assigneeIds IDs des assignees à définir par défaut
 */
export function setDefaultAssignees(assigneeIds: string[]): void {
  saveAppConfig({ defaultAssignees: assigneeIds });
}

/**
 * Ajoute un assignee aux assignees par défaut
 * @param assigneeId ID de l'assignee à ajouter
 */
export function addDefaultAssignee(assigneeId: string): void {
  const config = getAppConfig();
  if (!config.defaultAssignees.includes(assigneeId)) {
    const newAssignees = [...config.defaultAssignees, assigneeId];
    saveAppConfig({ defaultAssignees: newAssignees });
  }
}

/**
 * Retire un assignee des assignees par défaut
 * @param assigneeId ID de l'assignee à retirer
 */
export function removeDefaultAssignee(assigneeId: string): void {
  const config = getAppConfig();
  const newAssignees = config.defaultAssignees.filter(id => id !== assigneeId);
  saveAppConfig({ defaultAssignees: newAssignees });
}

/**
 * Assigne automatiquement les assignees par défaut à une étape
 * @param stepId ID de l'étape
 * @returns true si l'assignation a réussi
 */
export async function assignDefaultUsersToStep(stepId: string): Promise<boolean> {
  try {
    const config = getAppConfig();

    if (!config.autoAssignNewSteps || config.defaultAssignees.length === 0) {
      return true; // Pas d'erreur, juste pas d'assignation automatique
    }

    // Vérifier que les assignees existent toujours
    const defaultAssignees = await getDefaultAssignees();

    if (defaultAssignees.length === 0) {
      console.log('Aucun assignee par défaut valide trouvé');
      return true;
    }

    // Assigner chaque utilisateur par défaut
    const assignments = defaultAssignees.map(assignee => ({
      step_id: stepId,
      assignee_id: assignee.id
    }));

    const { error } = await supabase
      .from('step_assignments')
      .insert(assignments);

    if (error) {
      console.error('Erreur lors de l\'assignation automatique:', error);
      return false;
    }

    console.log(`Assignation automatique réussie: ${defaultAssignees.length} assignees -> ${stepId}`);
    return true;
  } catch (error) {
    console.error('Erreur lors de l\'assignation automatique:', error);
    return false;
  }
}
