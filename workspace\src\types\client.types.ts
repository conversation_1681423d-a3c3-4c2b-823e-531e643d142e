/**
 * Types spécifiques aux clients et à leur gestion
 */

export type UUID = string;
export type ISODateString = string;
export type Status = 'manquant' | 'transmis' | 'validation' | 'valide' | 'refuse' | 'urgent';

/**
 * Interface pour un assignee (personne assignée à une tâche)
 */
export interface Assignee {
  id: UUID;
  name: string;
  createdAt?: ISODateString;
}

/**
 * Interface pour une étape de traitement
 */
export interface Step {
  id: UUID;
  name: string;
  status: Status;
  receivedDate: ISODateString | null;
  comment: string;
  assignees: Assignee[];
}

/**
 * Interface pour un client (dossier)
 */
export interface Client {
  id: UUID;
  name: string;
  dueDate: ISODateString | null; // Date de rendu du dossier
  completed: boolean; // Marqué comme terminé manuellement
  steps: Step[];
  created_at?: ISODateString;
}

/**
 * Interface pour les statistiques de progression
 */
export interface ProgressStats {
  completed: number;
  total: number;
  percentage: number;
}

/**
 * Interface pour les tâches urgentes
 */
export interface UrgentTask {
  clientId: UUID;
  clientName: string;
  stepId: UUID;
  stepName: string;
  dueDate?: ISODateString;
}

/**
 * Interface pour les filtres de recherche
 */
export interface ClientSearchFilters {
  query?: string;
  status?: 'active' | 'completed' | 'all';
  urgent?: boolean;
  assigneeId?: UUID;
  dueDateBefore?: ISODateString;
  dueDateAfter?: ISODateString;
}

/**
 * Interface pour les options de tri
 */
export interface ClientSortOptions {
  field: 'name' | 'dueDate' | 'created_at' | 'progress';
  direction: 'asc' | 'desc';
  mode: 'manual' | 'automatic';
}

/**
 * Interface pour les résultats de recherche
 */
export interface ClientSearchResult {
  clients: Client[];
  totalCount: number;
  hasMore: boolean;
}

/**
 * Interface pour les opérations en lot
 */
export interface ClientBatchOperation {
  type: 'update_status' | 'assign_user' | 'set_due_date' | 'mark_completed';
  clientIds: UUID[];
  data: any;
}

/**
 * Interface pour l'historique des modifications
 */
export interface ClientHistory {
  id: UUID;
  clientId: UUID;
  action: string;
  oldValue?: any;
  newValue?: any;
  userId?: UUID;
  timestamp: ISODateString;
}

/**
 * Interface pour les templates de clients
 */
export interface ClientTemplate {
  id: UUID;
  name: string;
  description?: string;
  steps: Omit<Step, 'id' | 'assignees'>[];
  defaultDueDate?: number; // Nombre de jours à partir de la création
  isDefault: boolean;
  created_at: ISODateString;
}

/**
 * Interface pour les métriques de performance
 */
export interface ClientMetrics {
  totalClients: number;
  activeClients: number;
  completedClients: number;
  urgentTasks: number;
  averageCompletionTime: number; // en jours
  completionRate: number; // pourcentage
  topBottlenecks: Array<{
    stepName: string;
    averageTime: number;
    count: number;
  }>;
}

/**
 * Types pour les événements de cycle de vie des clients
 */
export type ClientLifecycleEvent = 
  | { type: 'created'; client: Client }
  | { type: 'updated'; client: Client; changes: Partial<Client> }
  | { type: 'completed'; client: Client }
  | { type: 'deleted'; clientId: UUID }
  | { type: 'step_added'; client: Client; step: Step }
  | { type: 'step_updated'; client: Client; step: Step; changes: Partial<Step> }
  | { type: 'step_deleted'; client: Client; stepId: UUID };

/**
 * Interface pour les callbacks d'événements
 */
export interface ClientEventCallbacks {
  onClientCreated?: (client: Client) => void;
  onClientUpdated?: (client: Client, changes: Partial<Client>) => void;
  onClientCompleted?: (client: Client) => void;
  onClientDeleted?: (clientId: UUID) => void;
  onStepUpdated?: (client: Client, step: Step) => void;
  onUrgentTaskDetected?: (task: UrgentTask) => void;
}

/**
 * Interface pour la configuration des clients
 */
export interface ClientConfiguration {
  defaultSteps: string[];
  urgentThresholdDays: number;
  autoAssignNewSteps: boolean;
  defaultAssignees: UUID[];
  enableNotifications: boolean;
  enableRealtime: boolean;
  cacheTimeout: number; // en millisecondes
}

/**
 * Types utilitaires pour les validations
 */
export type ClientValidationError = {
  field: keyof Client | keyof Step;
  message: string;
  code: string;
};

export type ClientValidationResult = {
  isValid: boolean;
  errors: ClientValidationError[];
};

/**
 * Interface pour les exports de données
 */
export interface ClientExportOptions {
  format: 'json' | 'csv' | 'xlsx';
  includeSteps: boolean;
  includeAssignees: boolean;
  includeHistory: boolean;
  dateRange?: {
    start: ISODateString;
    end: ISODateString;
  };
  filters?: ClientSearchFilters;
}

/**
 * Interface pour les imports de données
 */
export interface ClientImportResult {
  success: boolean;
  importedCount: number;
  skippedCount: number;
  errors: Array<{
    row: number;
    message: string;
    data?: any;
  }>;
}
