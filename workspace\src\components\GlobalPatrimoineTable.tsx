import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  TrendingUp,
  Building2,
  Euro,
  X,
  Plus,
  Edit3,
  Settings,
  Trash2,
  BarChart3,
  Calculator,
  Maximize2,
  Minimize2,
  Calendar,
  ChevronDown,
  ChevronUp,
  Save
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useClientContext } from '@/contexts/ClientContext';
import { useGlobalPatrimoine } from '@/hooks/useGlobalPatrimoine';
import { usePatrimoineCache } from '@/hooks/usePatrimoineCache';
import { usePatrimoineEvolutionSimple } from '@/hooks/usePatrimoineEvolutionSimple';
import { PatrimoineAnalytics } from './PatrimoineAnalytics';


interface PatrimoineMatrixEntry {
  clientId: string;
  clientName: string;
  fournisseurId: string;
  fournisseurNom: string;
  montant: number;
  entryId?: string; // ID de l'entrée en base si elle existe
}

interface MatrixData {
  clients: Array<{
    id: string;
    name: string;
    total: number;
  }>;
  fournisseurs: Array<{
    id: string;
    nom: string;
    total: number;
  }>;
  matrix: Map<string, Map<string, PatrimoineMatrixEntry>>; // clientId -> fournisseurId -> entry
  grandTotal: number;
}

interface GlobalPatrimoineTableProps {
  isOpen: boolean;
  onClose: () => void;
}

export const GlobalPatrimoineTable: React.FC<GlobalPatrimoineTableProps> = ({ isOpen, onClose }) => {
  const { clients } = useClientContext();
  const {
    entries,
    loading,
    loadAllPatrimoine,
    upsertPatrimoineEntry,
    getClientTotals,
    getFournisseurTotals,
    getGrandTotal
  } = useGlobalPatrimoine();

  const {
    fournisseurs: cachedFournisseurs,
    loadPatrimoineData: loadCachedData
  } = usePatrimoineCache();

  const {
    snapshots,
    loading: evolutionLoading,
    upsertSnapshot,
    deleteSnapshot,
    formatMontant: formatMontantEvolution,
    hasData: hasEvolutionData
  } = usePatrimoineEvolutionSimple();

  const [matrixData, setMatrixData] = useState<MatrixData>({
    clients: [],
    fournisseurs: [],
    matrix: new Map(),
    grandTotal: 0
  });
  const [editingCell, setEditingCell] = useState<{ clientId: string; fournisseurId: string } | null>(null);
  const [editingValue, setEditingValue] = useState<string>('');
  const [showFournisseurManager, setShowFournisseurManager] = useState(false);
  const [newFournisseurName, setNewFournisseurName] = useState('');
  const [editingFournisseur, setEditingFournisseur] = useState<{ id: string; nom: string } | null>(null);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);

  // États pour l'évolution temporelle
  const [editingSnapshot, setEditingSnapshot] = useState<{ index: number; date: string; total: string; commentaire: string } | null>(null);
  const [editingField, setEditingField] = useState<'date' | 'total' | null>(null);
  const [showEvolutionTable, setShowEvolutionTable] = useState(false);

  // Construire la matrice à partir des données du hook (optimisé avec cache)
  const buildMatrix = useCallback(async () => {
    try {
      // 1. Utiliser les fournisseurs du cache si disponibles
      let fournisseursData = cachedFournisseurs;

      // Si pas de fournisseurs en cache, charger depuis la base
      if (!fournisseursData || fournisseursData.length === 0) {
        console.log('🔄 Chargement fournisseurs depuis la base...');
        await loadCachedData(false); // Utilise le cache si valide
        fournisseursData = cachedFournisseurs;
      }

      // 2. Construire la matrice
      const matrix = new Map<string, Map<string, PatrimoineMatrixEntry>>();
      const clientTotals = getClientTotals();
      const fournisseurTotals = getFournisseurTotals();
      const grandTotal = getGrandTotal();

      // Initialiser la matrice pour tous les clients
      clients.forEach(client => {
        matrix.set(client.id, new Map());
      });

      // Remplir la matrice avec les données existantes
      entries.forEach(entry => {
        const clientMap = matrix.get(entry.client_id);
        if (clientMap) {
          // Trouver le fournisseur correspondant
          const fournisseur = fournisseursData?.find(f => f.nom === entry.fournisseur);
          if (fournisseur) {
            const matrixEntry: PatrimoineMatrixEntry = {
              clientId: entry.client_id,
              clientName: clients.find(c => c.id === entry.client_id)?.name || 'Client inconnu',
              fournisseurId: fournisseur.id,
              fournisseurNom: fournisseur.nom,
              montant: entry.montant,
              entryId: entry.id
            };

            clientMap.set(fournisseur.id, matrixEntry);
          }
        }
      });

      // 3. Préparer les données finales
      const clientsWithTotals = clients.map(client => ({
        id: client.id,
        name: client.name,
        total: clientTotals.get(client.id) || 0
      }));

      const fournisseursWithTotals = (fournisseursData || []).map(fournisseur => ({
        id: fournisseur.id,
        nom: fournisseur.nom,
        total: fournisseurTotals.get(fournisseur.nom) || 0
      }));

      setMatrixData({
        clients: clientsWithTotals,
        fournisseurs: fournisseursWithTotals,
        matrix,
        grandTotal
      });

    } catch (error) {
      console.error('Erreur lors de la construction de la matrice:', error);
      toast.error('Erreur lors du chargement des données');
    }
  }, [cachedFournisseurs, loadCachedData, entries, clients, getClientTotals, getFournisseurTotals, getGrandTotal]);

  // Formater les montants (optimisé avec useMemo)
  const formatMontant = useMemo(() => {
    const formatter = new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    });

    return (montant: number) => {
      if (montant === 0) return '-';
      return formatter.format(montant);
    };
  }, []);

  // Obtenir la valeur d'une cellule
  const getCellValue = (clientId: string, fournisseurId: string): number => {
    return matrixData.matrix.get(clientId)?.get(fournisseurId)?.montant || 0;
  };

  // Gérer l'édition d'une cellule
  const handleCellEdit = (clientId: string, fournisseurId: string, currentValue: number) => {
    setEditingCell({ clientId, fournisseurId });
    // Si la valeur est 0, on affiche un champ vide pour gagner du temps
    setEditingValue(currentValue === 0 ? '' : currentValue.toString());
  };

  // Sauvegarder l'édition
  const handleSaveEdit = async () => {
    if (!editingCell) return;

    const newValue = parseFloat(editingValue) || 0;
    const fournisseurNom = matrixData.fournisseurs.find(f => f.id === editingCell.fournisseurId)?.nom;

    if (fournisseurNom) {
      const success = await upsertPatrimoineEntry(
        editingCell.clientId,
        fournisseurNom,
        newValue
      );

      if (success) {
        // Reconstruire la matrice avec les nouvelles données
        await buildMatrix();
      }
    }

    setEditingCell(null);
    setEditingValue('');
  };

  // Annuler l'édition
  const handleCancelEdit = () => {
    setEditingCell(null);
    setEditingValue('');
  };

  // Ajouter un nouveau fournisseur
  const handleAddFournisseur = async () => {
    if (!newFournisseurName.trim()) return;

    try {
      // Obtenir le prochain ordre d'affichage
      const { data: existingFournisseurs } = await supabase
        .from('patrimoine_fournisseurs')
        .select('ordre_affichage')
        .order('ordre_affichage', { ascending: false })
        .limit(1);

      const nextOrder = (existingFournisseurs?.[0]?.ordre_affichage || 0) + 1;

      const { error } = await supabase
        .from('patrimoine_fournisseurs')
        .insert({
          nom: newFournisseurName.trim(),
          ordre_affichage: nextOrder
        });

      if (error) throw error;

      toast.success('Fournisseur ajouté');
      setNewFournisseurName('');
      await buildMatrix(); // Reconstruire la matrice

    } catch (error) {
      console.error('Erreur lors de l\'ajout du fournisseur:', error);
      toast.error('Erreur lors de l\'ajout');
    }
  };

  // Supprimer un fournisseur
  const handleDeleteFournisseur = async (fournisseurId: string, fournisseurNom: string) => {
    if (!confirm(`Êtes-vous sûr de vouloir supprimer le fournisseur "${fournisseurNom}" ?\n\nToutes les entrées de patrimoine associées seront également supprimées.`)) {
      return;
    }

    try {
      // Supprimer d'abord toutes les entrées de patrimoine associées
      const { error: patrimoineError } = await supabase
        .from('client_patrimoine')
        .delete()
        .eq('fournisseur', fournisseurNom);

      if (patrimoineError) throw patrimoineError;

      // Puis supprimer le fournisseur
      const { error: fournisseurError } = await supabase
        .from('patrimoine_fournisseurs')
        .delete()
        .eq('id', fournisseurId);

      if (fournisseurError) throw fournisseurError;

      toast.success('Fournisseur supprimé');
      await loadAllPatrimoine(); // Recharger toutes les données
      await buildMatrix();

    } catch (error) {
      console.error('Erreur lors de la suppression du fournisseur:', error);
      toast.error('Erreur lors de la suppression');
    }
  };

  // Renommer un fournisseur
  const handleRenameFournisseur = async (fournisseurId: string, oldName: string, newName: string) => {
    if (!newName.trim() || newName === oldName) return;

    try {
      // Mettre à jour le nom du fournisseur
      const { error: fournisseurError } = await supabase
        .from('patrimoine_fournisseurs')
        .update({ nom: newName.trim() })
        .eq('id', fournisseurId);

      if (fournisseurError) throw fournisseurError;

      // Mettre à jour toutes les entrées de patrimoine associées
      const { error: patrimoineError } = await supabase
        .from('client_patrimoine')
        .update({ fournisseur: newName.trim() })
        .eq('fournisseur', oldName);

      if (patrimoineError) throw patrimoineError;

      toast.success('Fournisseur renommé');
      setEditingFournisseur(null);
      await loadAllPatrimoine(); // Recharger toutes les données
      await buildMatrix();

    } catch (error) {
      console.error('Erreur lors du renommage du fournisseur:', error);
      toast.error('Erreur lors du renommage');
    }
  };

  // Fonctions pour gérer les snapshots d'évolution
  const handleAddSnapshot = async () => {
    if (!newSnapshotDate || !newSnapshotTotal) {
      toast.error('Veuillez remplir la date et le total');
      return;
    }

    const total = parseFloat(newSnapshotTotal);
    if (isNaN(total)) {
      toast.error('Le total doit être un nombre valide');
      return;
    }

    const success = await upsertSnapshot(
      newSnapshotDate,
      total,
      newSnapshotCommentaire || undefined
    );

    if (success) {
      setNewSnapshotDate('');
      setNewSnapshotTotal('');
      setNewSnapshotCommentaire('');
    }
  };

  const handleEditSnapshot = (snapshot: any) => {
    setEditingSnapshot({
      date: snapshot.date,
      total: snapshot.total.toString(),
      commentaire: snapshot.commentaire || ''
    });
  };

  const handleSaveSnapshotEdit = async () => {
    if (!editingSnapshot) return;

    const total = parseFloat(editingSnapshot.total);
    if (isNaN(total) || total < 0) {
      toast.error('Le total doit être un nombre positif valide');
      return;
    }

    if (!editingSnapshot.date) {
      toast.error('Veuillez sélectionner une date');
      return;
    }

    const success = await upsertSnapshot(
      editingSnapshot.date,
      total,
      editingSnapshot.commentaire || undefined
    );

    if (success) {
      setEditingSnapshot(null);
      setEditingField(null);
    }
  };

  const handleDeleteSnapshot = async (snapshotId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer ce snapshot ?')) return;

    await deleteSnapshot(snapshotId);
  };

  // Charger les données au montage et quand la dialog s'ouvre
  useEffect(() => {
    if (isOpen) {
      loadAllPatrimoine();
    }
  }, [isOpen, loadAllPatrimoine]);

  // Reconstruire la matrice quand les données changent
  useEffect(() => {
    if (entries.length >= 0 && clients.length > 0) {
      buildMatrix();
    }
  }, [entries, clients]);

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className={`${isFullscreen ? 'max-w-[99vw] max-h-[99vh] w-[99vw] h-[99vh]' : 'max-w-7xl max-h-[90vh]'} overflow-hidden transition-all duration-300`}>
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              Tableau Global du Patrimoine
            </DialogTitle>
          </div>
          <DialogDescription>
            Vue d'ensemble de la répartition du patrimoine par client et fournisseur avec totaux consolidés.
          </DialogDescription>
          <div className="flex items-center justify-between mt-4">
            <div className="flex items-center gap-3">
              <Button
                onClick={() => setShowAnalytics(true)}
                variant="outline"
                size="sm"
                className="border-purple-200 text-purple-700 hover:bg-purple-50 shadow-sm"
              >
                <BarChart3 className="h-4 w-4 mr-2" />
                Analyse Approfondie
              </Button>
              <Button
                onClick={() => setShowFournisseurManager(true)}
                variant="outline"
                size="sm"
                className="border-blue-200 text-blue-700 hover:bg-blue-50 shadow-sm"
              >
                <Settings className="h-4 w-4 mr-2" />
                Gérer Fournisseurs
              </Button>
            </div>
            <div className="flex items-center gap-3">
              <Button
                onClick={() => setIsFullscreen(!isFullscreen)}
                variant="outline"
                size="sm"
                className="border-gray-200 text-gray-700 hover:bg-gray-50 shadow-sm"
              >
                {isFullscreen ? (
                  <>
                    <Minimize2 className="h-4 w-4 mr-2" />
                    Réduire
                  </>
                ) : (
                  <>
                    <Maximize2 className="h-4 w-4 mr-2" />
                    Plein Écran
                  </>
                )}
              </Button>
              <Badge variant="secondary" className="text-xl px-4 py-2 bg-gradient-to-r from-green-100 to-green-200 text-green-800 font-bold shadow-sm border border-green-300">
                <Euro className="h-5 w-5 mr-2" />
                Total: {formatMontant(matrixData.grandTotal)}
              </Badge>
            </div>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Chargement du patrimoine global...</p>
            </div>
          </div>
        ) : (
          <div className={`overflow-auto ${isFullscreen ? 'max-h-[90vh]' : 'max-h-[70vh]'} transition-all duration-300`}>
            <Table className="border-2 border-gray-300 rounded-lg overflow-hidden shadow-sm">
              <TableHeader>
                <TableRow className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b-2 border-blue-200">
                  <TableHead className="font-bold text-gray-800 py-3 px-3 min-w-[180px] sticky left-0 bg-blue-50 z-10 border-r-2 border-blue-300">
                    <div className="flex items-center gap-2">
                      <Building2 className="h-4 w-4 text-blue-600" />
                      <span className="text-sm">Clients</span>
                    </div>
                  </TableHead>
                  {matrixData.fournisseurs.map((fournisseur, index) => (
                    <TableHead key={fournisseur.id} className="text-center py-3 px-2 min-w-[120px] border-r border-gray-300">
                      <span className="font-bold text-gray-800 text-xs">{fournisseur.nom}</span>
                    </TableHead>
                  ))}
                  <TableHead className="text-center py-3 px-3 min-w-[130px] bg-gradient-to-r from-green-50 to-green-100 border-l-2 border-green-400">
                    <div className="flex items-center justify-center gap-1">
                      <Euro className="h-4 w-4 text-green-600" />
                      <span className="font-bold text-green-800 text-sm">Total Client</span>
                    </div>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {matrixData.clients.map((client, clientIndex) => (
                  <TableRow key={client.id} className={`hover:bg-blue-50/50 transition-colors border-b border-gray-200 ${clientIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50/50'}`}>
                    <TableCell className="font-bold py-2 px-3 sticky left-0 bg-white z-10 border-r-2 border-blue-300">
                      <span className="text-gray-900 text-xs">{client.name}</span>
                    </TableCell>
                    {matrixData.fournisseurs.map((fournisseur) => {
                      const cellValue = getCellValue(client.id, fournisseur.id);
                      const isEditing = editingCell?.clientId === client.id && editingCell?.fournisseurId === fournisseur.id;

                      return (
                        <TableCell key={fournisseur.id} className="text-center py-2 px-2 border-r border-gray-200">
                          {isEditing ? (
                            <Input
                              type="number"
                              value={editingValue}
                              onChange={(e) => setEditingValue(e.target.value)}
                              className="w-20 text-center text-xs border-blue-300 focus:border-blue-500"
                              autoFocus
                              onBlur={handleSaveEdit}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  handleSaveEdit();
                                }
                                if (e.key === 'Escape') {
                                  handleCancelEdit();
                                }
                              }}
                            />
                          ) : (
                            <span
                              className={`cursor-pointer px-2 py-1 rounded text-xs transition-all duration-200 ${
                                cellValue > 0
                                  ? 'text-green-700 hover:bg-green-100 font-bold bg-green-50 border border-green-200'
                                  : 'text-gray-400 hover:bg-gray-100 border border-gray-200'
                              }`}
                              onClick={() => handleCellEdit(client.id, fournisseur.id, cellValue)}
                            >
                              {formatMontant(cellValue)}
                            </span>
                          )}
                        </TableCell>
                      );
                    })}
                    <TableCell className="text-center py-2 px-3 bg-gradient-to-r from-green-50 to-green-100 font-bold text-green-800 border-l-2 border-green-400">
                      <div className="flex items-center justify-center gap-1">
                        <span className="text-sm">{formatMontant(client.total)}</span>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
                
                {/* Ligne des totaux */}
                <TableRow className="bg-gradient-to-r from-green-100 to-green-200 border-t-3 border-green-400">
                  <TableCell className="font-bold py-2 px-3 sticky left-0 bg-gradient-to-r from-green-100 to-green-200 z-10 border-r-2 border-green-400">
                    <div className="flex items-center gap-1">
                      <Calculator className="h-4 w-4 text-green-700" />
                      <span className="text-green-900 text-xs">TOTAL</span>
                    </div>
                  </TableCell>
                  {matrixData.fournisseurs.map((fournisseur) => (
                    <TableCell key={fournisseur.id} className="text-center py-2 px-2 font-bold text-green-800 border-r border-green-300">
                      <span className="text-xs bg-green-200 px-1 py-1 rounded">{formatMontant(fournisseur.total)}</span>
                    </TableCell>
                  ))}
                  <TableCell className="text-center py-2 px-3 font-bold text-green-900 bg-gradient-to-r from-green-200 to-green-300 border-l-3 border-green-500">
                    <div className="flex items-center justify-center gap-1">
                      <Euro className="h-4 w-4 text-green-700" />
                      <span className="text-sm">{formatMontant(matrixData.grandTotal)}</span>
                    </div>
                  </TableCell>
                </TableRow>


              </TableBody>
            </Table>
          </div>
        )}
      </DialogContent>

      {/* Petit élément discret pour l'évolution temporelle */}
      {isOpen && (
        <div className="mt-4 border-t border-gray-200 pt-3">
          <div className="flex items-center justify-between">
            <button
              onClick={() => setShowEvolutionTable(!showEvolutionTable)}
              className="flex items-center gap-2 text-xs text-blue-600 hover:text-blue-800 transition-colors"
            >
              {showEvolutionTable ? (
                <ChevronUp className="h-3 w-3" />
              ) : (
                <ChevronDown className="h-3 w-3" />
              )}
              <span>Évolution temporelle</span>
              {snapshots.length > 0 && (
                <span className="bg-blue-100 text-blue-700 px-1.5 py-0.5 rounded-full text-xs">
                  {snapshots.length}
                </span>
              )}
            </button>

            {showEvolutionTable && (
              <button
                onClick={async () => {
                  const today = new Date().toISOString().split('T')[0];
                  const success = await upsertSnapshot(today, matrixData.grandTotal);
                  if (success) {
                    toast.success("Total actuel figé dans l'évolution");
                  }
                }}
                className="flex items-center gap-1 text-xs text-green-600 hover:text-green-800 bg-green-50 hover:bg-green-100 px-2 py-1 rounded transition-colors"
              >
                <Save className="h-3 w-3" />
                <span>Figer total actuel</span>
              </button>
            )}
          </div>

          {/* Tableau d'évolution temporelle compact */}
          {showEvolutionTable && (
            <div className="mt-3 bg-blue-50/50 border border-blue-200 rounded-lg overflow-hidden">
              <div className="bg-blue-100 px-3 py-2 border-b border-blue-200">
                <h4 className="text-xs font-medium text-blue-800">Points d'évolution du patrimoine</h4>
              </div>

              <div className="p-3 space-y-2">
                {Array.from({ length: 4 }, (_, index) => {
                  const snapshot = snapshots[index];
                  return (
                    <div key={index} className="grid grid-cols-12 gap-2 items-center py-1">
                      {/* Date */}
                      <div className="col-span-3">
                        {editingSnapshot && editingField === 'date' && editingSnapshot.index === index ? (
                          <Input
                            type="date"
                            value={editingSnapshot.date}
                            onChange={(e) => setEditingSnapshot({ ...editingSnapshot, date: e.target.value })}
                            className="w-full text-xs border-blue-300 focus:border-blue-500"
                            autoFocus
                            onBlur={() => {
                              handleSaveSnapshotEdit();
                              setEditingField(null);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleSaveSnapshotEdit();
                                setEditingField(null);
                              }
                              if (e.key === 'Escape') {
                                setEditingSnapshot(null);
                                setEditingField(null);
                              }
                            }}
                          />
                        ) : (
                          <div
                            className="text-xs text-blue-700 cursor-pointer hover:bg-blue-100 px-2 py-1 rounded"
                            onClick={() => {
                              if (snapshot) {
                                setEditingSnapshot({
                                  index,
                                  date: snapshot.date,
                                  total: snapshot.total.toString(),
                                  commentaire: snapshot.commentaire || ''
                                });
                                setEditingField('date');
                              } else {
                                const today = new Date().toISOString().split('T')[0];
                                setEditingSnapshot({ index, date: today, total: '0', commentaire: '' });
                                setEditingField('date');
                              }
                            }}
                          >
                            {snapshot ? new Date(snapshot.date).toLocaleDateString('fr-FR') : 'Cliquer pour date'}
                          </div>
                        )}
                      </div>

                      {/* Colonnes fournisseurs */}
                      {matrixData.fournisseurs.map((fournisseur, fIndex) => (
                        <div key={fournisseur.id} className="col-span-1 text-center">
                          <div
                            className="text-xs text-gray-500 cursor-pointer hover:bg-blue-100 px-1 py-1 rounded"
                            onDoubleClick={() => {
                              toast.info(`Édition ${fournisseur.nom} - À implémenter`);
                            }}
                          >
                            -
                          </div>
                        </div>
                      ))}

                      {/* Total */}
                      <div className="col-span-2">
                        {editingSnapshot && editingField === 'total' && editingSnapshot.index === index ? (
                          <Input
                            type="number"
                            value={editingSnapshot.total}
                            onChange={(e) => setEditingSnapshot({ ...editingSnapshot, total: e.target.value })}
                            className="w-full text-xs border-blue-300 focus:border-blue-500"
                            autoFocus
                            onBlur={() => {
                              handleSaveSnapshotEdit();
                              setEditingField(null);
                            }}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                handleSaveSnapshotEdit();
                                setEditingField(null);
                              }
                              if (e.key === 'Escape') {
                                setEditingSnapshot(null);
                                setEditingField(null);
                              }
                            }}
                          />
                        ) : (
                          <div
                            className="text-xs font-medium text-blue-700 cursor-pointer hover:bg-blue-100 px-2 py-1 rounded text-center"
                            onDoubleClick={() => {
                              if (snapshot) {
                                setEditingSnapshot({
                                  index,
                                  date: snapshot.date,
                                  total: snapshot.total.toString(),
                                  commentaire: snapshot.commentaire || ''
                                });
                                setEditingField('total');
                              } else {
                                const today = new Date().toISOString().split('T')[0];
                                setEditingSnapshot({ index, date: today, total: '0', commentaire: '' });
                                setEditingField('total');
                              }
                            }}
                          >
                            {snapshot ? formatMontantEvolution(snapshot.total) : 'Double-clic'}
                          </div>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="col-span-1 text-center">
                        {snapshot && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteSnapshot(snapshot.id)}
                            className="text-red-500 hover:bg-red-50 h-6 w-6 p-0"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  );
                })}

                {/* En-têtes des colonnes */}
                <div className="grid grid-cols-12 gap-2 items-center py-1 border-t border-blue-200 pt-2 mt-2">
                  <div className="col-span-3 text-xs font-medium text-blue-600">Date</div>
                  {matrixData.fournisseurs.map((fournisseur) => (
                    <div key={fournisseur.id} className="col-span-1 text-xs font-medium text-blue-600 text-center truncate" title={fournisseur.nom}>
                      {fournisseur.nom.substring(0, 3)}
                    </div>
                  ))}
                  <div className="col-span-2 text-xs font-medium text-blue-600 text-center">Total</div>
                  <div className="col-span-1"></div>
                </div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Dialog de gestion des fournisseurs */}
      <Dialog open={showFournisseurManager} onOpenChange={setShowFournisseurManager}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-blue-600" />
              Gestion des Fournisseurs
            </DialogTitle>
            <DialogDescription>
              Gérer la liste des fournisseurs de patrimoine : ajouter, modifier ou supprimer des fournisseurs.
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Ajouter un nouveau fournisseur */}
            <div className="flex gap-3">
              <Input
                placeholder="Nom du nouveau fournisseur..."
                value={newFournisseurName}
                onChange={(e) => setNewFournisseurName(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleAddFournisseur()}
                className="flex-1"
              />
              <Button
                onClick={handleAddFournisseur}
                disabled={!newFournisseurName.trim()}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Ajouter
              </Button>
            </div>

            {/* Liste des fournisseurs existants */}
            <div className="space-y-2 max-h-96 overflow-y-auto">
              <h3 className="font-medium text-gray-700 mb-3">Fournisseurs existants</h3>
              {matrixData.fournisseurs.map((fournisseur) => (
                <div key={fournisseur.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    {editingFournisseur?.id === fournisseur.id ? (
                      <Input
                        value={editingFournisseur.nom}
                        onChange={(e) => setEditingFournisseur({ ...editingFournisseur, nom: e.target.value })}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            handleRenameFournisseur(fournisseur.id, fournisseur.nom, editingFournisseur.nom);
                          }
                          if (e.key === 'Escape') {
                            setEditingFournisseur(null);
                          }
                        }}
                        onBlur={() => handleRenameFournisseur(fournisseur.id, fournisseur.nom, editingFournisseur.nom)}
                        className="w-48"
                        autoFocus
                      />
                    ) : (
                      <span
                        className="font-medium cursor-pointer hover:text-blue-600"
                        onClick={() => setEditingFournisseur({ id: fournisseur.id, nom: fournisseur.nom })}
                      >
                        {fournisseur.nom}
                      </span>
                    )}
                    <Badge variant="outline" className="bg-green-50 text-green-700">
                      {formatMontant(fournisseur.total)}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setEditingFournisseur({ id: fournisseur.id, nom: fournisseur.nom })}
                      className="text-blue-600 hover:bg-blue-50"
                    >
                      <Edit3 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleDeleteFournisseur(fournisseur.id, fournisseur.nom)}
                      className="text-red-600 hover:bg-red-50"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Dialog d'analyse approfondie */}
      <PatrimoineAnalytics
        isOpen={showAnalytics}
        onClose={() => setShowAnalytics(false)}
      />
    </Dialog>
  );
};
