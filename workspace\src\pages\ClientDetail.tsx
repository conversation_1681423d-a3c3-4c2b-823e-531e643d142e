
import { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useClientContext } from '@/contexts/ClientContext';
import { StatusSelect } from '@/components/StatusSelect';
import { ProgressBar } from '@/components/ProgressBar';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AutoResizeTextarea } from '@/components/ui/auto-resize-textarea';
import { Logo } from '@/components/Logo';

import {
  Table, TableBody, TableCell, TableHead, TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  AlertDialog, AlertDialogAction, AlertDialogCancel,
  AlertDialogContent, AlertDialogDescription, AlertDialogFooter,
  AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger
} from '@/components/ui/alert-dialog';
import {
  DropdownMenu, DropdownMenuTrigger, DropdownMenuContent,
  DropdownMenuItem, DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import {
  ArrowLeft, Copy, Loader2, MoreVertical, Plus, Save, Trash2, CheckCircle, RotateCcw
} from 'lucide-react';
import {
  Dialog, DialogContent, DialogFooter, DialogHeader,
  DialogTitle, DialogTrigger, DialogDescription
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AssigneeSelect } from '@/components/AssigneeSelect';
import { StyledCommentBox } from "@/components/StyledCommentBox";
import { cn } from "@/lib/utils";
import { EditableStepName } from '@/components/EditableStepName';
import { DueDateDisplay } from '@/components/DueDateDisplay';
import { ComplianceTab } from '@/components/ComplianceTab';
import { FreeNotesTab } from '@/components/FreeNotesTab';
import { PatrimoineTab } from '@/components/PatrimoineTab';
import { toast } from 'sonner';

const ClientDetail = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const {
    getClient, updateClientName, updateClientDueDate, addStep, deleteStep,
    updateStepStatus, updateStepDate, updateStepComment,
    duplicateClientTemplate, deleteClient, getClientProgress,
    loading, updateStepName, toggleClientCompleted
  } = useClientContext();

  const client = getClient(id || '');
  const [clientName, setClientName] = useState(client?.name || '');
  const [isEditingName, setIsEditingName] = useState(false);
  const [newStepName, setNewStepName] = useState('');
  const [isAddingStep, setIsAddingStep] = useState(false);
  const [isSavingStep, setIsSavingStep] = useState(false);
  const [isSavingName, setIsSavingName] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isTogglingCompleted, setIsTogglingCompleted] = useState(false);

  if (loading) {
    return (
      <div className="container mx-auto py-16 px-4 flex flex-col items-center justify-center">
        <Loader2 className="h-10 w-10 text-blue-600 animate-spin mb-4" />
        <p className="text-lg text-gray-600">Chargement des données...</p>
      </div>
    );
  }

  if (!client) {
    return (
      <div className="container mx-auto py-8 px-4 text-center">
        <div className="bg-red-50 rounded-lg p-6 max-w-md mx-auto border border-red-200">
          <h2 className="text-2xl font-bold mb-4 text-red-600">Client non trouvé</h2>
          <p className="text-gray-600 mb-6">Le client que vous recherchez n'existe pas ou a été supprimé.</p>
          <Button onClick={() => navigate('/')} variant="outline" className="border-red-300">
            <ArrowLeft className="mr-2" size={16} />
            Retourner à l'accueil
          </Button>
        </div>
      </div>
    );
  }

  const progress = getClientProgress(client);

  const handleSaveName = async () => {
    if (clientName.trim() && clientName !== client.name) {
      setIsSavingName(true);
      await updateClientName(client.id, clientName);
      setIsSavingName(false);
    } else {
      setClientName(client.name);
    }
    setIsEditingName(false);
  };

  const handleAddStep = async () => {
    if (newStepName.trim()) {
      setIsSavingStep(true);
      await addStep(client.id, newStepName.trim());
      setIsSavingStep(false);
      setNewStepName('');
      setIsAddingStep(false);
    }
  };

  const handleDeleteClient = async () => {
    setIsDeleting(true);
    await deleteClient(client.id);
    setIsDeleting(false);
    navigate('/');
  };

  const handleUpdateStepName = async (stepId: string, newName: string) => {
    try {
      await updateStepName(client.id, stepId, newName);
    } catch (error) {
      console.error('Error updating step name:', error);
      toast("Impossible de mettre à jour le nom de l'étape");
    }
  };

  const handleToggleCompleted = async () => {
    if (!client) return;

    setIsTogglingCompleted(true);
    try {
      await toggleClientCompleted(client.id);
      toast(client.completed ? "Client marqué comme en cours" : "Client marqué comme terminé");
      navigate('/'); // Retourner à la liste après changement de statut
    } catch (error) {
      console.error('Error toggling client completion:', error);
      toast("Impossible de modifier le statut du client");
    } finally {
      setIsTogglingCompleted(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl relative">
      <div className="absolute top-4 right-4 flex items-center gap-3">
        <Logo size="sm" className="opacity-80 hover:opacity-100 transition-opacity" />
      </div>

      <div className="mb-8">
        <Button
          variant="ghost"
          className="mb-4 hover:bg-blue-50 text-blue-600"
          onClick={() => navigate('/')}
        >
          <ArrowLeft className="mr-2" size={16} />
          Retour à la liste
        </Button>

        <div className="flex justify-between items-center flex-wrap gap-4 responsive-header">
          <div className="flex items-center gap-6 flex-1 min-w-0">
            <div className="flex-1 min-w-0">
              {isEditingName ? (
                <div className="flex items-center gap-2">
                  <Input
                    value={clientName}
                    onChange={(e) => setClientName(e.target.value)}
                    className="text-xl font-bold border-blue-300 focus:border-blue-500 max-w-xs"
                    autoFocus
                  />
                  <Button
                    size="sm"
                    onClick={handleSaveName}
                    disabled={isSavingName}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {isSavingName ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Save size={16} />
                    )}
                  </Button>
                </div>
              ) : (
                <h1
                  className="text-2xl sm:text-3xl font-bold cursor-pointer hover:text-blue-600 transition-colors flex items-center gap-2"
                  onClick={() => setIsEditingName(true)}
                >
                  <span className="truncate">{client.name}</span>
                  <span className="text-sm text-gray-400 font-normal shrink-0">(cliquer pour modifier)</span>
                </h1>
              )}
            </div>

            {/* Date de rendu compacte à droite du nom */}
            {!isEditingName && (
              <div className="shrink-0 flex items-center gap-2">
                <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <span className="text-sm font-medium text-gray-700">Date de rendu</span>
                <Input
                  type="date"
                  id={`due-date-${client.id}`}
                  name={`due-date-${client.id}`}
                  value={client.dueDate || ''}
                  onChange={(e) => updateClientDueDate(client.id, e.target.value || null)}
                  className="w-40 h-8 text-sm border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                  placeholder="Sélectionner une date"
                />
              </div>
            )}
          </div>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="icon">
                <MoreVertical size={16} />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => duplicateClientTemplate(client.id)}>
                <Copy size={16} className="mr-2" />
                Dupliquer le client
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={handleToggleCompleted}
                disabled={isTogglingCompleted}
                className={client.completed ? "text-orange-600 focus:text-orange-600" : "text-green-600 focus:text-green-600"}
              >
                {isTogglingCompleted ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : client.completed ? (
                  <RotateCcw size={16} className="mr-2" />
                ) : (
                  <CheckCircle size={16} className="mr-2" />
                )}
                {client.completed ? "Marquer comme en cours" : "Marquer comme terminé"}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <DropdownMenuItem
                    className="text-red-500 focus:text-red-500"
                    onSelect={(e) => e.preventDefault()}
                  >
                    <Trash2 size={16} className="mr-2" />
                    Supprimer le client
                  </DropdownMenuItem>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Êtes-vous sûr ?</AlertDialogTitle>
                    <AlertDialogDescription>
                      Cette action ne peut pas être annulée. Cela supprimera définitivement
                      {' '}{client.name} et toutes ses données.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Annuler</AlertDialogCancel>
                    <AlertDialogAction
                      className="bg-red-500 hover:bg-red-600"
                      onClick={handleDeleteClient}
                      disabled={isDeleting}
                    >
                      {isDeleting ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Suppression...
                        </>
                      ) : (
                        <>Supprimer</>
                      )}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <ProgressBar
          completed={progress.completed}
          total={progress.total}
          className="mt-4 max-w-md"
        />


      </div>

      {/* Système d'onglets */}
      <Tabs defaultValue="notes" className="w-full">
        <TabsList className="grid w-full grid-cols-4 mb-6">
          <TabsTrigger value="notes" className="flex items-center gap-2">
            Notes rapides
          </TabsTrigger>
          <TabsTrigger value="compliance" className="flex items-center gap-2">
            Conformité
            <span className="bg-green-100 text-green-800 text-xs font-medium px-2 py-0.5 rounded-full">
              3
            </span>
          </TabsTrigger>
          <TabsTrigger value="encours" className="flex items-center gap-2">
            Encours
          </TabsTrigger>
          <TabsTrigger value="steps" className="flex items-center gap-2">
            Étapes / Documents
            {client.steps.length > 0 && (
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-0.5 rounded-full">
                {client.steps.length}
              </span>
            )}
          </TabsTrigger>
        </TabsList>

        {/* Onglet Notes rapides */}
        <TabsContent value="notes">
          <FreeNotesTab clientId={client.id} />
        </TabsContent>

        {/* Onglet Conformité */}
        <TabsContent value="compliance">
          <ComplianceTab client={client} />
        </TabsContent>

        {/* Onglet Encours */}
        <TabsContent value="encours">
          <PatrimoineTab client={client} />
        </TabsContent>

        {/* Onglet Documents/Étapes */}
        <TabsContent value="steps">
          <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-xl border shadow-lg overflow-hidden responsive-table-container">
        <Table className="responsive-table">
          <TableHeader className="bg-gray-50/70 backdrop-blur-sm sticky top-0 z-10">
            <TableRow>
              <TableHead className="w-[280px] font-medium">Document / Étape</TableHead>
              <TableHead className="w-[140px] font-medium">Statut</TableHead>
              <TableHead className="w-[160px] font-medium hide-on-mobile">Date</TableHead>
              <TableHead className="font-medium">Commentaire</TableHead>
              <TableHead className="w-[160px] font-medium hide-on-mobile">Personnes</TableHead>
              <TableHead className="w-[48px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {client.steps.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="h-24 text-center">
                  <div className="flex flex-col items-center py-6 text-gray-500">
                    <p className="mb-2">Aucune étape définie</p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsAddingStep(true)}
                      className="border-blue-300 text-blue-600 hover:bg-blue-50"
                    >
                      <Plus size={14} className="mr-1" />
                      Ajouter une étape
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ) : (
              client.steps.map(step => (
                <TableRow
                  key={step.id}
                  className={cn(
                    "transition-colors hover:bg-gray-50/80",
                    step.status === 'urgent' ? 'bg-pink-50/70 hover:bg-pink-50/90' : ''
                  )}
                >
                  <TableCell>
                    <EditableStepName
                      name={step.name}
                      onSave={(newName) => handleUpdateStepName(step.id, newName)}
                    />
                  </TableCell>
                  <TableCell>
                    <StatusSelect
                      value={step.status}
                      onChange={(status) => updateStepStatus(client.id, step.id, status)}
                    />
                  </TableCell>
                  <TableCell>
                    <Input
                      type="date"
                      id={`step-date-${step.id}`}
                      name={`step-date-${step.id}`}
                      value={step.receivedDate || ''}
                      onChange={(e) => {
                        console.log('Nouvelle date sélectionnée:', e.target.value);
                        updateStepDate(client.id, step.id, e.target.value || null);
                      }}
                      onBlur={(e) => {
                        // Vérifier à nouveau lors de la perte de focus
                        if (e.target.value && e.target.value !== step.receivedDate) {
                          console.log('Vérification de la date au blur:', e.target.value);
                          updateStepDate(client.id, step.id, e.target.value);
                        }
                      }}
                      className="border-gray-200 focus:border-blue-500 transition-colors h-9"
                    />
                  </TableCell>
                  <TableCell className="py-2">
                    <StyledCommentBox
                      value={step.comment}
                      onChange={(e) => updateStepComment(client.id, step.id, e.target.value)}
                      placeholder="Ajouter un commentaire..."
                    />
                  </TableCell>
                  <TableCell>
                    <AssigneeSelect
                      clientId={client.id}
                      stepId={step.id}
                      currentAssignees={step.assignees}
                    />
                  </TableCell>
                  <TableCell className="group/cell">
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="opacity-0 group-hover/cell:opacity-100 text-red-500 hover:text-red-600 hover:bg-red-50"
                        >
                          <Trash2 size={16} />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Supprimer cette étape ?</AlertDialogTitle>
                          <AlertDialogDescription>
                            Cette action supprimera définitivement l'étape "{step.name}".
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Annuler</AlertDialogCancel>
                          <AlertDialogAction
                            className="bg-red-500 hover:bg-red-600"
                            onClick={() => deleteStep(client.id, step.id)}
                          >
                            Supprimer
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
          </Table>
          </div>

          <Dialog open={isAddingStep} onOpenChange={setIsAddingStep}>
            <DialogTrigger asChild>
              <Button
                className="mt-6 bg-blue-600 hover:bg-blue-700"
              >
                <Plus size={16} className="mr-2" />
                Ajouter une étape
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Ajouter une nouvelle étape</DialogTitle>
                <DialogDescription>
                  Saisissez le nom du document ou de l'étape à ajouter.
                </DialogDescription>
              </DialogHeader>
              <div className="py-4">
                <Input
                  placeholder="Nom de l'étape"
                  value={newStepName}
                  onChange={(e) => setNewStepName(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleAddStep()}
                  autoFocus
                  className="border-gray-300 focus:border-blue-500"
                />
              </div>
              <DialogFooter>
                <Button
                  onClick={handleAddStep}
                  disabled={!newStepName.trim() || isSavingStep}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  {isSavingStep ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Ajout en cours...
                    </>
                  ) : (
                    <>Ajouter</>
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ClientDetail;
