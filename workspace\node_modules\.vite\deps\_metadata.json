{"hash": "91d84ae7", "configHash": "77f8a11e", "lockfileHash": "7bcb1b68", "browserHash": "06ce94db", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "c9aa0c24", "needsInterop": true}, "@dnd-kit/core": {"src": "../../@dnd-kit/core/dist/core.esm.js", "file": "@dnd-kit_core.js", "fileHash": "8d6d21f3", "needsInterop": false}, "@dnd-kit/sortable": {"src": "../../@dnd-kit/sortable/dist/sortable.esm.js", "file": "@dnd-kit_sortable.js", "fileHash": "6caa4419", "needsInterop": false}, "@dnd-kit/utilities": {"src": "../../@dnd-kit/utilities/dist/utilities.esm.js", "file": "@dnd-kit_utilities.js", "fileHash": "376ec474", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "9dfbb0c6", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "52b207b8", "needsInterop": false}, "@radix-ui/react-context-menu": {"src": "../../@radix-ui/react-context-menu/dist/index.mjs", "file": "@radix-ui_react-context-menu.js", "fileHash": "fce26eca", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "b847c815", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "31106b3e", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "57067b1b", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "29a12051", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "3dcf7e5c", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "ca4da985", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "4b563e2c", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "833a11c7", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "c12d78ed", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "3653d17c", "needsInterop": false}, "@tauri-apps/api/window": {"src": "../../@tauri-apps/api/window.js", "file": "@tauri-apps_api_window.js", "fileHash": "dd797dcd", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "83b695c8", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "78390962", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.mjs", "file": "date-fns.js", "fileHash": "3c09799d", "needsInterop": false}, "date-fns/locale": {"src": "../../date-fns/locale.mjs", "file": "date-fns_locale.js", "fileHash": "72a79b25", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "57440178", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "17eba1c4", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "29b350fe", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "97484f98", "needsInterop": true}, "react-mentions": {"src": "../../react-mentions/dist/react-mentions.esm.js", "file": "react-mentions.js", "fileHash": "35707bb4", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "06bb0feb", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "88fb1624", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "40fde810", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "5887b157", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "44c56d81", "needsInterop": false}}, "chunks": {"browser-MBMWUMDU": {"file": "browser-MBMWUMDU.js"}, "browser-5FUWBSZQ": {"file": "browser-5FUWBSZQ.js"}, "chunk-NHY3NUFE": {"file": "chunk-NHY3NUFE.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-A55GUTZU": {"file": "chunk-A55GUTZU.js"}, "chunk-HWMC2YUY": {"file": "chunk-HWMC2YUY.js"}, "chunk-UX7G4BSN": {"file": "chunk-UX7G4BSN.js"}, "chunk-525GK5KX": {"file": "chunk-525GK5KX.js"}, "chunk-CIYBTUL6": {"file": "chunk-CIYBTUL6.js"}, "chunk-HCAA6Y25": {"file": "chunk-HCAA6Y25.js"}, "chunk-BDXBUB4R": {"file": "chunk-BDXBUB4R.js"}, "chunk-V5HFRF4I": {"file": "chunk-V5HFRF4I.js"}, "chunk-AKD63LI2": {"file": "chunk-AKD63LI2.js"}, "chunk-SNOUU5QH": {"file": "chunk-SNOUU5QH.js"}, "chunk-4WI46J7R": {"file": "chunk-4WI46J7R.js"}, "chunk-W3GUYWEY": {"file": "chunk-W3GUYWEY.js"}, "chunk-JNAKKGOO": {"file": "chunk-JNAKKGOO.js"}, "chunk-RULIVCTP": {"file": "chunk-RULIVCTP.js"}, "chunk-5R4EQB64": {"file": "chunk-5R4EQB64.js"}, "chunk-7JWUO6O7": {"file": "chunk-7JWUO6O7.js"}, "chunk-5OZAACO2": {"file": "chunk-5OZAACO2.js"}, "chunk-X5NZ3XE2": {"file": "chunk-X5NZ3XE2.js"}, "chunk-CRNJR6QK": {"file": "chunk-CRNJR6QK.js"}, "chunk-W6L2VRDA": {"file": "chunk-W6L2VRDA.js"}, "chunk-ZMLY2J2T": {"file": "chunk-ZMLY2J2T.js"}, "chunk-4B2QHNJT": {"file": "chunk-4B2QHNJT.js"}}}