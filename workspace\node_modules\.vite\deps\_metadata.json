{"hash": "91d84ae7", "configHash": "77f8a11e", "lockfileHash": "7bcb1b68", "browserHash": "06ce94db", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "a43fa30b", "needsInterop": true}, "@dnd-kit/core": {"src": "../../@dnd-kit/core/dist/core.esm.js", "file": "@dnd-kit_core.js", "fileHash": "5fc3ac3b", "needsInterop": false}, "@dnd-kit/sortable": {"src": "../../@dnd-kit/sortable/dist/sortable.esm.js", "file": "@dnd-kit_sortable.js", "fileHash": "718c6720", "needsInterop": false}, "@dnd-kit/utilities": {"src": "../../@dnd-kit/utilities/dist/utilities.esm.js", "file": "@dnd-kit_utilities.js", "fileHash": "091d39de", "needsInterop": false}, "@radix-ui/react-alert-dialog": {"src": "../../@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "f43fbc15", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "4067cf52", "needsInterop": false}, "@radix-ui/react-context-menu": {"src": "../../@radix-ui/react-context-menu/dist/index.mjs", "file": "@radix-ui_react-context-menu.js", "fileHash": "57280f7a", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "b80a9e2b", "needsInterop": false}, "@radix-ui/react-dropdown-menu": {"src": "../../@radix-ui/react-dropdown-menu/dist/index.mjs", "file": "@radix-ui_react-dropdown-menu.js", "fileHash": "c8aa85d2", "needsInterop": false}, "@radix-ui/react-popover": {"src": "../../@radix-ui/react-popover/dist/index.mjs", "file": "@radix-ui_react-popover.js", "fileHash": "a5a3fdf1", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "055eabf7", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "0674ddd9", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "d51268b4", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "1f800a8c", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "60e630ba", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "f280b163", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "ade706b3", "needsInterop": false}, "@tauri-apps/api/window": {"src": "../../@tauri-apps/api/window.js", "file": "@tauri-apps_api_window.js", "fileHash": "f76b6755", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "cf9dd295", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "6a75f575", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.mjs", "file": "date-fns.js", "fileHash": "968ec748", "needsInterop": false}, "date-fns/locale": {"src": "../../date-fns/locale.mjs", "file": "date-fns_locale.js", "fileHash": "0947e76f", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "62e91a1a", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "c1f333fb", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "aa88a90c", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "e0a245f8", "needsInterop": true}, "react-mentions": {"src": "../../react-mentions/dist/react-mentions.esm.js", "file": "react-mentions.js", "fileHash": "f1b0a150", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "f1a0c55c", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "046d157e", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "8deef7ad", "needsInterop": false}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "742789e7", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "f1cff1c0", "needsInterop": false}}, "chunks": {"browser-MBMWUMDU": {"file": "browser-MBMWUMDU.js"}, "browser-5FUWBSZQ": {"file": "browser-5FUWBSZQ.js"}, "chunk-NHY3NUFE": {"file": "chunk-NHY3NUFE.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-IIGUYIAY": {"file": "chunk-IIGUYIAY.js"}, "chunk-DKCV5M44": {"file": "chunk-DKCV5M44.js"}, "chunk-HWMC2YUY": {"file": "chunk-HWMC2YUY.js"}, "chunk-525GK5KX": {"file": "chunk-525GK5KX.js"}, "chunk-CIYBTUL6": {"file": "chunk-CIYBTUL6.js"}, "chunk-4RFQAQH7": {"file": "chunk-4RFQAQH7.js"}, "chunk-ZNOYXXDP": {"file": "chunk-ZNOYXXDP.js"}, "chunk-V5HFRF4I": {"file": "chunk-V5HFRF4I.js"}, "chunk-GBOPLMYL": {"file": "chunk-GBOPLMYL.js"}, "chunk-WCGBNP2R": {"file": "chunk-WCGBNP2R.js"}, "chunk-P3D5FP5F": {"file": "chunk-P3D5FP5F.js"}, "chunk-MT5FQSCI": {"file": "chunk-MT5FQSCI.js"}, "chunk-WI3GJDGN": {"file": "chunk-WI3GJDGN.js"}, "chunk-FM3RJRAT": {"file": "chunk-FM3RJRAT.js"}, "chunk-P74YMTS5": {"file": "chunk-P74YMTS5.js"}, "chunk-JE5J4ZBJ": {"file": "chunk-JE5J4ZBJ.js"}, "chunk-RIL2ZJSN": {"file": "chunk-RIL2ZJSN.js"}, "chunk-X5NZ3XE2": {"file": "chunk-X5NZ3XE2.js"}, "chunk-CRNJR6QK": {"file": "chunk-CRNJR6QK.js"}, "chunk-W6L2VRDA": {"file": "chunk-W6L2VRDA.js"}, "chunk-ZMLY2J2T": {"file": "chunk-ZMLY2J2T.js"}, "chunk-4B2QHNJT": {"file": "chunk-4B2QHNJT.js"}}}