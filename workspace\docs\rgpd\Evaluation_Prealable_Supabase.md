# Évaluation préalable des garanties offertes par Supabase en matière de protection des données

## 1. Introduction

Conformément à l'article 28 du RGPD, WeMa a procédé à une évaluation préalable des garanties offertes par Supabase avant de lui confier le traitement de données à caractère personnel. Cette évaluation vise à s'assurer que Supabase présente des garanties suffisantes quant à la mise en œuvre de mesures techniques et organisationnelles appropriées pour que le traitement réponde aux exigences du RGPD et garantisse la protection des droits des personnes concernées.

## 2. Présentation de Supabase

**Supabase, Inc.** est une entreprise américaine fondée en 2020 qui propose une plateforme de base de données PostgreSQL open-source avec des fonctionnalités d'authentification, de stockage et d'API automatisées. Supabase se positionne comme une alternative open-source à Firebase.

**Site web** : [https://supabase.com](https://supabase.com)  
**Siège social** : San Francisco, Californie, États-Unis  
**Année de création** : 2020  
**Type de service** : Plateforme de base de données PostgreSQL as a Service

## 3. Certifications et conformité

Supabase dispose des certifications suivantes en matière de sécurité et de protection des données :

- **SOC 2 Type 2** (obtenue le 22 mai 2023)
- **HIPAA** (obtenue le 11 août 2023)

Ces certifications attestent que Supabase a mis en place des contrôles efficaces en matière de sécurité, disponibilité, intégrité de traitement, confidentialité et protection de la vie privée, et que ces contrôles ont été vérifiés par un auditeur indépendant sur une période prolongée.

## 4. Mesures de sécurité techniques

### 4.1 Sécurité de l'infrastructure

- **Hébergement** : Supabase utilise AWS comme fournisseur d'infrastructure cloud, qui dispose de nombreuses certifications de sécurité (ISO 27001, SOC 2, etc.).
- **Localisation des données** : Les données sont hébergées dans des centres de données situés dans l'Union Européenne (région eu-central-1).
- **Isolation** : Chaque projet Supabase est isolé dans son propre environnement sécurisé.

### 4.2 Sécurité des données

- **Chiffrement au repos** : Les données stockées dans PostgreSQL sont chiffrées au repos.
- **Chiffrement en transit** : Toutes les communications avec Supabase sont chiffrées via TLS.
- **Sauvegardes** : Supabase effectue des sauvegardes automatiques quotidiennes des bases de données.
- **Contrôle d'accès** : Système de contrôle d'accès basé sur les rôles (RBAC) et politiques de sécurité au niveau des lignes (RLS).

### 4.3 Authentification et autorisation

- **Authentification multi-facteurs** : Disponible pour les comptes administrateurs.
- **Gestion des identités** : Système d'authentification intégré avec support pour OAuth, magic links, et authentification par mot de passe.
- **Journalisation** : Journalisation des événements d'authentification et des accès.

### 4.4 Surveillance et réponse aux incidents

- **Surveillance continue** : Surveillance 24/7 des systèmes et des alertes automatisées.
- **Gestion des incidents** : Procédures documentées pour la détection, l'évaluation et la réponse aux incidents de sécurité.
- **Notification des violations** : Engagement à notifier les clients en cas de violation de données.

## 5. Mesures organisationnelles

### 5.1 Politiques et procédures

- **Politique de confidentialité** : [https://supabase.com/privacy](https://supabase.com/privacy)
- **Conditions d'utilisation** : [https://supabase.com/terms](https://supabase.com/terms)
- **Politique de sécurité** : Documentation disponible sur les pratiques de sécurité.

### 5.2 Formation et sensibilisation

- **Formation du personnel** : Le personnel de Supabase reçoit une formation sur la sécurité et la protection des données.
- **Engagements de confidentialité** : Les employés sont soumis à des obligations de confidentialité.

### 5.3 Sous-traitance ultérieure

- **Transparence** : Supabase communique sur ses principaux sous-traitants (notamment AWS).
- **Garanties** : Les sous-traitants ultérieurs sont soumis à des obligations similaires en matière de protection des données.

## 6. Transferts de données hors UE

Supabase héberge les données dans des centres de données situés dans l'Union Européenne (région eu-central-1). Cependant, certains accès techniques peuvent être effectués depuis des pays tiers, notamment les États-Unis, où se trouve l'équipe de Supabase.

Ces transferts potentiels sont encadrés par :
- Les certifications de sécurité de Supabase (SOC 2 Type 2)
- Les mesures techniques de sécurité (chiffrement, contrôles d'accès)
- Les engagements contractuels dans les conditions d'utilisation

## 7. Exercice des droits des personnes concernées

Supabase fournit des outils et des API qui permettent à WeMa de répondre aux demandes d'exercice des droits des personnes concernées :
- Droit d'accès : Possibilité d'extraire les données via l'API
- Droit de rectification : Possibilité de modifier les données via l'API
- Droit à l'effacement : Possibilité de supprimer les données via l'API
- Droit à la portabilité : Possibilité d'exporter les données dans des formats standards

## 8. Limites identifiées

### 8.1 Compte gratuit

WeMa utilise actuellement un compte gratuit de Supabase, ce qui implique certaines limitations :
- Absence de contrat personnalisé ou d'avenant RGPD spécifique
- Niveau de service (SLA) limité
- Support technique standard

### 8.2 Droit d'audit

Les conditions d'utilisation standard de Supabase ne prévoient pas explicitement un droit d'audit pour les utilisateurs de comptes gratuits. Cependant, Supabase publie régulièrement des informations sur ses certifications et ses pratiques de sécurité.

## 9. Conclusion de l'évaluation

Sur la base de cette évaluation préalable, Supabase présente des garanties suffisantes en matière de protection des données pour les raisons suivantes :

1. **Certifications reconnues** : SOC 2 Type 2 et HIPAA, attestant d'un niveau élevé de sécurité et de conformité.
2. **Mesures techniques robustes** : Chiffrement, contrôles d'accès, isolation des projets, etc.
3. **Transparence** : Documentation détaillée sur les pratiques de sécurité et les politiques.
4. **Localisation des données** : Hébergement dans l'UE, conforme aux exigences du RGPD.
5. **Outils adaptés** : Fonctionnalités permettant de répondre aux demandes d'exercice des droits.

Les limitations identifiées (compte gratuit, droit d'audit limité) sont compensées par les garanties offertes et les mesures alternatives mises en place par WeMa pour assurer un suivi régulier de la conformité de Supabase.

WeMa envisagera, dans le cadre de l'évolution de son utilisation de Supabase, de passer à une offre payante qui pourrait inclure des garanties contractuelles supplémentaires.

## 10. Date et validation

Évaluation réalisée le : [Date]  
Par : [Nom et fonction]  
Validée par : [Nom et fonction du responsable]
