import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

/**
 * Valide et récupère les variables d'environnement Supabase
 * @throws Error si les variables sont manquantes ou invalides
 */
function validateEnvironmentVariables() {
  const SUPABASE_URL = import.meta.env.VITE_SUPABASE_URL;
  const SUPABASE_PUBLISHABLE_KEY = import.meta.env.VITE_SUPABASE_API_KEY;

  // Validation de l'URL Supabase
  if (!SUPABASE_URL) {
    throw new Error(
      '❌ ERREUR CONFIGURATION: La variable d\'environnement VITE_SUPABASE_URL est manquante.\n' +
      'Veuillez créer un fichier .env avec:\n' +
      'VITE_SUPABASE_URL=https://votre-projet.supabase.co'
    );
  }

  if (!SUPABASE_URL.startsWith('https://') || !SUPABASE_URL.includes('.supabase.co')) {
    throw new Error(
      '❌ ERREUR CONFIGURATION: L\'URL Supabase semble invalide.\n' +
      `URL fournie: ${SUPABASE_URL}\n` +
      'Format attendu: https://votre-projet.supabase.co'
    );
  }

  // Validation de la clé API
  if (!SUPABASE_PUBLISHABLE_KEY) {
    throw new Error(
      '❌ ERREUR CONFIGURATION: La variable d\'environnement VITE_SUPABASE_API_KEY est manquante.\n' +
      'Veuillez créer un fichier .env avec:\n' +
      'VITE_SUPABASE_API_KEY=votre_cle_publique_supabase'
    );
  }

  if (SUPABASE_PUBLISHABLE_KEY.length < 100) {
    throw new Error(
      '❌ ERREUR CONFIGURATION: La clé API Supabase semble trop courte.\n' +
      'Vérifiez que vous utilisez la clé publique (anon key) et non la clé secrète.'
    );
  }

  console.log('✅ Variables d\'environnement Supabase validées avec succès');
  return { SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY };
}

// Valider les variables d'environnement au chargement du module
const { SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY } = validateEnvironmentVariables();

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);