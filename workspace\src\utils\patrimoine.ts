/**
 * Utilitaires pour la gestion du patrimoine des clients
 */

import { supabase } from '@/integrations/supabase/client';
import { safeDecrypt, isEncrypted } from '@/utils/encryption';

/**
 * Calcule le total du patrimoine pour un client donné
 * @param clientId - ID du client
 * @returns Promise<number> - Total du patrimoine en euros
 */
export async function calculateClientPatrimoine(clientId: string): Promise<number> {
  try {
    const { data, error } = await supabase
      .from('client_patrimoine')
      .select('montant, devise')
      .eq('client_id', clientId);

    if (error) {
      console.error('Erreur lors du calcul du patrimoine:', error);
      return 0;
    }

    if (!data || data.length === 0) {
      return 0;
    }

    // Déchiffrer et calculer le total
    const total = await data.reduce(async (sumPromise, entry) => {
      const sum = await sumPromise;

      // Déchiffrer le montant si nécessaire
      let montant = 0;
      if (entry.montant) {
        if (isEncrypted(entry.montant.toString())) {
          const decryptedMontant = await safeDecrypt(entry.montant.toString());
          montant = parseFloat(decryptedMontant) || 0;
        } else {
          montant = entry.montant || 0;
        }
      }

      if (entry.devise === 'EUR' || !entry.devise) {
        return sum + montant;
      }
      // Pour d'autres devises, on pourrait ajouter un taux de change
      return sum + montant;
    }, Promise.resolve(0));

    return total;
  } catch (error) {
    console.error('Erreur lors du calcul du patrimoine:', error);
    return 0;
  }
}

/**
 * Calcule le patrimoine pour plusieurs clients en une seule requête
 * @param clientIds - Array des IDs des clients
 * @returns Promise<Map<string, number>> - Map avec clientId -> total patrimoine
 */
export async function calculateMultipleClientPatrimoine(clientIds: string[]): Promise<Map<string, number>> {
  try {
    if (clientIds.length === 0) {
      return new Map();
    }

    const { data, error } = await supabase
      .from('client_patrimoine')
      .select('client_id, montant, devise')
      .in('client_id', clientIds);

    if (error) {
      console.error('Erreur lors du calcul du patrimoine multiple:', error);
      return new Map();
    }

    // Grouper par client et calculer les totaux
    const patrimoineMap = new Map<string, number>();

    // Initialiser tous les clients à 0
    clientIds.forEach(id => patrimoineMap.set(id, 0));

    // Déchiffrer et calculer les totaux
    if (data) {
      for (const entry of data) {
        const currentTotal = patrimoineMap.get(entry.client_id) || 0;

        // Déchiffrer le montant si nécessaire
        let montant = 0;
        if (entry.montant) {
          if (isEncrypted(entry.montant.toString())) {
            const decryptedMontant = await safeDecrypt(entry.montant.toString());
            montant = parseFloat(decryptedMontant) || 0;
          } else {
            montant = entry.montant || 0;
          }
        }

        // Pour l'instant on assume que tout est en EUR
        if (entry.devise === 'EUR' || !entry.devise) {
          patrimoineMap.set(entry.client_id, currentTotal + montant);
        } else {
          // Pour d'autres devises, on pourrait ajouter un taux de change
          patrimoineMap.set(entry.client_id, currentTotal + montant);
        }
      }
    }

    return patrimoineMap;
  } catch (error) {
    console.error('Erreur lors du calcul du patrimoine multiple:', error);
    return new Map();
  }
}

/**
 * Formate un montant en euros
 * @param montant - Montant à formater
 * @returns string - Montant formaté
 */
export function formatPatrimoine(montant: number): string {
  if (montant === 0) {
    return '';
  }

  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'EUR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(montant);
}

/**
 * Formate un montant en version compacte (K, M)
 * @param montant - Montant à formater
 * @returns string - Montant formaté en version compacte
 */
export function formatPatrimoineCompact(montant: number): string {
  if (montant === 0) {
    return '';
  }

  if (montant >= 1000000) {
    return `${(montant / 1000000).toFixed(1)}M€`;
  } else if (montant >= 1000) {
    return `${(montant / 1000).toFixed(0)}K€`;
  } else {
    return `${montant}€`;
  }
}
