-- Migration pour ajouter des mécanismes de durée de conservation des données
-- Conforme aux recommandations de la CNIL pour la protection des données

-- Fonction pour purger les notifications anciennes (conservation de 1 an)
CREATE OR REPLACE FUNCTION public.purge_old_notifications()
RETURNS void AS $$
BEGIN
  DELETE FROM public.notifications
  WHERE created_at < NOW() - INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Commentaire sur la fonction
COMMENT ON FUNCTION public.purge_old_notifications IS 'Purge les notifications de plus de 1 an pour conformité RGPD';

-- Fonction pour purger les pings anciens (conservation de 1 an)
CREATE OR REPLACE FUNCTION public.purge_old_pings()
RETURNS void AS $$
BEGIN
  DELETE FROM public.pings
  WHERE created_at < NOW() - INTERVAL '1 year';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Commentaire sur la fonction
COMMENT ON FUNCTION public.purge_old_pings IS 'Purge les messages directs (pings) de plus de 1 an pour conformité RGPD';

-- Fonction pour anonymiser les clients inactifs (conservation de 3 ans)
CREATE OR REPLACE FUNCTION public.anonymize_inactive_clients()
RETURNS void AS $$
DECLARE
  inactive_client_id UUID;
  anonymized_name TEXT;
BEGIN
  -- Trouver les clients sans activité depuis 3 ans
  FOR inactive_client_id IN
    SELECT c.id
    FROM public.clients c
    LEFT JOIN public.steps s ON s.client_id = c.id
    WHERE (
      -- Aucune mise à jour du client depuis 3 ans
      c.created_at < NOW() - INTERVAL '3 years'
      AND NOT EXISTS (
        -- Aucune étape mise à jour depuis 3 ans
        SELECT 1 FROM public.steps
        WHERE client_id = c.id
        AND (
          created_at > NOW() - INTERVAL '3 years'
          OR received_date > NOW() - INTERVAL '3 years'
        )
      )
    )
    GROUP BY c.id
  LOOP
    -- Générer un nom anonymisé
    anonymized_name := 'Client anonymisé ' || TO_CHAR(NOW(), 'YYYY-MM-DD');
    
    -- Anonymiser le client
    UPDATE public.clients
    SET name = anonymized_name
    WHERE id = inactive_client_id;
    
    -- Anonymiser les commentaires des étapes
    UPDATE public.steps
    SET comment = 'Commentaire anonymisé'
    WHERE client_id = inactive_client_id
    AND comment IS NOT NULL
    AND comment != '';
    
    -- Journaliser l'anonymisation
    INSERT INTO public.data_access_logs (
      operation,
      table_name,
      record_id,
      details,
      timestamp
    ) VALUES (
      'anonymize',
      'clients',
      inactive_client_id,
      'Anonymisation automatique pour conformité RGPD',
      NOW()
    );
  END LOOP;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Commentaire sur la fonction
COMMENT ON FUNCTION public.anonymize_inactive_clients IS 'Anonymise les clients inactifs depuis plus de 3 ans pour conformité RGPD';

-- Créer des déclencheurs pour exécuter les purges automatiquement
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Purge des notifications (tous les jours à 1h du matin)
SELECT cron.schedule(
  'purge-old-notifications',
  '0 1 * * *',
  $$SELECT public.purge_old_notifications()$$
);

-- Purge des pings (tous les jours à 2h du matin)
SELECT cron.schedule(
  'purge-old-pings',
  '0 2 * * *',
  $$SELECT public.purge_old_pings()$$
);

-- Anonymisation des clients inactifs (tous les lundis à 3h du matin)
SELECT cron.schedule(
  'anonymize-inactive-clients',
  '0 3 * * 1',
  $$SELECT public.anonymize_inactive_clients()$$
);
