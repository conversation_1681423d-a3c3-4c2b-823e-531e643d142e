/**
 * Composant de configuration de l'application
 * Permet de gérer les paramètres comme les assignees par défaut
 */

import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Settings } from "lucide-react";
import { toast } from "sonner";
import { 
  getAppConfig, 
  saveAppConfig, 
  getDefaultAssignees,
  setDefaultAssignees 
} from "@/services/configService";
import { getAllProfiles } from "@/services/profileService";
import { Assignee, Profile } from "@/types";

interface ConfigurationDialogProps {
  trigger?: React.ReactNode;
}

export function ConfigurationDialog({ trigger }: ConfigurationDialogProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profiles, setProfiles] = useState<Profile[]>([]);
  const [selectedAssignees, setSelectedAssignees] = useState<string[]>([]);
  const [autoAssign, setAutoAssign] = useState(true);

  // Charger les données initiales
  useEffect(() => {
    if (isOpen) {
      loadData();
    }
  }, [isOpen]);

  const loadData = async () => {
    setLoading(true);
    try {
      // Charger tous les profils
      const allProfiles = await getAllProfiles();
      setProfiles(allProfiles);

      // Charger la configuration actuelle
      const config = getAppConfig();
      setAutoAssign(config.autoAssignNewSteps);
      setSelectedAssignees(config.defaultAssignees);
    } catch (error) {
      console.error('Erreur lors du chargement des données de configuration:', error);
      toast.error('Impossible de charger la configuration');
    } finally {
      setLoading(false);
    }
  };

  const handleAssigneeToggle = (assigneeId: string, checked: boolean) => {
    if (checked) {
      setSelectedAssignees(prev => [...prev, assigneeId]);
    } else {
      setSelectedAssignees(prev => prev.filter(id => id !== assigneeId));
    }
  };

  const handleSave = async () => {
    try {
      // Sauvegarder la configuration
      saveAppConfig({
        defaultAssignees: selectedAssignees,
        autoAssignNewSteps: autoAssign
      });

      toast.success('Configuration sauvegardée avec succès');
      setIsOpen(false);
    } catch (error) {
      console.error('Erreur lors de la sauvegarde de la configuration:', error);
      toast.error('Impossible de sauvegarder la configuration');
    }
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm">
      <Settings className="h-4 w-4 mr-2" />
      Configuration
    </Button>
  );

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Configuration de l'application</DialogTitle>
          <DialogDescription>
            Gérez les paramètres de l'application, notamment les assignations automatiques.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Configuration de l'assignation automatique */}
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="auto-assign"
                checked={autoAssign}
                onCheckedChange={setAutoAssign}
              />
              <Label htmlFor="auto-assign" className="text-sm font-medium">
                Assignation automatique des nouvelles étapes
              </Label>
            </div>
            <p className="text-sm text-muted-foreground">
              Assigne automatiquement les utilisateurs sélectionnés ci-dessous aux nouvelles étapes créées.
            </p>
          </div>

          {/* Sélection des assignees par défaut */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              Utilisateurs assignés par défaut
            </Label>
            <p className="text-sm text-muted-foreground">
              Sélectionnez les utilisateurs qui seront automatiquement assignés aux nouvelles étapes.
            </p>
            
            {loading ? (
              <div className="text-sm text-muted-foreground">Chargement...</div>
            ) : (
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {profiles.map((profile) => (
                  <div key={profile.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`assignee-${profile.id}`}
                      checked={selectedAssignees.includes(profile.id)}
                      onCheckedChange={(checked) => 
                        handleAssigneeToggle(profile.id, checked as boolean)
                      }
                      disabled={!autoAssign}
                    />
                    <Label 
                      htmlFor={`assignee-${profile.id}`} 
                      className="text-sm cursor-pointer"
                    >
                      {profile.name}
                    </Label>
                  </div>
                ))}
                {profiles.length === 0 && (
                  <div className="text-sm text-muted-foreground">
                    Aucun utilisateur disponible
                  </div>
                )}
              </div>
            )}
          </div>

          {/* Informations sur la configuration actuelle */}
          <div className="bg-muted/50 p-3 rounded-lg">
            <h4 className="text-sm font-medium mb-2">Configuration actuelle</h4>
            <ul className="text-sm text-muted-foreground space-y-1">
              <li>
                Assignation automatique: {autoAssign ? 'Activée' : 'Désactivée'}
              </li>
              <li>
                Utilisateurs par défaut: {selectedAssignees.length} sélectionné(s)
              </li>
            </ul>
          </div>
        </div>

        <div className="flex justify-end space-x-2 pt-4">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Annuler
          </Button>
          <Button onClick={handleSave} disabled={loading}>
            Sauvegarder
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
