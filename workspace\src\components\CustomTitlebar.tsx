import React, { useEffect } from 'react';
import { appWindow } from '@tauri-apps/api/window';

/**
 * Composant de barre de titre personnalisée pour l'application Tauri
 * Remplace la barre de titre native par une barre blanche avec le bouton de fermeture noir
 */
const CustomTitlebar: React.FC = () => {
  useEffect(() => {
    // Fonction pour injecter des styles CSS personnalisés pour la barre de titre
    const injectTitlebarStyles = () => {
      // Sélectionner la barre de titre
      const titlebar = document.querySelector('.titlebar') as HTMLElement;
      if (titlebar) {
        // Appliquer le style blanc à la barre de titre
        titlebar.style.backgroundColor = 'white';
        titlebar.style.color = '#333';
        
        // Sélectionner tous les boutons de la barre de titre
        const buttons = titlebar.querySelectorAll('.titlebar-button');
        buttons.forEach((button) => {
          const btn = button as HTMLElement;
          
          // Appliquer des styles différents selon le type de bouton
          if (btn.classList.contains('close')) {
            // Bouton de fermeture en noir
            btn.style.backgroundColor = '#000';
            
            // Icône de fermeture en blanc pour contraste
            const svg = btn.querySelector('svg');
            if (svg) {
              svg.setAttribute('fill', 'white');
            }
          } else {
            // Autres boutons (minimiser, maximiser) transparents avec icônes grises
            btn.style.backgroundColor = 'transparent';
            
            const svg = btn.querySelector('svg');
            if (svg) {
              svg.setAttribute('fill', '#333');
            }
          }
        });
      }
    };

    // Appliquer les styles immédiatement
    injectTitlebarStyles();
    
    // Observer les changements dans le DOM pour réappliquer les styles si nécessaire
    // (par exemple, si la barre de titre est recréée après un changement de thème)
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
          // Vérifier si la barre de titre a été ajoutée ou modifiée
          const titlebarAdded = Array.from(mutation.addedNodes).some(
            (node) => node instanceof HTMLElement && (
              node.classList.contains('titlebar') || 
              node.querySelector('.titlebar')
            )
          );
          
          if (titlebarAdded) {
            injectTitlebarStyles();
          }
        }
      });
    });
    
    // Observer le document entier pour les changements
    observer.observe(document.body, { 
      childList: true, 
      subtree: true 
    });
    
    // Nettoyer l'observateur lors du démontage du composant
    return () => {
      observer.disconnect();
    };
  }, []);

  return null; // Ce composant n'a pas de rendu visuel, il modifie juste les styles existants
};

export default CustomTitlebar;
