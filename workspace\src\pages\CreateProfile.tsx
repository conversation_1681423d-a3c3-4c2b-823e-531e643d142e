
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft, Loader2 } from 'lucide-react';
import { useProfileContext } from '@/contexts/ProfileContext';
import { Logo } from '@/components/Logo';

const CreateProfile = () => {
  const navigate = useNavigate();
  const [name, setName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const { createProfile } = useProfileContext();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) return;

    setIsCreating(true);
    await createProfile(name.trim());
    setIsCreating(false);
    navigate('/');
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-md">
      <Button
        variant="ghost"
        onClick={() => navigate('/')}
        className="mb-8"
      >
        <ArrowLeft className="mr-2 h-4 w-4" />
        Retour
      </Button>

      <div className="flex flex-col items-center mb-8">
        <Logo size="lg" className="mb-4" />
        <h1 className="text-2xl font-bold text-center">
          Créer votre profil
        </h1>
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <Input
            placeholder="Votre nom"
            value={name}
            onChange={(e) => setName(e.target.value)}
            className="w-full"
            autoFocus
          />
        </div>

        <Button
          type="submit"
          className="w-full"
          disabled={!name.trim() || isCreating}
        >
          {isCreating ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Création en cours...
            </>
          ) : (
            'Créer le profil'
          )}
        </Button>
      </form>
    </div>
  );
};

export default CreateProfile;
