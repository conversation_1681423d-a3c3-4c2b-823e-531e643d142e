# Guide de Contribution - WeMa Tracker

Merci de votre intérêt pour contribuer à WeMa Tracker ! Ce document décrit les processus et guidelines pour contribuer au projet.

## 🚀 Démarrage Rapide

### Prérequis
- Node.js (version 18 ou supérieure)
- Rust (dernière version stable)
- Git

### Installation
```bash
git clone https://github.com/adrienlle/dossier-tracker-client.git
cd dossier-tracker-client
npm install
```

### Configuration
1. Copiez `.env.example` vers `.env`
2. Configurez vos variables d'environnement Supabase
3. Lancez le serveur de développement : `npm run dev`

## 📋 Types de Contributions

### 🐛 Rapports de Bugs
- Utilisez les templates d'issues GitHub
- Incluez les étapes de reproduction
- Spécifiez votre environnement (OS, versions)

### ✨ Nouvelles Fonctionnalités
- Ouvrez d'abord une issue pour discussion
- Respectez l'architecture existante
- Incluez des tests si applicable

### 📚 Documentation
- Corrections de typos
- Améliorations de clarté
- Traductions

## 🔧 Processus de Développement

### 1. Branches
- `main` : code de production
- `develop` : développement actif
- `feature/nom-feature` : nouvelles fonctionnalités
- `fix/nom-bug` : corrections de bugs

### 2. Commits
Utilisez le format Conventional Commits :
```
type(scope): description

feat(client): ajout du système de notifications
fix(auth): correction de la validation des tokens
docs(readme): mise à jour des instructions d'installation
```

### 3. Pull Requests
- Une PR par fonctionnalité/correction
- Tests passants requis
- Code review obligatoire
- Documentation mise à jour si nécessaire

## 🧪 Tests

```bash
# Tests unitaires
npm test

# Tests E2E
npm run test:e2e

# Linting
npm run lint

# Build Tauri
npm run tauri build
```

## 📐 Standards de Code

### TypeScript/JavaScript
- Utilisez TypeScript strict
- Suivez les règles ESLint configurées
- Documentez les fonctions complexes

### React
- Composants fonctionnels avec hooks
- Props typées avec TypeScript
- Noms explicites pour les composants

### Rust (Tauri)
- Suivez les conventions Rust standard
- Utilisez `cargo fmt` et `cargo clippy`
- Documentez les fonctions publiques

## 🔒 Sécurité

### Variables d'Environnement
- Ne committez JAMAIS de vraies clés API
- Utilisez `.env.example` pour les templates
- Validez toutes les entrées utilisateur

### RGPD
- Respectez les principes de minimisation des données
- Implémentez le droit à l'effacement
- Documentez le traitement des données

## 📞 Communication

- **Issues GitHub** : Bugs et demandes de fonctionnalités
- **Discussions GitHub** : Questions générales
- **Email** : <EMAIL> pour les questions sensibles

## ⚖️ Licence

En contribuant, vous acceptez que vos contributions soient licenciées sous la même licence propriétaire que le projet.

## 🙏 Reconnaissance

Tous les contributeurs seront reconnus dans le fichier CONTRIBUTORS.md.

---

Merci de votre contribution à WeMa Tracker ! 🎉 