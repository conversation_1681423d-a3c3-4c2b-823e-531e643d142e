import React from 'react';
import {
  ContextMenu,
  ContextMenuContent,
  ContextMenuItem,
  ContextMenuTrigger,
  ContextMenuSeparator,
} from '@/components/ui/context-menu';
import { Check, RotateCcw } from 'lucide-react';
import { Client } from '@/types';

interface ClientContextMenuProps {
  client: Client;
  onToggleCompleted: (clientId: string, completed: boolean) => void;
  children: React.ReactNode;
}

export const ClientContextMenu: React.FC<ClientContextMenuProps> = ({
  client,
  onToggleCompleted,
  children,
}) => {
  const handleToggleCompleted = () => {
    onToggleCompleted(client.id, !client.completed);
  };

  return (
    <ContextMenu>
      <ContextMenuTrigger asChild>
        {children}
      </ContextMenuTrigger>
      <ContextMenuContent className="w-48">
        <ContextMenuItem onClick={handleToggleCompleted}>
          {client.completed ? (
            <>
              <RotateCcw className="mr-2 h-4 w-4" />
              Marquer comme actif
            </>
          ) : (
            <>
              <Check className="mr-2 h-4 w-4" />
              Marquer comme terminé
            </>
          )}
        </ContextMenuItem>
        <ContextMenuSeparator />
        <ContextMenuItem disabled className="text-gray-400">
          Client: {client.name}
        </ContextMenuItem>
      </ContextMenuContent>
    </ContextMenu>
  );
};
