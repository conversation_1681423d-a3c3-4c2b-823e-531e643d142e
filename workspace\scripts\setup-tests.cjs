#!/usr/bin/env node

/**
 * Script de configuration des tests pour WeMa Tracker
 * 
 * Ce script installe et configure tous les outils de test nécessaires :
 * - Vitest pour les tests unitaires
 * - Playwright pour les tests E2E
 * - MSW pour les mocks d'API
 * - Testing Library pour les tests de composants React
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Configuration des tests pour WeMa Tracker...\n');

// Fonction utilitaire pour exécuter des commandes
function runCommand(command, description) {
  console.log(`📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} terminé\n`);
  } catch (error) {
    console.error(`❌ Erreur lors de ${description}:`, error.message);
    process.exit(1);
  }
}

// Fonction pour vérifier si une dépendance existe
function isDependencyInstalled(packageName) {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  return !!(
    packageJson.dependencies?.[packageName] || 
    packageJson.devDependencies?.[packageName]
  );
}

// Étape 1: Installation des dépendances de test
console.log('📋 Installation des dépendances de test...\n');

const testDependencies = [
  'vitest',
  '@vitest/ui',
  '@vitest/coverage-v8',
  '@testing-library/react',
  '@testing-library/jest-dom',
  '@testing-library/user-event',
  '@playwright/test',
  'jsdom',
  'msw',
  'vitest-mock-extended',
  '@types/uuid'
];

const missingDeps = testDependencies.filter(dep => !isDependencyInstalled(dep));

if (missingDeps.length > 0) {
  console.log(`Installing missing dependencies: ${missingDeps.join(', ')}`);
  runCommand(
    `npm install --save-dev ${missingDeps.join(' ')}`,
    'Installation des dépendances de test'
  );
} else {
  console.log('✅ Toutes les dépendances de test sont déjà installées\n');
}

// Étape 2: Installation des navigateurs Playwright
console.log('🌐 Installation des navigateurs Playwright...');
runCommand('npx playwright install', 'Installation des navigateurs Playwright');

// Étape 3: Vérification de la structure des dossiers
console.log('📁 Vérification de la structure des dossiers de test...');

const requiredDirs = [
  'src/test',
  'src/test/mocks',
  'src/components/__tests__',
  'src/services/__tests__',
  'e2e',
  'coverage'
];

requiredDirs.forEach(dir => {
  const fullPath = path.join(process.cwd(), dir);
  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
    console.log(`✅ Créé le dossier: ${dir}`);
  }
});

console.log('\n✅ Structure des dossiers vérifiée\n');

// Étape 4: Mise à jour du .gitignore
console.log('📝 Mise à jour du .gitignore...');

const gitignorePath = path.join(process.cwd(), '.gitignore');
let gitignoreContent = '';

if (fs.existsSync(gitignorePath)) {
  gitignoreContent = fs.readFileSync(gitignorePath, 'utf8');
}

const testIgnoreEntries = [
  '# Test coverage',
  'coverage/',
  '# Playwright',
  'test-results/',
  'playwright-report/',
  'playwright/.cache/',
  '# Vitest',
  '.vitest/',
  '# Test artifacts',
  '*.test.js.snap',
  '*.test.tsx.snap'
];

let needsUpdate = false;
testIgnoreEntries.forEach(entry => {
  if (!gitignoreContent.includes(entry)) {
    gitignoreContent += '\n' + entry;
    needsUpdate = true;
  }
});

if (needsUpdate) {
  fs.writeFileSync(gitignorePath, gitignoreContent);
  console.log('✅ .gitignore mis à jour\n');
} else {
  console.log('✅ .gitignore déjà à jour\n');
}

// Étape 5: Affichage des commandes de test disponibles
console.log('🎯 Commandes de test disponibles:\n');

const testCommands = [
  '  npm test                    - Lance tous les tests unitaires',
  '  npm run test:watch          - Lance les tests en mode surveillance',
  '  npm run test:ui             - Interface graphique pour les tests',
  '  npm run test:coverage       - Tests avec rapport de couverture',
  '  npm run test:unit           - Tests unitaires uniquement',
  '  npm run test:integration    - Tests d\'intégration uniquement',
  '  npm run test:e2e            - Tests E2E avec Playwright',
  '  npm run test:e2e:ui         - Tests E2E avec interface graphique',
  '  npm run test:all            - Tous les types de tests'
];

testCommands.forEach(cmd => console.log(cmd));

console.log('\n🎉 Configuration des tests terminée avec succès !');
console.log('\n💡 Pour commencer, lancez: npm test');
console.log('📖 Documentation des tests: voir CONTRIBUTING.md\n');

// Vérification finale
console.log('🔍 Vérification finale...');
try {
  execSync('npm run lint', { stdio: 'pipe' });
  console.log('✅ Linting OK');
} catch (error) {
  console.log('⚠️  Linting: quelques warnings (normal)');
}

console.log('\n🚀 Prêt pour les tests !'); 