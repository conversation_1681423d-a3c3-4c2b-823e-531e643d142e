// Script pour maintenir la base de données Supabase active
// À exécuter tous les 3 jours pour éviter la mise en pause du projet

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Obtenir le chemin du répertoire actuel
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Lire les variables d'environnement depuis le fichier .env
function loadEnv() {
  try {
    const envPath = path.resolve(__dirname, '../.env');
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envVars = {};

    envContent.split('\n').forEach(line => {
      const [key, value] = line.split('=');
      if (key && value) {
        envVars[key.trim()] = value.trim();
      }
    });

    return envVars;
  } catch (error) {
    console.error('Erreur lors de la lecture du fichier .env:', error);
    return {
      VITE_SUPABASE_URL: 'https://hgyfjvhnfsuflqxzdgcv.supabase.co',
      VITE_SUPABASE_API_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhneWZqdmhuZnN1ZmxxeHpkZ2N2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ2MTY3ODgsImV4cCI6MjA2MDE5Mjc4OH0.IBHO1LbWX5McgIl721L94c27E-vy8JkQNV5blicSHEQ'
    };
  }
}

async function keepAlive() {
  try {
    const env = loadEnv();
    const supabaseUrl = env.VITE_SUPABASE_URL;
    const supabaseKey = env.VITE_SUPABASE_API_KEY;

    if (!supabaseUrl || !supabaseKey) {
      throw new Error('Variables d\'environnement Supabase manquantes');
    }

    console.log('Connexion à Supabase...');
    const supabase = createClient(supabaseUrl, supabaseKey);

    // Effectuer une requête simple pour maintenir la base de données active
    console.log('Envoi d\'une requête keep-alive à Supabase...');
    const { data, error } = await supabase
      .from('assignees')
      .select('count(*)', { count: 'exact', head: true });

    if (error) {
      throw error;
    }

    console.log('Requête keep-alive envoyée avec succès à', new Date().toISOString());
    console.log('Prochaine requête dans 3 jours');

    // Écrire un log de l'exécution
    const logMessage = `Keep-alive exécuté avec succès le ${new Date().toISOString()}\n`;
    fs.appendFileSync(path.resolve(__dirname, 'keep-alive.log'), logMessage);

    return true;
  } catch (error) {
    console.error('Erreur lors de l\'envoi de la requête keep-alive:', error);

    // Écrire l'erreur dans le log
    const errorMessage = `Erreur le ${new Date().toISOString()}: ${error.message}\n`;
    fs.appendFileSync(path.resolve(__dirname, 'keep-alive.log'), errorMessage);

    return false;
  }
}

// Exécuter la fonction
keepAlive()
  .then(success => {
    if (success) {
      process.exit(0);
    } else {
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Erreur non gérée:', error);
    process.exit(1);
  });
