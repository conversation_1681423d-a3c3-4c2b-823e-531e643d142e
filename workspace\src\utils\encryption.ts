/**
 * Système de chiffrement simple et robuste pour WeMa Tracker
 * Chiffre uniquement les données sensibles : noms clients, commentaires, messages
 */

// Configuration fixe et unique
const ENCRYPTION_KEY = "WeMaTracker-2025-RGPD-Protection-Key-v1";
const PREFIX = "enc:";

/**
 * Génère une clé de chiffrement à partir de la phrase secrète
 */
async function getKey(): Promise<CryptoKey> {
  const encoder = new TextEncoder();
  const keyMaterial = await crypto.subtle.importKey(
    'raw',
    encoder.encode(ENCRYPTION_KEY),
    { name: 'PBKDF2' },
    false,
    ['deriveBits', 'deriveKey']
  );

  return crypto.subtle.deriveKey(
    {
      name: 'PBKDF2',
      salt: encoder.encode('WeMaTracker-Salt'),
      iterations: 100000,
      hash: 'SHA-256'
    },
    keyMaterial,
    { name: 'AES-GCM', length: 256 },
    false,
    ['encrypt', 'decrypt']
  );
}

/**
 * Chiffre un texte avec AES-256-GCM
 * @param plaintext - Texte en clair à chiffrer
 * @returns Texte chiffré avec préfixe "enc:"
 */
export async function encryptText(plaintext: string): Promise<string> {
  if (!plaintext) return '';

  try {
    const key = await getKey();
    const encoder = new TextEncoder();
    const data = encoder.encode(plaintext);

    // Générer un IV aléatoire
    const iv = crypto.getRandomValues(new Uint8Array(12));

    // Chiffrer
    const encrypted = await crypto.subtle.encrypt(
      { name: 'AES-GCM', iv },
      key,
      data
    );

    // Combiner IV + données chiffrées
    const combined = new Uint8Array(iv.length + encrypted.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(encrypted), iv.length);

    // Encoder en base64
    const base64 = btoa(String.fromCharCode(...combined));

    return `${PREFIX}${base64}`;
  } catch (error) {
    console.error('Erreur lors du chiffrement:', error);
    throw new Error('Échec du chiffrement');
  }
}

/**
 * Déchiffre un texte chiffré
 * @param encryptedText - Texte chiffré avec préfixe "enc:"
 * @returns Texte en clair
 */
export async function decryptText(encryptedText: string): Promise<string> {
  if (!encryptedText || !encryptedText.startsWith(PREFIX)) {
    throw new Error('Format de données chiffrées invalide');
  }

  try {
    const key = await getKey();
    const base64Data = encryptedText.slice(PREFIX.length);

    // Vérifier que les données base64 sont valides
    if (!base64Data || base64Data.length === 0) {
      throw new Error('Données base64 vides');
    }

    // Vérifier le format base64
    const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
    if (!base64Regex.test(base64Data)) {
      throw new Error('Format base64 invalide');
    }

    // Décoder le base64 avec gestion d'erreur
    let combined: Uint8Array;
    try {
      combined = new Uint8Array(
        atob(base64Data).split('').map(char => char.charCodeAt(0))
      );
    } catch (error) {
      throw new Error('Échec du décodage base64');
    }

    // Vérifier la taille minimale (IV + données)
    if (combined.length < 13) {
      throw new Error('Données chiffrées trop courtes');
    }

    // Extraire IV et données
    const iv = combined.slice(0, 12);
    const data = combined.slice(12);

    // Déchiffrer
    const decrypted = await crypto.subtle.decrypt(
      { name: 'AES-GCM', iv },
      key,
      data
    );

    return new TextDecoder().decode(decrypted);
  } catch (error) {
    console.error('Erreur lors du déchiffrement:', error);
    throw new Error('Échec du déchiffrement');
  }
}

/**
 * Vérifie si un texte est chiffré
 * @param text - Texte à vérifier
 * @returns true si le texte est chiffré
 */
export function isEncrypted(text: string): boolean {
  if (!text || !text.startsWith(PREFIX)) {
    return false;
  }

  const base64Data = text.slice(PREFIX.length);

  // Vérifier que les données ne sont pas vides
  if (!base64Data || base64Data.length === 0) {
    return false;
  }

  // Vérifier le format base64
  const base64Regex = /^[A-Za-z0-9+/]*={0,2}$/;
  return base64Regex.test(base64Data) && base64Data.length > 16; // Minimum pour IV + quelques données
}

/**
 * Déchiffre un texte de manière sécurisée avec gestion d'erreur
 * @param text - Texte potentiellement chiffré
 * @returns Texte déchiffré ou marqueur d'erreur
 */
export async function safeDecrypt(text: string): Promise<string> {
  if (!text) return '';
  if (!isEncrypted(text)) return text;

  try {
    return await decryptText(text);
  } catch (error) {
    console.error('⚠️ Erreur de déchiffrement:', error);
    return '[Données corrompues]';
  }
}

/**
 * Chiffre un texte de manière sécurisée avec gestion d'erreur
 * @param text - Texte à chiffrer
 * @returns Texte chiffré ou texte original en cas d'erreur
 */
export async function safeEncrypt(text: string): Promise<string> {
  if (!text) return '';

  try {
    return await encryptText(text);
  } catch (error) {
    console.error('⚠️ Erreur de chiffrement:', error);
    return text; // Retourner le texte original en cas d'erreur
  }
}
