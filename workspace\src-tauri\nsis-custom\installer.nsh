!macro customInstall
  ; Définir l'icône de la fenêtre de l'installateur
  !define MUI_ICON "${BUILD_RESOURCES_DIR}\icons\icon.ico"
  !define MUI_UNICON "${BUILD_RESOURCES_DIR}\icons\icon.ico"

  ; Remplacer l'icône de la fenêtre de l'installateur
  SetOutPath $INSTDIR
  File "${BUILD_RESOURCES_DIR}\icons\icon.ico"

  ; Modifier le registre pour utiliser notre icône
  WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\${PRODUCT_NAME}" "DisplayIcon" "$INSTDIR\icon.ico"
!macroend
