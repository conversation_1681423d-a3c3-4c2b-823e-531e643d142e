-- Migration pour mettre à jour les contraintes de clé étrangère
-- Cette migration corrige les références de clé étrangère pour utiliser la table assignees au lieu de profiles

-- 1. Supprimer les contraintes de clé étrangère existantes sur la table notifications
ALTER TABLE IF EXISTS public.notifications
  DROP CONSTRAINT IF EXISTS notifications_created_by_fkey,
  DROP CONSTRAINT IF EXISTS notifications_user_id_fkey;

-- 2. Ajouter les nouvelles contraintes de clé étrangère qui référencent la table assignees
ALTER TABLE IF EXISTS public.notifications
  ADD CONSTRAINT notifications_created_by_fkey
    FOREIGN KEY (created_by)
    REFERENCES public.assignees(id)
    ON DELETE SET NULL,
  ADD CONSTRAINT notifications_user_id_fkey
    FOREIGN KEY (user_id)
    REFERENCES public.assignees(id)
    ON DELETE CASCADE;

-- 3. Supprimer les contraintes de clé étrangère existantes sur la table pings
ALTER TABLE IF EXISTS public.pings
  DROP CONSTRAINT IF EXISTS pings_from_user_id_fkey,
  DROP CONSTRAINT IF EXISTS pings_to_user_id_fkey;

-- 4. Ajouter les nouvelles contraintes de clé étrangère qui référencent la table assignees
ALTER TABLE IF EXISTS public.pings
  ADD CONSTRAINT pings_from_user_id_fkey
    FOREIGN KEY (from_user_id)
    REFERENCES public.assignees(id)
    ON DELETE CASCADE,
  ADD CONSTRAINT pings_to_user_id_fkey
    FOREIGN KEY (to_user_id)
    REFERENCES public.assignees(id)
    ON DELETE CASCADE;

-- 5. Commentaires sur les modifications
COMMENT ON CONSTRAINT notifications_created_by_fkey ON public.notifications IS 'Contrainte mise à jour pour référencer la table assignees';
COMMENT ON CONSTRAINT notifications_user_id_fkey ON public.notifications IS 'Contrainte mise à jour pour référencer la table assignees';
COMMENT ON CONSTRAINT pings_from_user_id_fkey ON public.pings IS 'Contrainte mise à jour pour référencer la table assignees';
COMMENT ON CONSTRAINT pings_to_user_id_fkey ON public.pings IS 'Contrainte mise à jour pour référencer la table assignees';

-- 6. Journaliser la migration
INSERT INTO public.data_access_logs (
  operation,
  table_name,
  details,
  timestamp
) VALUES (
  'update',
  'system',
  'Migration des contraintes de clé étrangère de profiles vers assignees',
  NOW()
);
