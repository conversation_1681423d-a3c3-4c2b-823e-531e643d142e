[package]
name = "app"
version = "0.1.0"
description = "WeMa Tracker - Application professionnelle de suivi des dossiers clients"
authors = ["WeMa <<EMAIL>>"]
license = "Proprietary"
repository = "https://github.com/wema/tracker"
homepage = "https://wema.fr"
keywords = ["productivity", "client-management", "business"]
categories = ["office"]
edition = "2021"
rust-version = "1.77.2"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[lib]
name = "app_lib"
crate-type = ["staticlib", "cdylib", "rlib"]

[build-dependencies]
tauri-build = { version = "2.1.1", features = [] }

[package.metadata.bundle]
resources = ["app.rc"]

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
log = "0.4"
tauri = { version = "2.4.1", features = [] }
tauri-plugin-log = "2.0.0-rc"
