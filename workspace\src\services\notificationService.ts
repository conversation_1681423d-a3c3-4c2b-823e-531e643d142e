/**
 * Service pour la gestion des notifications
 * Contient les fonctions liées aux opérations sur les notifications
 */

import { supabase } from "@/integrations/supabase/client";
import { Notification, Client, Step, Assignee } from "@/types";

import { sendDesktopNotification } from "@/utils/notifications";
import { updateTaskbarBadge } from "@/utils/taskbar";
import { handleError } from "@/services/errorService";

/**
 * Récupère les notifications d'un utilisateur
 * @param userId ID de l'utilisateur
 * @returns Liste des notifications
 */
export async function getUserNotifications(userId: string): Promise<Notification[]> {
  try {
    const { data: notifications, error } = await supabase
      .from('notifications')
      .select('*, created_by_profile:assignees!created_by(name)')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;

    // Utiliser directement les messages (plus de chiffrement)
    const processedNotifications = notifications.map(notification => ({
      ...notification,
      message: notification.message || ''
    }));

    return processedNotifications;
  } catch (error) {
    console.error('Erreur lors de la récupération des notifications:', error);
    return [];
  }
}

/**
 * Marque une notification comme lue
 * @param notificationId ID de la notification
 * @param userId ID de l'utilisateur
 */
export async function markNotificationAsRead(notificationId: string, userId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', notificationId)
      .eq('user_id', userId);

    if (error) throw error;
  } catch (error) {
    console.error('Erreur lors du marquage de la notification comme lue:', error);
    throw error;
  }
}

/**
 * Marque toutes les notifications d'un utilisateur comme lues
 * @param userId ID de l'utilisateur
 */
export async function markAllNotificationsAsRead(userId: string): Promise<void> {
  try {
    const { error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('user_id', userId)
      .eq('read', false);

    if (error) throw error;
  } catch (error) {
    console.error('Erreur lors du marquage de toutes les notifications comme lues:', error);
    throw error;
  }
}

/**
 * Crée une notification
 * @param userId ID de l'utilisateur destinataire
 * @param stepId ID de l'étape concernée
 * @param createdById ID du créateur de la notification
 * @param message Message de la notification
 * @param sendDesktopNotif Envoyer une notification de bureau
 */
export async function createNotification(
  userId: string,
  stepId: string,
  createdById: string,
  message: string,
  sendDesktopNotif: boolean = false
): Promise<void> {
  try {
    // Insérer directement le message (plus de chiffrement)
    const { error } = await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        step_id: stepId,
        created_by: createdById,
        message: message
      });

    if (error) throw error;

    // Envoyer une notification de bureau si demandé
    if (sendDesktopNotif) {
      sendDesktopNotification(
        "Nouvelle notification",
        message,
        "/logo.png"
      );
    }
  } catch (error) {
    console.error('Erreur lors de la création de la notification:', error);
    throw error;
  }
}

/**
 * Classe pour les nouvelles méthodes de notification
 */
export class NotificationService {
  /**
   * Crée une notification pour un changement de statut urgent
   */
  static async createUrgentStatusNotification(
    client: Client,
    step: Step,
    assignees: Assignee[],
    currentProfileId?: string
  ): Promise<void> {
    if (!assignees || assignees.length === 0) return;

    try {
      for (const assignee of assignees) {
        if (currentProfileId) {
          // Créer une notification pour la tâche urgente
          const notificationMessage = `Tâche urgente: ${step.name} pour ${client.name}`;
          await createNotification(assignee.id, step.id, currentProfileId, notificationMessage, true);
        }
      }
    } catch (error) {
      handleError(error, 'NotificationService.createUrgentStatusNotification');
    }
  }

  /**
   * Crée une notification pour un changement de statut validé
   */
  static async createValidatedStatusNotification(
    client: Client,
    step: Step,
    assignees: Assignee[],
    currentProfileId?: string
  ): Promise<void> {
    if (!assignees || assignees.length === 0) return;

    try {
      for (const assignee of assignees) {
        if (currentProfileId) {
          // Créer une notification pour la tâche validée
          const notificationMessage = `Étape validée: ${step.name} pour ${client.name}`;
          await createNotification(assignee.id, step.id, currentProfileId, notificationMessage, true);
        }
      }
    } catch (error) {
      handleError(error, 'NotificationService.createValidatedStatusNotification');
    }
  }

  /**
   * Crée des notifications pour les mentions dans un commentaire
   */
  static async createMentionNotifications(
    mentionIds: string[],
    stepId: string,
    currentProfileName: string,
    currentProfileId?: string
  ): Promise<void> {
    if (!mentionIds || mentionIds.length === 0 || !currentProfileId) return;

    try {
      const baseMessage = `${currentProfileName} vous a mentionné dans un commentaire`;

      // Créer une notification pour chaque mention
      for (const userId of mentionIds) {
        await createNotification(userId, stepId, currentProfileId, baseMessage, false);
      }

      console.log(`✅ ${mentionIds.length} notifications de mention créées`);
    } catch (error) {
      handleError(error, 'NotificationService.createMentionNotifications');
    }
  }

  /**
   * Envoie une notification système (desktop)
   */
  static async sendSystemNotification(
    title: string,
    message: string,
    icon: string = "/logo.png"
  ): Promise<void> {
    try {
      await sendDesktopNotification(title, message, icon);
    } catch (error) {
      console.error('Erreur lors de l\'envoi de la notification système:', error);
      // Ne pas faire échouer l'opération principale si la notification échoue
    }
  }

  /**
   * Supprime les anciennes notifications (plus de 30 jours)
   */
  static async cleanupOldNotifications(): Promise<number> {
    try {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data, error } = await supabase
        .from('notifications')
        .delete()
        .lt('created_at', thirtyDaysAgo.toISOString())
        .select('id');

      if (error) throw error;

      const deletedCount = data?.length || 0;
      console.log(`🧹 ${deletedCount} anciennes notifications supprimées`);

      return deletedCount;
    } catch (error) {
      handleError(error, 'NotificationService.cleanupOldNotifications');
      return 0;
    }
  }
}
