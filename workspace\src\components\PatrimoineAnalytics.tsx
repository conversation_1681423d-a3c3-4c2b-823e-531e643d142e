import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  AreaChart,
  Area
} from 'recharts';
import {
  BarChart3,
  PieChart as PieChartIcon,
  TrendingUp,
  Users,
  Building2,
  Euro,
  Calendar,
  Target,
  Maximize2
} from 'lucide-react';
import { usePatrimoineAnalytics } from '@/hooks/usePatrimoineAnalytics';
import { usePatrimoineEvolution } from '@/hooks/usePatrimoineEvolution';

interface PatrimoineAnalyticsProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PatrimoineAnalytics: React.FC<PatrimoineAnalyticsProps> = ({ isOpen, onClose }) => {
  // État pour le mode plein écran de l'évolution
  const [isEvolutionFullscreen, setIsEvolutionFullscreen] = useState(false);

  const {
    analyticsData,
    loading,
    selectedDate,
    setSelectedDate,
    getDateShortcuts,
    refreshData,
    formatMontant,
    formatPercentage,
    hasData
  } = usePatrimoineAnalytics();

  // Hook pour l'évolution temporelle (remplace le graphique en barres)
  const {
    evolutionData,
    loading: evolutionLoading,
    selectedPeriod,
    setSelectedPeriod,
    periods,
    formatDate,
    hasData: hasEvolutionData
  } = usePatrimoineEvolution();

  // Solution de contournement : utiliser les données du camembert pour créer un point de départ
  const totalPatrimoineFromAnalytics = analyticsData.fournisseurData.reduce((sum, f) => sum + f.montant, 0);



  // Les données sont automatiquement chargées par les hooks
  // Plus besoin de refreshData manuel

  // Tooltip personnalisé pour le camembert
  const CustomPieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800">{data.name}</p>
          <p className="text-green-600">{formatMontant(data.value)}</p>
          <p className="text-blue-600">{formatPercentage(data.percentage)}</p>
        </div>
      );
    }
    return null;
  };

  if (!isOpen) return null;

  return (
    <>
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Analyse Approfondie du Patrimoine
            </DialogTitle>
          </div>
          <DialogDescription>
            Visualisation détaillée de la répartition du patrimoine par fournisseurs et clients avec métriques avancées.
          </DialogDescription>
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <Badge variant="secondary" className="text-sm px-3 py-1 bg-blue-100 text-blue-800">
                {analyticsData.temporal.totalEntries} entrées analysées
              </Badge>
            </div>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Chargement des analyses...</p>
            </div>
          </div>
        ) : !hasData ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <BarChart3 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-600 mb-2">Aucune donnée à analyser</p>
              <p className="text-gray-500">Ajoutez des entrées de patrimoine pour voir les analyses</p>
            </div>
          </div>
        ) : (
          <div className="space-y-6 overflow-y-auto max-h-[80vh] pr-2">
            {/* Filtres temporels */}
            {analyticsData.temporal.hasTemporalData && (
              <Card className="border-blue-200 bg-blue-50/30">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    Période d'analyse
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex flex-wrap gap-2">
                    {getDateShortcuts().map((shortcut) => (
                      <Button
                        key={shortcut.label}
                        variant={selectedDate === shortcut.value ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedDate(shortcut.value)}
                        className={selectedDate === shortcut.value ? "bg-blue-600 hover:bg-blue-700" : ""}
                      >
                        {shortcut.label}
                      </Button>
                    ))}
                  </div>
                  {analyticsData.temporal.oldestEntry && analyticsData.temporal.newestEntry && (
                    <p className="text-xs text-gray-600 mt-2">
                      Données disponibles du {analyticsData.temporal.oldestEntry.toLocaleDateString('fr-FR')} 
                      au {analyticsData.temporal.newestEntry.toLocaleDateString('fr-FR')}
                    </p>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Métriques clés */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="border-green-200 bg-green-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Euro className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Total Patrimoine</p>
                      <p className="text-lg font-bold text-green-700">
                        {formatMontant(analyticsData.metrics.totalPatrimoine)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-blue-200 bg-blue-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Clients Actifs</p>
                      <p className="text-lg font-bold text-blue-700">
                        {analyticsData.metrics.nombreClients}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-purple-200 bg-purple-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Building2 className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Fournisseurs</p>
                      <p className="text-lg font-bold text-purple-700">
                        {analyticsData.metrics.nombreFournisseurs}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-orange-200 bg-orange-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Target className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Montant Moyen</p>
                      <p className="text-lg font-bold text-orange-700">
                        {formatMontant(analyticsData.metrics.montantMoyen)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Graphiques principaux */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Répartition par fournisseur */}
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <PieChartIcon className="h-5 w-5 text-blue-600" />
                    Répartition par Fournisseur
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={analyticsData.fournisseurData}
                          cx="50%"
                          cy="45%"
                          outerRadius={85}
                          innerRadius={30}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percentage, index }) => {
                            if (percentage >= 1) {
                              return `${name} (${formatPercentage(percentage)})`;
                            }
                            return '';
                          }}
                          labelLine={true}
                        >
                          {analyticsData.fournisseurData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip content={<CustomPieTooltip />} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Liste déroulante pour les petits segments */}
                  {analyticsData.fournisseurData.filter(item => item.percentage < 1).length > 0 && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <details className="cursor-pointer">
                        <summary className="text-sm font-medium text-gray-700 hover:text-gray-900">
                          Autres fournisseurs ({analyticsData.fournisseurData.filter(item => item.percentage < 1).length})
                        </summary>
                        <div className="mt-2 space-y-1">
                          {analyticsData.fournisseurData
                            .filter(item => item.percentage < 1)
                            .map((item, index) => (
                              <div key={`small-${index}`} className="flex items-center justify-between text-xs">
                                <div className="flex items-center gap-2">
                                  <div
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: item.color }}
                                  ></div>
                                  <span>{item.name}</span>
                                </div>
                                <span className="text-gray-600">{formatPercentage(item.percentage)}</span>
                              </div>
                            ))}
                        </div>
                      </details>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Évolution temporelle du patrimoine */}
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center justify-between text-lg">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-green-600" />
                      Évolution Temporelle du Patrimoine
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-green-200 text-green-700 hover:bg-green-50"
                      onClick={() => setIsEvolutionFullscreen(true)}
                    >
                      <Maximize2 className="h-4 w-4 mr-2" />
                      Agrandir
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {/* Contrôles de période */}
                  <div className="mb-4">
                    <div className="flex flex-wrap gap-2">
                      {periods.map((period) => (
                        <Button
                          key={period.value}
                          variant={selectedPeriod === period.value ? "default" : "outline"}
                          size="sm"
                          onClick={() => setSelectedPeriod(period.value)}
                          className={selectedPeriod === period.value ? "bg-green-600 hover:bg-green-700" : ""}
                        >
                          {period.label}
                        </Button>
                      ))}
                    </div>
                  </div>

                  {evolutionLoading ? (
                    <div className="h-80 flex items-center justify-center">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto mb-4"></div>
                        <p className="text-gray-500">Calcul de l'évolution...</p>
                      </div>
                    </div>
                  ) : !hasEvolutionData && !evolutionLoading && totalPatrimoineFromAnalytics === 0 ? (
                    <div className="h-80 flex items-center justify-center text-gray-500">
                      <div className="text-center">
                        <TrendingUp className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                        <p className="text-lg font-medium mb-2">Aucune donnée d'évolution</p>
                        <p className="text-sm">Ajoutez des entrées patrimoine pour voir l'évolution</p>
                      </div>
                    </div>
                  ) : !hasEvolutionData && totalPatrimoineFromAnalytics > 0 ? (
                    <div className="space-y-4">
                      {/* Message pour point de départ */}
                      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                        <div className="flex items-center gap-2 text-green-700">
                          <TrendingUp className="h-4 w-4" />
                          <span className="text-sm font-medium">
                            Point de départ établi • La courbe évoluera automatiquement dans le temps
                          </span>
                        </div>
                      </div>

                      {/* Graphique simple avec le point actuel */}
                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <AreaChart
                            data={[{
                              date: new Date().toISOString().split('T')[0],
                              totalPatrimoine: totalPatrimoineFromAnalytics
                            }]}
                            margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                          >
                            <defs>
                              <linearGradient id="patrimoineGradientAnalytics" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                                <stop offset="95%" stopColor="#10B981" stopOpacity={0}/>
                              </linearGradient>
                            </defs>
                            <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                            <XAxis
                              dataKey="date"
                              tickFormatter={(value) => new Date(value).toLocaleDateString('fr-FR')}
                              tick={{ fontSize: 11 }}
                              axisLine={{ stroke: '#9ca3af' }}
                              tickLine={{ stroke: '#9ca3af' }}
                            />
                            <YAxis
                              tickFormatter={(value) => formatMontant(value)}
                              tick={{ fontSize: 11 }}
                              axisLine={{ stroke: '#9ca3af' }}
                              tickLine={{ stroke: '#9ca3af' }}
                              domain={[0, totalPatrimoineFromAnalytics * 1.1]}
                            />
                            <Tooltip
                              content={({ active, payload, label }) => {
                                if (active && payload && payload.length) {
                                  return (
                                    <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
                                      <p className="font-semibold text-gray-800 mb-2">
                                        {new Date(label).toLocaleDateString('fr-FR')}
                                      </p>
                                      <p className="text-green-600 font-medium">
                                        Total: {formatMontant(payload[0].value as number)}
                                      </p>
                                      <p className="text-blue-600 text-sm">Point de départ</p>
                                    </div>
                                  );
                                }
                                return null;
                              }}
                            />
                            <Area
                              type="monotone"
                              dataKey="totalPatrimoine"
                              stroke="#10B981"
                              strokeWidth={3}
                              fill="url(#patrimoineGradientAnalytics)"
                              dot={{ fill: '#10B981', strokeWidth: 2, r: 6 }}
                              activeDot={{ r: 8, stroke: '#10B981', strokeWidth: 2, fill: '#ffffff' }}
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {/* Message informatif pour l'évolution */}
                      {evolutionData.timeline.length <= 2 && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
                          <div className="flex items-center gap-2 text-green-700">
                            <TrendingUp className="h-4 w-4" />
                            <span className="text-sm font-medium">
                              {evolutionData.timeline.length === 1
                                ? "Point de départ établi • La courbe évoluera automatiquement dans le temps"
                                : "Évolution en cours • La courbe s'enrichit automatiquement avec chaque modification"
                              }
                            </span>
                          </div>
                        </div>
                      )}

                      <div className="h-80">
                        <ResponsiveContainer width="100%" height="100%">
                          <AreaChart data={evolutionData.timeline}>
                            <defs>
                              <linearGradient id="patrimoineGradientAnalytics" x1="0" y1="0" x2="0" y2="1">
                                <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                                <stop offset="95%" stopColor="#10B981" stopOpacity={0}/>
                              </linearGradient>
                            </defs>
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis
                              dataKey="date"
                              tickFormatter={formatDate}
                              tick={{ fontSize: 11 }}
                            />
                            <YAxis
                              tickFormatter={(value) => formatMontant(value)}
                              tick={{ fontSize: 11 }}
                            />
                            <Tooltip
                              content={({ active, payload, label }) => {
                                if (active && payload && payload.length) {
                                  const data = payload[0].payload;
                                  return (
                                    <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
                                      <p className="font-semibold text-gray-800 mb-2">{formatDate(label)}</p>
                                      <div className="space-y-1">
                                        <p className="text-green-600 font-medium">
                                          Total: {formatMontant(data.totalPatrimoine)}
                                        </p>
                                        <p className="text-blue-600 text-sm">
                                          {data.nombreEntries} entrées • {data.nombreClients} clients
                                        </p>
                                        <p className="text-purple-600 text-sm">
                                          {data.nombreFournisseurs} fournisseurs actifs
                                        </p>
                                      </div>
                                    </div>
                                  );
                                }
                                return null;
                              }}
                            />
                            <Area
                              type="monotone"
                              dataKey="totalPatrimoine"
                              stroke="#10B981"
                              strokeWidth={3}
                              fill="url(#patrimoineGradientAnalytics)"
                            />
                          </AreaChart>
                        </ResponsiveContainer>
                      </div>
                    </div>
                  )}

                  {/* Métriques d'évolution */}
                  {(hasEvolutionData || evolutionData.timeline.length > 0) && (
                    <div className="mt-4 grid grid-cols-2 gap-4">
                      <div className="bg-green-50 p-3 rounded-lg">
                        <p className="text-xs text-gray-600 font-medium">
                          {evolutionData.timeline.length === 1 ? 'Patrimoine Actuel' : 'Croissance Totale'}
                        </p>
                        <p className={`text-lg font-bold ${evolutionData.timeline.length === 1 ? 'text-green-700' : evolutionData.totalGrowth >= 0 ? 'text-green-700' : 'text-red-700'}`}>
                          {evolutionData.timeline.length === 1
                            ? formatMontant(evolutionData.currentTotal)
                            : formatPercentage(evolutionData.totalGrowth)
                          }
                        </p>
                      </div>
                      <div className="bg-blue-50 p-3 rounded-lg">
                        <p className="text-xs text-gray-600 font-medium">
                          {evolutionData.timeline.length === 1 ? 'Point de Départ' : 'Périodes Analysées'}
                        </p>
                        <p className="text-lg font-bold text-blue-700">
                          {evolutionData.timeline.length === 1 ? 'Établi' : evolutionData.timeline.length}
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* Top performers */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-green-200 bg-green-50/30">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                    Top Fournisseurs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.fournisseurData.slice(0, 5).map((fournisseur, index) => (
                      <div key={`fournisseur-${index}-${fournisseur.name}`} className="flex items-center justify-between p-3 bg-white rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-700 text-sm font-bold">
                            {index + 1}
                          </div>
                          <span className="font-medium">{fournisseur.name}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-green-700">{formatMontant(fournisseur.value)}</p>
                          <p className="text-xs text-gray-500">{formatPercentage(fournisseur.percentage)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-blue-200 bg-blue-50/30">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    Top Clients
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.clientData.slice(0, 5).map((client, index) => (
                      <div key={`client-${index}-${client.name}`} className="flex items-center justify-between p-3 bg-white rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-700 text-sm font-bold">
                            {index + 1}
                          </div>
                          <span className="font-medium">{client.name}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-blue-700">{formatMontant(client.value)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>

    {/* Dialog plein écran séparé pour l'évolution temporelle */}
    {isEvolutionFullscreen && (
      <Dialog open={isEvolutionFullscreen} onOpenChange={setIsEvolutionFullscreen}>
        <DialogContent className="max-w-[95vw] max-h-[95vh] w-[95vw] h-[95vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <TrendingUp className="h-6 w-6 text-green-600" />
              Évolution Temporelle du Patrimoine - Vue Étendue
            </DialogTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsEvolutionFullscreen(false)}
              className="h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="flex-1 space-y-4 overflow-hidden">
          {/* Contrôles de période */}
          <div className="flex flex-wrap gap-2">
            {periods.map((period) => (
              <Button
                key={period.value}
                variant={selectedPeriod === period.value ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedPeriod(period.value)}
                className={selectedPeriod === period.value ? "bg-green-600 hover:bg-green-700" : ""}
              >
                {period.label}
              </Button>
            ))}
          </div>

          {/* Graphique plein écran */}
          <div className="flex-1 h-[calc(95vh-200px)]">
            {!hasEvolutionData && totalPatrimoineFromAnalytics > 0 ? (
              <div className="space-y-4 h-full">
                {/* Message pour point de départ */}
                <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                  <div className="flex items-center gap-2 text-green-700">
                    <TrendingUp className="h-5 w-5" />
                    <span className="font-medium">
                      Point de départ établi • La courbe évoluera automatiquement dans le temps
                    </span>
                  </div>
                </div>

                {/* Graphique plein écran */}
                <div className="h-[calc(100%-80px)]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart
                      data={[{
                        date: new Date().toISOString().split('T')[0],
                        totalPatrimoine: totalPatrimoineFromAnalytics
                      }]}
                      margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                    >
                      <defs>
                        <linearGradient id="patrimoineGradientFullscreen" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                          <stop offset="95%" stopColor="#10B981" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                      <XAxis
                        dataKey="date"
                        tickFormatter={(value) => new Date(value).toLocaleDateString('fr-FR')}
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#9ca3af' }}
                        tickLine={{ stroke: '#9ca3af' }}
                      />
                      <YAxis
                        tickFormatter={(value) => formatMontant(value)}
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#9ca3af' }}
                        tickLine={{ stroke: '#9ca3af' }}
                        domain={[0, totalPatrimoineFromAnalytics * 1.1]}
                      />
                      <Tooltip
                        content={({ active, payload, label }) => {
                          if (active && payload && payload.length) {
                            return (
                              <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
                                <p className="font-semibold text-gray-800 mb-2 text-lg">
                                  {new Date(label).toLocaleDateString('fr-FR')}
                                </p>
                                <p className="text-green-600 font-medium text-lg">
                                  Total: {formatMontant(payload[0].value as number)}
                                </p>
                                <p className="text-blue-600">Point de départ</p>
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="totalPatrimoine"
                        stroke="#10B981"
                        strokeWidth={4}
                        fill="url(#patrimoineGradientFullscreen)"
                        dot={{ fill: '#10B981', strokeWidth: 3, r: 8 }}
                        activeDot={{ r: 12, stroke: '#10B981', strokeWidth: 3, fill: '#ffffff' }}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </div>
            ) : hasEvolutionData ? (
              <div className="space-y-4 h-full">
                {/* Message informatif */}
                {evolutionData.timeline.length <= 2 && (
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 text-green-700">
                      <TrendingUp className="h-5 w-5" />
                      <span className="font-medium">
                        {evolutionData.timeline.length === 1
                          ? "Point de départ établi • La courbe évoluera automatiquement dans le temps"
                          : "Évolution en cours • La courbe s'enrichit automatiquement avec chaque modification"
                        }
                      </span>
                    </div>
                  </div>
                )}

                {/* Graphique d'évolution plein écran */}
                <div className="h-[calc(100%-80px)]">
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={evolutionData.timeline} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
                      <defs>
                        <linearGradient id="patrimoineGradientFullscreen" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="5%" stopColor="#10B981" stopOpacity={0.3}/>
                          <stop offset="95%" stopColor="#10B981" stopOpacity={0}/>
                        </linearGradient>
                      </defs>
                      <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
                      <XAxis
                        dataKey="date"
                        tickFormatter={formatDate}
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#9ca3af' }}
                        tickLine={{ stroke: '#9ca3af' }}
                      />
                      <YAxis
                        tickFormatter={(value) => formatMontant(value)}
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#9ca3af' }}
                        tickLine={{ stroke: '#9ca3af' }}
                      />
                      <Tooltip
                        content={({ active, payload, label }) => {
                          if (active && payload && payload.length) {
                            const data = payload[0].payload;
                            return (
                              <div className="bg-white p-4 border border-gray-200 rounded-lg shadow-lg">
                                <p className="font-semibold text-gray-800 mb-3 text-lg">{formatDate(label)}</p>
                                <div className="space-y-2">
                                  <p className="text-green-600 font-medium text-lg">
                                    Total: {formatMontant(data.totalPatrimoine)}
                                  </p>
                                  <p className="text-blue-600">
                                    {data.nombreEntries} entrées • {data.nombreClients} clients
                                  </p>
                                  <p className="text-purple-600">
                                    {data.nombreFournisseurs} fournisseurs actifs
                                  </p>
                                </div>
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <Area
                        type="monotone"
                        dataKey="totalPatrimoine"
                        stroke="#10B981"
                        strokeWidth={4}
                        fill="url(#patrimoineGradientFullscreen)"
                        dot={{ fill: '#10B981', strokeWidth: 2, r: 6 }}
                        activeDot={{ r: 10, stroke: '#10B981', strokeWidth: 2, fill: '#ffffff' }}
                      />
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
              </div>
            ) : (
              <div className="h-full flex items-center justify-center text-gray-500">
                <div className="text-center">
                  <TrendingUp className="h-16 w-16 mx-auto mb-4 text-gray-300" />
                  <p className="text-xl font-medium mb-2">Aucune donnée d'évolution</p>
                  <p>Ajoutez des entrées patrimoine pour voir l'évolution</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
      </Dialog>
    )}
    </>
  );
};
