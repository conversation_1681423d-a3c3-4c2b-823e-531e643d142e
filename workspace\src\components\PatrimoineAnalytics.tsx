import React, { useEffect, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend
} from 'recharts';
import {
  Bar<PERSON>hart3,
  Pie<PERSON>hart as PieChartIcon,
  TrendingUp,
  Users,
  Building2,
  Euro,
  Calendar,
  Target
} from 'lucide-react';
import { usePatrimoineAnalytics } from '@/hooks/usePatrimoineAnalytics';
import { PatrimoineEvolutionChart } from './PatrimoineEvolutionChart';

interface PatrimoineAnalyticsProps {
  isOpen: boolean;
  onClose: () => void;
}

export const PatrimoineAnalytics: React.FC<PatrimoineAnalyticsProps> = ({ isOpen, onClose }) => {
  const {
    analyticsData,
    loading,
    selectedDate,
    setSelectedDate,
    getDateShortcuts,
    refreshData,
    formatMontant,
    formatPercentage,
    hasData
  } = usePatrimoineAnalytics();





  // Les données sont automatiquement chargées par les hooks
  // Plus besoin de refreshData manuel

  // Tooltip personnalisé pour le camembert
  const CustomPieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-semibold text-gray-800">{data.name}</p>
          <p className="text-green-600">{formatMontant(data.value)}</p>
          <p className="text-blue-600">{formatPercentage(data.percentage)}</p>
        </div>
      );
    }
    return null;
  };

  if (!isOpen) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              Analyse Approfondie du Patrimoine
            </DialogTitle>
          </div>
          <DialogDescription>
            Visualisation détaillée de la répartition du patrimoine par fournisseurs et clients avec métriques avancées.
          </DialogDescription>
          <div className="flex items-center justify-between mt-2">
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <Badge variant="secondary" className="text-sm px-3 py-1 bg-blue-100 text-blue-800">
                {analyticsData.temporal.totalEntries} entrées analysées
              </Badge>
            </div>
          </div>
        </DialogHeader>

        {loading ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-500">Chargement des analyses...</p>
            </div>
          </div>
        ) : !hasData ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <BarChart3 className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <p className="text-lg font-medium text-gray-600 mb-2">Aucune donnée à analyser</p>
              <p className="text-gray-500">Ajoutez des entrées de patrimoine pour voir les analyses</p>
            </div>
          </div>
        ) : (
          <div className="space-y-6 overflow-y-auto max-h-[80vh] pr-2">
            {/* Filtres temporels */}
            {analyticsData.temporal.hasTemporalData && (
              <Card className="border-blue-200 bg-blue-50/30">
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-blue-600" />
                    Période d'analyse
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="flex flex-wrap gap-2">
                    {getDateShortcuts().map((shortcut) => (
                      <Button
                        key={shortcut.label}
                        variant={selectedDate === shortcut.value ? "default" : "outline"}
                        size="sm"
                        onClick={() => setSelectedDate(shortcut.value)}
                        className={selectedDate === shortcut.value ? "bg-blue-600 hover:bg-blue-700" : ""}
                      >
                        {shortcut.label}
                      </Button>
                    ))}
                  </div>
                  {analyticsData.temporal.oldestEntry && analyticsData.temporal.newestEntry && (
                    <p className="text-xs text-gray-600 mt-2">
                      Données disponibles du {analyticsData.temporal.oldestEntry.toLocaleDateString('fr-FR')} 
                      au {analyticsData.temporal.newestEntry.toLocaleDateString('fr-FR')}
                    </p>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Métriques clés */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card className="border-green-200 bg-green-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Euro className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Total Patrimoine</p>
                      <p className="text-lg font-bold text-green-700">
                        {formatMontant(analyticsData.metrics.totalPatrimoine)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-blue-200 bg-blue-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Users className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Clients Actifs</p>
                      <p className="text-lg font-bold text-blue-700">
                        {analyticsData.metrics.nombreClients}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-purple-200 bg-purple-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Building2 className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Fournisseurs</p>
                      <p className="text-lg font-bold text-purple-700">
                        {analyticsData.metrics.nombreFournisseurs}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="border-orange-200 bg-orange-50/30">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Target className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <p className="text-xs text-gray-600 font-medium">Montant Moyen</p>
                      <p className="text-lg font-bold text-orange-700">
                        {formatMontant(analyticsData.metrics.montantMoyen)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Graphiques principaux */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Répartition par fournisseur */}
              <Card className="border-0 shadow-lg">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <PieChartIcon className="h-5 w-5 text-blue-600" />
                    Répartition par Fournisseur
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-80">
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={analyticsData.fournisseurData}
                          cx="50%"
                          cy="45%"
                          outerRadius={85}
                          innerRadius={30}
                          fill="#8884d8"
                          dataKey="value"
                          label={({ name, percentage, index }) => {
                            if (percentage >= 1) {
                              return `${name} (${formatPercentage(percentage)})`;
                            }
                            return '';
                          }}
                          labelLine={true}
                        >
                          {analyticsData.fournisseurData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip content={<CustomPieTooltip />} />
                      </PieChart>
                    </ResponsiveContainer>
                  </div>

                  {/* Liste déroulante pour les petits segments */}
                  {analyticsData.fournisseurData.filter(item => item.percentage < 1).length > 0 && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <details className="cursor-pointer">
                        <summary className="text-sm font-medium text-gray-700 hover:text-gray-900">
                          Autres fournisseurs ({analyticsData.fournisseurData.filter(item => item.percentage < 1).length})
                        </summary>
                        <div className="mt-2 space-y-1">
                          {analyticsData.fournisseurData
                            .filter(item => item.percentage < 1)
                            .map((item, index) => (
                              <div key={`small-${index}`} className="flex items-center justify-between text-xs">
                                <div className="flex items-center gap-2">
                                  <div
                                    className="w-3 h-3 rounded-full"
                                    style={{ backgroundColor: item.color }}
                                  ></div>
                                  <span>{item.name}</span>
                                </div>
                                <span className="text-gray-600">{formatPercentage(item.percentage)}</span>
                              </div>
                            ))}
                        </div>
                      </details>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Évolution temporelle simplifiée */}
              <PatrimoineEvolutionChart
                fournisseurs={analyticsData.fournisseurData.map(f => ({
                  id: f.id,
                  nom: f.name,
                  total: f.value
                }))}
              />
            </div>

            {/* Top performers */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card className="border-green-200 bg-green-50/30">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                    Top Fournisseurs
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.fournisseurData.slice(0, 5).map((fournisseur, index) => (
                      <div key={`fournisseur-${index}-${fournisseur.name}`} className="flex items-center justify-between p-3 bg-white rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-green-100 text-green-700 text-sm font-bold">
                            {index + 1}
                          </div>
                          <span className="font-medium">{fournisseur.name}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-green-700">{formatMontant(fournisseur.value)}</p>
                          <p className="text-xs text-gray-500">{formatPercentage(fournisseur.percentage)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card className="border-blue-200 bg-blue-50/30">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Users className="h-5 w-5 text-blue-600" />
                    Top Clients
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.clientData.slice(0, 5).map((client, index) => (
                      <div key={`client-${index}-${client.name}`} className="flex items-center justify-between p-3 bg-white rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-6 h-6 rounded-full bg-blue-100 text-blue-700 text-sm font-bold">
                            {index + 1}
                          </div>
                          <span className="font-medium">{client.name}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-blue-700">{formatMontant(client.value)}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
