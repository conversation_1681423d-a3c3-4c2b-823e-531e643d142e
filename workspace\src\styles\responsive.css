/* Styles responsive pour les tableaux et autres éléments */

/* Styles pour les tableaux sur petits écrans */
@media (max-width: 768px) {
  /* Rendre les tableaux scrollables horizontalement */
  .responsive-table-container {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  /* Ajuster la taille des cellules pour les petits écrans */
  .responsive-table th,
  .responsive-table td {
    white-space: nowrap;
    padding: 0.5rem;
  }

  /* Cacher certaines colonnes sur les très petits écrans */
  @media (max-width: 480px) {
    .responsive-table .hide-on-mobile {
      display: none;
    }
  }
}

/* Styles pour les formulaires sur petits écrans */
@media (max-width: 640px) {
  .responsive-form {
    display: flex;
    flex-direction: column;
  }

  .responsive-form-row {
    flex-direction: column;
    gap: 0.5rem;
  }
}

/* Styles pour les cartes sur petits écrans */
@media (max-width: 640px) {
  .responsive-card {
    padding: 0.75rem;
  }
}

/* Styles pour les boutons sur petits écrans */
@media (max-width: 480px) {
  .responsive-button-group {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: 0.5rem;
  }

  .responsive-button-group button {
    width: 100%;
  }
}

/* Styles pour les en-têtes sur petits écrans */
@media (max-width: 640px) {
  .responsive-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .responsive-header-actions {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }
}
