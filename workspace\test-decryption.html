<!DOCTYPE html>
<html>
<head>
    <title>Test de déchiffrement</title>
</head>
<body>
    <h1>Test de déchiffrement WeMa Tracker</h1>
    <div id="results"></div>

    <script>
        // Configuration identique à encryption.ts
        const ENCRYPTION_KEY = "WeMaTracker-2025-RGPD-Protection-Key-v1";
        const PREFIX = "enc:";

        // Fonction de génération de clé
        async function getKey() {
            const encoder = new TextEncoder();
            const keyMaterial = await crypto.subtle.importKey(
                'raw',
                encoder.encode(ENCRYPTION_KEY),
                { name: 'PBKDF2' },
                false,
                ['deriveBits', 'deriveKey']
            );

            return crypto.subtle.deriveKey(
                {
                    name: 'PBKDF2',
                    salt: encoder.encode('WeMaTracker-Salt'),
                    iterations: 100000,
                    hash: 'SHA-256'
                },
                keyMaterial,
                { name: 'AES-GCM', length: 256 },
                false,
                ['encrypt', 'decrypt']
            );
        }

        // Fonction de déchiffrement
        async function decryptText(encryptedText) {
            if (!encryptedText || !encryptedText.startsWith(PREFIX)) {
                throw new Error('Format de données chiffrées invalide');
            }

            try {
                const key = await getKey();
                const base64Data = encryptedText.slice(PREFIX.length);
                
                // Décoder le base64
                const combined = new Uint8Array(
                    atob(base64Data).split('').map(char => char.charCodeAt(0))
                );

                // Extraire IV et données
                const iv = combined.slice(0, 12);
                const data = combined.slice(12);

                // Déchiffrer
                const decrypted = await crypto.subtle.decrypt(
                    { name: 'AES-GCM', iv },
                    key,
                    data
                );

                return new TextDecoder().decode(decrypted);
            } catch (error) {
                console.error('Erreur lors du déchiffrement:', error);
                throw new Error('Échec du déchiffrement');
            }
        }

        // Test avec le nom chiffré du client
        async function testDecryption() {
            const encryptedName = "enc:9U9+hDW3zTdSONiF9nTsDxXyI6oNka0agKfeSJTxK5ap";
            const resultsDiv = document.getElementById('results');
            
            try {
                resultsDiv.innerHTML += `<p><strong>Nom chiffré:</strong> ${encryptedName}</p>`;
                
                const decryptedName = await decryptText(encryptedName);
                
                resultsDiv.innerHTML += `<p><strong>Nom déchiffré:</strong> ${decryptedName}</p>`;
                resultsDiv.innerHTML += `<p style="color: green;"><strong>✅ Déchiffrement réussi !</strong></p>`;
                
            } catch (error) {
                resultsDiv.innerHTML += `<p style="color: red;"><strong>❌ Erreur:</strong> ${error.message}</p>`;
            }
        }

        // Lancer le test
        testDecryption();
    </script>
</body>
</html>
