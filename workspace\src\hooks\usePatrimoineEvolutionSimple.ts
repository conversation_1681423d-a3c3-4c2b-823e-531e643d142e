import { useState, useCallback, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { encryptText, safeDecrypt, isEncrypted } from '@/utils/encryption';
import { useSupabaseSubscription, onInsert, onUpdate, onDelete } from './useSupabaseSubscription';

interface EvolutionSnapshot {
  id: string;
  date: string; // Format YYYY-MM-DD
  total: number;
  devise: string;
  commentaire?: string;
  created_at: string;
  updated_at: string;
}

interface EvolutionData {
  snapshots: EvolutionSnapshot[];
  loading: boolean;
  error: string | null;
}

/**
 * Hook simplifié pour gérer l'évolution temporelle du patrimoine
 * Basé sur des totaux sous gestion à des dates données
 */
export const usePatrimoineEvolutionSimple = () => {
  const [data, setData] = useState<EvolutionData>({
    snapshots: [],
    loading: false,
    error: null
  });

  // Charger tous les snapshots
  const loadSnapshots = useCallback(async () => {
    try {
      setData(prev => ({ ...prev, loading: true, error: null }));

      const { data: snapshotsData, error } = await supabase
        .from('patrimoine_evolution')
        .select('*')
        .order('date_snapshot', { ascending: true });

      if (error) throw error;

      // Déchiffrer les totaux
      const decryptedSnapshots = await Promise.all(
        (snapshotsData || []).map(async (snapshot) => {
          let totalDecrypted = 0;

          if (snapshot.total_sous_gestion) {
            if (isEncrypted(snapshot.total_sous_gestion)) {
              const decrypted = await safeDecrypt(snapshot.total_sous_gestion);
              totalDecrypted = parseFloat(decrypted) || 0;
            } else {
              totalDecrypted = parseFloat(snapshot.total_sous_gestion) || 0;
            }
          }

          return {
            id: snapshot.id,
            date: snapshot.date_snapshot,
            total: totalDecrypted,
            devise: snapshot.devise || 'EUR',
            commentaire: snapshot.commentaire || undefined,
            created_at: snapshot.created_at,
            updated_at: snapshot.updated_at
          };
        })
      );

      setData(prev => ({
        ...prev,
        snapshots: decryptedSnapshots,
        loading: false
      }));

    } catch (error) {
      console.error('Erreur lors du chargement des snapshots:', error);
      setData(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : 'Erreur inconnue'
      }));
    }
  }, []);

  // Créer ou mettre à jour un snapshot
  const upsertSnapshot = useCallback(async (
    date: string,
    total: number,
    commentaire?: string,
    devise: string = 'EUR'
  ) => {
    try {
      // Chiffrer le total
      const encryptedTotal = await encryptText(total.toString());

      const { data: snapshot, error } = await supabase
        .from('patrimoine_evolution')
        .upsert({
          date_snapshot: date,
          total_sous_gestion: encryptedTotal,
          devise,
          commentaire: commentaire || null
        }, {
          onConflict: 'date_snapshot'
        })
        .select()
        .single();

      if (error) throw error;

      // Mettre à jour localement
      const newSnapshot: EvolutionSnapshot = {
        id: snapshot.id,
        date: snapshot.date_snapshot,
        total: total,
        devise: snapshot.devise,
        commentaire: snapshot.commentaire || undefined,
        created_at: snapshot.created_at,
        updated_at: snapshot.updated_at
      };

      setData(prev => {
        const existingIndex = prev.snapshots.findIndex(s => s.date === date);
        if (existingIndex >= 0) {
          // Mise à jour
          const updatedSnapshots = [...prev.snapshots];
          updatedSnapshots[existingIndex] = newSnapshot;
          return {
            ...prev,
            snapshots: updatedSnapshots.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
          };
        } else {
          // Création
          return {
            ...prev,
            snapshots: [...prev.snapshots, newSnapshot].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())
          };
        }
      });

      toast.success('Snapshot sauvegardé');
      return true;

    } catch (error) {
      console.error('Erreur lors de la sauvegarde du snapshot:', error);
      toast.error('Erreur lors de la sauvegarde');
      return false;
    }
  }, []);

  // Supprimer un snapshot
  const deleteSnapshot = useCallback(async (id: string) => {
    try {
      const { error } = await supabase
        .from('patrimoine_evolution')
        .delete()
        .eq('id', id);

      if (error) throw error;

      setData(prev => ({
        ...prev,
        snapshots: prev.snapshots.filter(s => s.id !== id)
      }));

      toast.success('Snapshot supprimé');
      return true;

    } catch (error) {
      console.error('Erreur lors de la suppression du snapshot:', error);
      toast.error('Erreur lors de la suppression');
      return false;
    }
  }, []);

  // Calculer les métriques d'évolution
  const getEvolutionMetrics = useCallback(() => {
    if (data.snapshots.length < 2) {
      return {
        totalGrowth: 0,
        averageGrowth: 0,
        bestSnapshot: data.snapshots[0] || null,
        worstSnapshot: data.snapshots[0] || null,
        currentTotal: data.snapshots[0]?.total || 0,
        previousTotal: 0
      };
    }

    const sortedSnapshots = [...data.snapshots].sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
    const firstSnapshot = sortedSnapshots[0];
    const lastSnapshot = sortedSnapshots[sortedSnapshots.length - 1];
    
    const totalGrowth = firstSnapshot.total > 0 
      ? ((lastSnapshot.total - firstSnapshot.total) / firstSnapshot.total) * 100 
      : 0;

    const averageGrowth = sortedSnapshots.length > 1 
      ? totalGrowth / (sortedSnapshots.length - 1) 
      : 0;

    const bestSnapshot = sortedSnapshots.reduce((best, current) => 
      current.total > best.total ? current : best, sortedSnapshots[0]);
    
    const worstSnapshot = sortedSnapshots.reduce((worst, current) => 
      current.total < worst.total ? current : worst, sortedSnapshots[0]);

    return {
      totalGrowth,
      averageGrowth,
      bestSnapshot,
      worstSnapshot,
      currentTotal: lastSnapshot.total,
      previousTotal: sortedSnapshots.length > 1 ? sortedSnapshots[sortedSnapshots.length - 2].total : 0
    };
  }, [data.snapshots]);

  // Formater les montants
  const formatMontant = useCallback((montant: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(montant);
  }, []);

  // Formater les pourcentages
  const formatPercentage = useCallback((percentage: number) => {
    const sign = percentage >= 0 ? '+' : '';
    return `${sign}${percentage.toFixed(1)}%`;
  }, []);

  // Charger les données au montage
  useEffect(() => {
    loadSnapshots();
  }, [loadSnapshots]);

  // Temps réel : recharger sur changements
  const handleRealtimeUpdate = useCallback(() => {
    console.log('🔄 Évolution: Changement détecté, rechargement...');
    loadSnapshots();
  }, [loadSnapshots]);

  useSupabaseSubscription(
    'patrimoine-evolution-realtime',
    [
      onInsert('patrimoine_evolution', handleRealtimeUpdate),
      onUpdate('patrimoine_evolution', handleRealtimeUpdate),
      onDelete('patrimoine_evolution', handleRealtimeUpdate)
    ]
  );

  return {
    // Données
    snapshots: data.snapshots,
    loading: data.loading,
    error: data.error,

    // Actions
    loadSnapshots,
    upsertSnapshot,
    deleteSnapshot,

    // Calculs
    getEvolutionMetrics,
    formatMontant,
    formatPercentage,

    // État
    hasData: data.snapshots.length > 0
  };
};
