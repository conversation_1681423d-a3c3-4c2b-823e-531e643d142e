/**
 * Utilitaires pour interagir avec la barre des tâches
 */

/**
 * Met à jour le badge de l'icône de l'application dans la barre des tâches
 * @param count Nombre à afficher sur le badge (0 pour masquer le badge)
 */
export async function updateTaskbarBadge(count: number): Promise<void> {
  try {
    // Essayer d'utiliser l'API Tauri pour mettre à jour le badge
    try {
      // Import dynamique pour éviter les erreurs de compilation
      const tauriWindow = await import('@tauri-apps/api/window');

      // Vérifier si l'API est disponible
      if (tauriWindow && tauriWindow.appWindow) {
        try {
          // Essayer de mettre à jour le titre de la fenêtre
          if (count > 0) {
            // Définir le badge avec le nombre
            await tauriWindow.appWindow.setTitle(`WeMa (${count})`);
          } else {
            // Réinitialiser le titre
            await tauriWindow.appWindow.setTitle('WeMa');
          }

          console.log(`Badge de la barre des tâches mis à jour: ${count}`);
        } catch (setTitleError) {
          console.warn('Erreur lors de la mise à jour du titre:', setTitleError);

          // Essayer une autre approche si setTitle ne fonctionne pas
          try {
            // Utiliser document.title comme solution de secours
            document.title = count > 0 ? `WeMa (${count})` : 'WeMa';
            console.log(`Titre du document mis à jour: ${document.title}`);
          } catch (docTitleError) {
            console.warn('Erreur lors de la mise à jour du titre du document:', docTitleError);
          }
        }
      } else {
        // Utiliser document.title comme solution de secours
        document.title = count > 0 ? `WeMa (${count})` : 'WeMa';
        console.log(`Titre du document mis à jour: ${document.title}`);
      }
    } catch (tauriError) {
      console.warn('Impossible de charger l\'API Tauri Window:', tauriError);

      // Utiliser document.title comme solution de secours
      document.title = count > 0 ? `WeMa (${count})` : 'WeMa';
      console.log(`Titre du document mis à jour: ${document.title}`);
    }
  } catch (error) {
    console.error('Erreur lors de la mise à jour du badge de la barre des tâches:', error);
  }
}
